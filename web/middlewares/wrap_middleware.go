package middlewares

import (
	"bytes"
	"io"
	"net/http"
	"runtime/debug"
	"time"
	"trading_tick_server/lib/logger"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func WrapWithAOP() gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()
		start := time.Now()
		// 捕获panic
		defer func() {
			if r := recover(); r != nil {
				logger.InfoCtx(ctx, "[AOP Panic]",
					zap.Any("error", r),
					zap.ByteString("stack", debugStack()))
				c.JSO<PERSON>(http.StatusOK, gin.H{
					"code":    500,
					"message": "系统异常",
					"data":    nil,
				})
			}
		}()
		// ===== 前置逻辑 =====
		dumpRequest(c)
		// 执行控制器方法
		c.Next()
		// ===== 后置逻辑 =====
		logger.InfoCtx(ctx, "[AOP After]",
			zap.String("path", c.Request.URL.Path),
			zap.Duration("cost", time.Since(start).Round(time.Millisecond)))
	}
}

func dumpRequest(c *gin.Context) {
	ctx := c.Request.Context()
	query := c.Request.URL.RawQuery
	logger.InfoCtx(ctx, "[AOP Before] 请求 query 参数",
		zap.String("path", c.Request.URL.Path),
		zap.String("query", query))

	// 读取 Body 内容（JSON）
	if c.Request.Method == http.MethodPost || c.Request.Method == http.MethodPut {
		body, err := io.ReadAll(c.Request.Body)
		if err == nil {
			// 打印
			logger.InfoCtx(ctx, "[AOP Before] 请求 JSON 参数",
				zap.ByteString("json", body))
			// 还原 Body，Gin 会再次读取
			c.Request.Body = io.NopCloser(bytes.NewBuffer(body))
		}
	}
}

func debugStack() []byte {
	return debug.Stack()
}
