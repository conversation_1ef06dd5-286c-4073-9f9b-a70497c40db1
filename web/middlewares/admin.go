package middlewares

import (
	"net/http"
	"strings"
	"trading_tick_server/lib/cache"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/logger"
	service_common "trading_tick_server/web/service/common"

	"github.com/gin-gonic/gin"
)

func AdminAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Request.URL.Path
		// 检测鉴权
		authorization := c.GetHeader("Authorization")
		if authorization == "" {
			c.JSON(http.StatusOK, service_common.NeedLogin())
			c.Abort()
			return
		}
		tokenString := strings.Split(authorization, " ")
		if tokenString[0] != "Bearer" || tokenString[1] == "" {
			c.J<PERSON>(http.StatusOK, service_common.NeedLogin())
			c.Abort()
			return
		}
		user, err := cache.SysUserGet(tokenString[1])
		if err != nil || tokenString[1] != user.Token {
			c.<PERSON>(http.StatusOK, service_common.NeedLogin())
			c.Abort()
			return
		}

		// 检测是否需要进行google totp验证
		if global.Yaml.WebServer.GoogleTotpHeartbeat && !strings.Contains(path, "verify_google_totp") &&
			!strings.Contains(path, "build_google_totp") && !strings.Contains(path, "logout") {
			if !cache.GoogleTotpHeartbeatGet(tokenString[1]) {
				c.JSON(http.StatusOK, service_common.GoogleTotpVerifyError())
				c.Abort()
				return
			} else {
				service_common.SetHeartbeat(tokenString[1])
			}
		}

		// 检测权限
		pathMethod := strings.Replace(path, "/admin/", "", 1)
		pathMethod = strings.Replace(pathMethod, "/", ":", -1) + ":" + strings.ToLower(c.Request.Method)
		if !cache.SystemUserAuth(user.ID, pathMethod) && user.ID > 100000 {
			c.JSON(http.StatusOK, service_common.SystemUserAuthError())
			c.Abort()
			return
		}

		c.Set("user", user)
		c.Set("path", path)
		c.Set("ip", c.ClientIP())
		c.Next()
	}
}

func AdminLog() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 生成 traceId 并注入上下文
		traceId := logger.GenerateTraceID()
		ctx := logger.InjectTraceID(c.Request.Context(), traceId)
		c.Request = c.Request.WithContext(ctx)
		c.Next()
	}
}
