package middlewares

import (
	"bytes"
	"io"
	"strings"
	"time"
	"trading_tick_server/lib/global"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func Cors() gin.HandlerFunc {
	config := cors.DefaultConfig()
	config.AllowMethods = []string{"GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"}
	config.AllowHeaders = []string{"Origin", "Content-Length", "Content-Type", "Authorization", "Operation"}
	config.AllowAllOrigins = true
	config.AllowCredentials = true
	return cors.New(config)
}

func Logger() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		query := c.Request.URL.RawQuery

		// 检查是否为 multipart/form-data 类型
		isMultipart := strings.Contains(c.Request.Header.Get("Content-Type"), "multipart/form-data;")
		var body []byte

		if !isMultipart {
			var err error
			body, err = c.GetRawData()
			if err != nil {
				global.Lg.Error("[Http] Get raw data failed", zap.Error(err))
			}
		} else {
			body = []byte("Boundary")
		}

		// 使用 defer 计算请求处理时间
		if global.Yaml.WebServer.LogLevel == "debug" {
			defer func() {
				cost := time.Since(start)
				global.Lg.Info(path,
					zap.Int("status", c.Writer.Status()),
					zap.String("method", c.Request.Method),
					zap.String("path", path),
					zap.String("query", query),
					zap.String("body", string(body)),
					zap.String("ip", c.ClientIP()),
					zap.String("user-agent", c.Request.UserAgent()),
					zap.String("errors", c.Errors.ByType(gin.ErrorTypePrivate).String()),
					zap.Duration("cost", cost),
				)
			}()
		}

		// 如果请求体已读取，重新设置 body，以便后续中间件可以再次读取
		if !isMultipart {
			c.Request.Body = io.NopCloser(bytes.NewBuffer(body))
		}

		// 继续后续的处理
		c.Next()
	}
}

func Recovery() gin.HandlerFunc {
	return gin.Recovery()
}
