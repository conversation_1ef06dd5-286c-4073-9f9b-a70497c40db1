package contorllers_api

import (
	"net/http"
	service_api "trading_tick_server/web/service/api"
	service_common "trading_tick_server/web/service/common"

	"github.com/gin-gonic/gin"
)

// @Summary k线插针列表
// @Description 无搜索条件。
// @Tags api/k线插针
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  query  service_api.KlineShadowListRequest  true  "参数"
// @Success 200 {object} structs.Response{data=service_api.KlineShadowListResponse} "返回码看对照表"
// @Router /api/kline_shadow [GET]
func GetKlineShadowList(c *gin.Context) {
	var request service_api.KlineShadowListRequest
	if err := c.Bind(&request); err == nil {
		res := request.GetKlineShadowList()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 添加k线插针
// @Description
// @Tags api/k线插针
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  body  service_api.KlineShadowAddRequest  true  "参数"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /api/kline_shadow [POST]
func PostKlineShadowAdd(c *gin.Context) {
	var request service_api.KlineShadowAddRequest
	if err := c.Bind(&request); err == nil {
		res := request.PostKlineShadowAdd()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 删除k线插针
// @Description
// @Tags api/k线插针
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  body  service_api.KlineShadowIDRequest  true  "参数"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /api/kline_shadow [DELETE]
func DelKlineShadowDel(c *gin.Context) {
	var request service_api.KlineShadowIDRequest
	if err := c.Bind(&request); err == nil {
		res := request.DelKlineShadowDel()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 删除k线插针清空
// @Description
// @Tags api/k线插针
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  body  service_api.KlineShadowSymbolRequest  true  "参数"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /api/kline_shadow/fix [DELETE]
func DelKlineShadowFix(c *gin.Context) {
	var request service_api.KlineShadowSymbolRequest
	if err := c.Bind(&request); err == nil {
		res := request.DelKlineShadowFix()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}
