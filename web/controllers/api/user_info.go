package contorllers_api

import (
	"net/http"
	service_api "trading_tick_server/web/service/api"
	service_common "trading_tick_server/web/service/common"

	"github.com/gin-gonic/gin"
)

// @Summary 连接请求
// @Description 无搜索条件。
// @Tags api/用户管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  query  service_api.KconnectionDeliveryRequest  true  "参数"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /api/connection_delivery [GET]
func GetKconnectionDelivery(c *gin.Context) {
	var request service_api.KconnectionDeliveryRequest
	if err := c.Bind(&request); err == nil {
		res := request.GetKconnectionDelivery()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}
