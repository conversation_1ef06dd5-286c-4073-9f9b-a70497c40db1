package contorllers_api

import (
	"fmt"
	"net/http"
	"time"
	"trading_tick_server/lib/logger"
	service_api "trading_tick_server/web/service/api"
	service_common "trading_tick_server/web/service/common"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// @Summary 历史k线
// @Description 无搜索条件。
// @Tags api/历史k线
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  query  service_api.KlineHistoryRequest  true  "参数"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /api/kline_history [GET]
func GetKlineHistory(c *gin.Context) {
	start := time.Now()
	defer func() {
		elapsed := time.Since(start)

		// 这里设置阈值，比如 100ms
		threshold := 40 * time.Millisecond

		if elapsed > threshold {
			logger.ViewCtx(c.Request.Context(), fmt.Sprintf("⚠️[取历史k线超出设定阈值] took %v 毫秒", elapsed.Milliseconds()))
		}
	}()
	var request service_api.KlineHistoryRequest
	if err := c.Bind(&request); err == nil {
		res := request.GetKlineHistory()
		logger.ViewCtx(c.Request.Context(), "✅获取历史K线", zap.String("code", request.Code), zap.Int64("klineTimestampEnd", request.KlineTimestampEnd), zap.Int("count", request.Count), zap.String("resolution", request.Resolution))
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 批量获取历史k线
// @Description 无搜索条件。
// @Tags api/历史k线
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  query  service_api.KlineHistoryRequest  true  "参数"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /api/kline_history [GET]
func GetKlineHistoryBatch(c *gin.Context) {
	start := time.Now()
	defer func() {
		elapsed := time.Since(start)

		// 这里设置阈值，比如 100ms
		threshold := 120 * time.Millisecond

		if elapsed > threshold {
			logger.ViewCtx(c.Request.Context(),
				"⚠️取批量历史k线耗时超过设定",
				zap.Int64("elapsed", elapsed.Milliseconds()),
			)
		}
	}()
	var request service_api.KlineHistoryBatchRequest
	if err := c.Bind(&request); err == nil {
		res := request.GetKlineHistoryBatch()
		logger.ViewCtx(c.Request.Context(), "✅批量获取历史K线", zap.String("codes", request.Codes), zap.Int64("klineTimestampEnd", request.KlineTimestampEnd), zap.Int("count", request.Count), zap.String("resolution", request.Resolution))
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}
