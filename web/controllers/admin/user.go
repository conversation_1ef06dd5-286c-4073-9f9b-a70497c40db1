package contorllers_admin

import (
	"net/http"
	service_admin "trading_tick_server/web/service/admin"
	service_common "trading_tick_server/web/service/common"

	"github.com/gin-gonic/gin"
)

// @Summary 用户列表
// @Description 无搜索条件。
// @Tags 后台/用户管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  query  service_admin.UserListRequest  true  "参数"
// @Success 200 {object} structs.Response{data=service_admin.UserListResponse} "返回码看对照表"
// @Router /admin/user/user [GET]
func GetUserList(c *gin.Context) {
	var request service_admin.UserListRequest
	if err := c.Bind(&request); err == nil {
		res := request.GetUserList()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 用户详情
// @Description 无搜索条件。
// @Tags 后台/用户管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  query  service_admin.UserViewRequest  true  "参数"
// @Success 200 {object} structs.Response{data=service_admin.UserViewResponse} "返回码看对照表"
// @Router /admin/user/view [GET]
func GetUserView(c *gin.Context) {
	var request service_admin.UserViewRequest
	if err := c.Bind(&request); err == nil {
		res := request.GetUserView()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 添加用户
// @Description
// @Tags 后台/用户管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  body  service_admin.UserAddRequest  true  "参数"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /admin/User/User [POST]
func PostUserAdd(c *gin.Context) {
	var request service_admin.UserAddRequest
	if err := c.Bind(&request); err == nil {
		res := request.PostUserAdd()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 删除用户
// @Description
// @Tags 后台/用户管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  body  service_admin.UserIDRequest  true  "参数"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /admin/User/User [DELETE]
func DelUserDel(c *gin.Context) {
	var request service_admin.UserIDRequest
	if err := c.Bind(&request); err == nil {
		res := request.DelUserDel()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 获取用户Symbol列表
// @Description 根据用户ID获取用户Symbol列表，支持分页和筛选
// @Tags 后台/用户管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  query  service_admin.UserSymbolListRequest  true  "参数"
// @Success 200 {object} structs.Response{data=service_admin.UserSymbolListResponse} "返回码看对照表"
// @Router /admin/user/symbol_list [GET]
func GetUserSymbolList(c *gin.Context) {
	var request service_admin.UserSymbolListRequest
	if err := c.Bind(&request); err == nil {
		res := request.GetUserSymbolList()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 编辑用户Symbol
// @Description 编辑用户Symbol的DecimalPlaces和FloatRange字段
// @Tags 后台/用户管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  body  service_admin.UserSymbolEditRequest  true  "参数"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /admin/user/symbol_edit [PUT]
func PutUserSymbolEdit(c *gin.Context) {
	var request service_admin.UserSymbolEditRequest
	if err := c.Bind(&request); err == nil {
		res := request.EditUserSymbol()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}
