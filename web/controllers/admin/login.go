package contorllers_admin

import (
	"net/http"
	"trading_tick_server/lib/structs"
	service_admin "trading_tick_server/web/service/admin"
	service_common "trading_tick_server/web/service/common"

	"github.com/gin-gonic/gin"
)

// @Summary 登录
// @Description 使用邮箱、用户名和密码登录。账号 (username) 和邮箱 (email) 必须至少填写其中一个。
// @Tags 后台/个人操作
// @Accept  json
// @Produce  json
// @Param   request  body  service_admin.LoginRequest  true  "登录请求"
// @Success 200 {object} structs.Response{data=service_admin.LoginResponse} "返回码看对照表"
// @Router /admin/login [POST]
func PostLogin(c *gin.Context) {
	var request service_admin.LoginRequest
	if err := c.Bind(&request); err == nil {
		res := request.PostLogin(c.ClientIP())
		c.JSON(http.StatusOK, res)
	} else {
		c.JSO<PERSON>(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 退出登录
// @Description
// @Tags 后台/个人操作
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /admin/logout [GET]
func GetLogout(c *gin.Context) {
	var request service_admin.LogoutRequest
	user, ok := c.Get("user")
	if !ok {
		c.JSON(http.StatusOK, service_common.AdminUserDataError())
	}
	if err := c.Bind(&request); err == nil {
		res := request.GetLogout(user.(structs.SysUser))
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}
