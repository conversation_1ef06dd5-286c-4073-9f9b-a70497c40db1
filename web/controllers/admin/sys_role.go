package contorllers_admin

import (
	"net/http"
	service_admin "trading_tick_server/web/service/admin"
	service_common "trading_tick_server/web/service/common"

	"github.com/gin-gonic/gin"
)

// @Summary 角色列表
// @Description 无搜索条件。
// @Tags 后台/角色管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  query  service_admin.SysRoleListRequest  true  "参数"
// @Success 200 {object} structs.Response{data=service_admin.SysRoleListResponse} "返回码看对照表"
// @Router /admin/system/role [GET]
func GetSysRoleList(c *gin.Context) {
	var request service_admin.SysRoleListRequest
	if err := c.Bind(&request); err == nil {
		res := request.GetSysRoleList()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 添加角色
// @Description
// @Tags 后台/角色管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  body  service_admin.SysRoleAddRequest  true  "参数"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /admin/system/role [POST]
func PostSysRoleAdd(c *gin.Context) {
	var request service_admin.SysRoleAddRequest
	if err := c.Bind(&request); err == nil {
		res := request.PostSysRoleAdd()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 修改角色
// @Description
// @Tags 后台/角色管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  body  service_admin.SysRoleUpateRequest  true  "参数"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /admin/system/role [PUT]
func PutSysRoleUpdate(c *gin.Context) {
	var request service_admin.SysRoleUpateRequest
	if err := c.Bind(&request); err == nil {
		res := request.PutSysRoleUpdate()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 删除角色
// @Description
// @Tags 后台/角色管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  body  service_admin.SysRoleIDRequest  true  "参数"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /admin/system/role [DELETE]
func DelSysRoleDel(c *gin.Context) {
	var request service_admin.SysRoleIDRequest
	if err := c.Bind(&request); err == nil {
		res := request.DelSysRoleDel()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 获取角色权限列表
// @Description
// @Tags 后台/角色管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  query  service_admin.SysRoleIDFRequest  true  "参数"
// @Success 200 {object} structs.Response{data=service_admin.SysRolePermListResponse} "返回码看对照表"
// @Router /admin/system/role/perm_list [GET]
func GetSysRolePermList(c *gin.Context) {
	var request service_admin.SysRoleIDFRequest
	if err := c.Bind(&request); err == nil {
		res := request.GetSysRolePermList()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 修改角色权限
// @Description
// @Tags 后台/角色管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  body  service_admin.SysRolePermSetRequest  true  "参数"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /admin/system/role/perm_set [PUT]
func PutSysRolePermSet(c *gin.Context) {
	var request service_admin.SysRolePermSetRequest
	if err := c.Bind(&request); err == nil {
		res := request.PutSysRolePermSet()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}
