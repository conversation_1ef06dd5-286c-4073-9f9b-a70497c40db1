package contorllers_admin

import (
	"net/http"
	"path/filepath"
	"strings"
	"time"
	"trading_tick_server/lib/function"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/structs"
	service_common "trading_tick_server/web/service/common"

	"github.com/gin-gonic/gin"
	"github.com/sknun/cf/cast"
)

// @Summary 上传文件
// @Description
// @Tags 后台/上传管理
// @Security BearerAuth
// @Accept  multipart/form-data
// @Produce  json
// @Param   file  formData  file  true  "上传的文件"
// @Success 200 {object} structs.Response{data=service_common.CommonUriResponse} "返回码看对照表"
// @Router /admin/upload_file [POST]
func PostUploadFile(c *gin.Context) {
	c.JSON(http.StatusOK, structs.Response{
		Code:    service_common.CodeCacheError,
		Message: "禁止操作",
	})
}

// @Summary 上传图片
// @Description
// @Tags 后台/上传管理
// @Security BearerAuth
// @Accept  multipart/form-data
// @Produce  json
// @Param   image  formData  file  true  "上传的图片"
// @Success 200 {object} structs.Response{data=service_common.CommonUriResponse} "返回码看对照表"
// @Router /admin/upload_image [POST]
func PostUploadImage(c *gin.Context) {
	// 获取上传的文件
	file, err := c.FormFile("image")
	if err != nil {
		c.JSON(http.StatusOK, service_common.ParamError(err))
		return
	}

	// 获取文件的扩展名
	ext := strings.ToLower(filepath.Ext(file.Filename))

	if !function.IsAllowedExtension(ext) {
		c.JSON(http.StatusOK, structs.Response{Code: service_common.CodeCommonError, Message: "禁止的扩展名"})
		return
	}

	// 保存图片文件到服务器本地
	name := cast.ToString(time.Now().Unix()) + ext
	dir := function.GenerateDirForData() + "/"
	filePath := global.PathUploads + dir + name
	if err := c.SaveUploadedFile(file, filePath); err != nil {
		c.JSON(http.StatusOK, structs.Response{Code: service_common.CodeCommonError, Message: "Failed to save image"})
		return
	}

	uri := global.Yaml.WebServer.LocalPath + dir + name

	c.JSON(http.StatusOK, structs.Response{
		Code:    service_common.CodeSuccess,
		Message: "ok",
		Data:    service_common.CommonUriResponse{Uri: uri},
	})
}
