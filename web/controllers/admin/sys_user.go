package contorllers_admin

import (
	"net/http"
	"trading_tick_server/lib/structs"
	service_admin "trading_tick_server/web/service/admin"
	service_common "trading_tick_server/web/service/common"

	"github.com/gin-gonic/gin"
)

// @Summary 系统用户列表
// @Description 无搜索条件。
// @Tags 后台/系统用户管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  query  service_admin.SysUserListRequest  true  "参数"
// @Success 200 {object} structs.Response{data=service_admin.SysUserListResponse} "返回码看对照表"
// @Router /admin/system/user [GET]
func GetSysUserList(c *gin.Context) {
	var request service_admin.SysUserListRequest
	if err := c.Bind(&request); err == nil {
		res := request.GetSysUserList()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 添加系统用户
// @Description
// @Tags 后台/系统用户管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  body  service_admin.SysUserAddRequest  true  "参数"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /admin/system/user [POST]
func PostSysUserAdd(c *gin.Context) {
	var request service_admin.SysUserAddRequest
	if err := c.Bind(&request); err == nil {
		res := request.PostSysUserAdd()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 修改系统用户
// @Description
// @Tags 后台/系统用户管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  body  service_admin.SysUserUpateRequest  true  "参数"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /admin/system/user [PUT]
func PutSysUserUpdate(c *gin.Context) {
	var request service_admin.SysUserUpateRequest
	if err := c.Bind(&request); err == nil {
		res := request.PutSysUserUpdate()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 删除系统用户
// @Description
// @Tags 后台/系统用户管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  body  service_admin.SysUserIDRequest  true  "参数"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /admin/system/user [DELETE]
func DelSysUserDel(c *gin.Context) {
	var request service_admin.SysUserIDRequest
	if err := c.Bind(&request); err == nil {
		res := request.DelSysUserDel()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 更改系统用户状态
// @Description
// @Tags 后台/系统用户管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  body  service_admin.SysUserStatusRequest  true  "参数"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /admin/system/user/status [PUT]
func PutSysUserStatus(c *gin.Context) {
	var request service_admin.SysUserStatusRequest
	if err := c.Bind(&request); err == nil {
		res := request.PutSysUserStatus()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 重置系统用户谷歌密钥
// @Description
// @Tags 后台/系统用户管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  body  service_admin.SysUserIDRequest  true  "参数"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /admin/system/user/reset_gs [PUT]
func PutSysUserResetGS(c *gin.Context) {
	var request service_admin.SysUserIDRequest
	if err := c.Bind(&request); err == nil {
		res := request.PutSysUserResetGS()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 获取系统用户角色列表
// @Description
// @Tags 后台/系统用户管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  query  service_admin.SysUserIDFRequest  true  "参数"
// @Success 200 {object} structs.Response{data=service_admin.SysUserRoleListResponse} "返回码看对照表"
// @Router /admin/system/user/role_list [GET]
func GetSysUserRoleList(c *gin.Context) {
	var request service_admin.SysUserIDFRequest
	if err := c.Bind(&request); err == nil {
		res := request.GetSysUserRoleList()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 修改系统用户角色
// @Description
// @Tags 后台/系统用户管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  body  service_admin.SysUserRoleSetRequest  true  "参数"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /admin/system/user/role_set [PUT]
func PutSysUserRoleSet(c *gin.Context) {
	var request service_admin.SysUserRoleSetRequest
	if err := c.Bind(&request); err == nil {
		res := request.PutSysUserRoleSet()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 修改密码
// @Description
// @Tags 后台/个人操作
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  body  service_admin.ResetPasswordRequest  true  "验证请求"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /admin/reset_password [POST]
func PostResetPassword(c *gin.Context) {
	var request service_admin.ResetPasswordRequest
	user, ok := c.Get("user")
	if !ok {
		c.JSON(http.StatusOK, service_common.AdminUserDataError())
	}
	if err := c.Bind(&request); err == nil {
		res := request.PostResetPassword(user.(structs.SysUser))
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}
