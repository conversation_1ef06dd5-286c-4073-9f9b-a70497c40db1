package contorllers_admin

import (
	"net/http"
	service_admin "trading_tick_server/web/service/admin"
	service_common "trading_tick_server/web/service/common"

	"github.com/gin-gonic/gin"
)

// @Summary 站点配置列表
// @Description 无搜索条件。
// @Tags 后台/站点管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Success 200 {object} structs.Response{data=service_admin.SiteConfigListResponse} "返回码看对照表"
// @Router /admin/system/siteconfig [GET]
func GetSiteConfigList(c *gin.Context) {
	var request service_admin.SiteConfigListRequest
	if err := c.Bind(&request); err == nil {
		res := request.GetSiteConfigList()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 修改站点配置
// @Description
// @Tags 后台/站点管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  body  service_admin.SiteConfigUpateRequest  true  "参数"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /admin/system/siteconfig [PUT]
func PutSiteConfigUpdate(c *gin.Context) {
	var request service_admin.SiteConfigUpateRequest
	if err := c.Bind(&request); err == nil {
		res := request.PutSiteConfigUpdate()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}
