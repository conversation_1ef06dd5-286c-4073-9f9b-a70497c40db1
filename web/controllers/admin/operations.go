package contorllers_admin

import (
	"net/http"
	service_admin "trading_tick_server/web/service/admin"
	service_common "trading_tick_server/web/service/common"

	"github.com/gin-gonic/gin"
)

// @Summary 内置操作
// @Description
// @Tags 后台/内置操作
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  body  service_admin.OperationsRequest  true  "参数"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /admin/system/siteconfig [PUT]
func PutOperations(c *gin.Context) {
	var request service_admin.OperationsRequest
	if err := c.Bind(&request); err == nil {
		res := request.PutOperations()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}
