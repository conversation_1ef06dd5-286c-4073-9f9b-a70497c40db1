package contorllers_admin

import (
	"net/http"
	service_admin "trading_tick_server/web/service/admin"
	service_common "trading_tick_server/web/service/common"

	"github.com/gin-gonic/gin"
)

// @Summary 权限列表
// @Description 无搜索条件。
// @Tags 后台/权限管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  query  service_admin.SysPermListRequest  true  "参数"
// @Success 200 {object} structs.Response{data=service_admin.SysPermListResponse} "返回码看对照表"
// @Router /admin/system/perm [GET]
func GetSysPermList(c *gin.Context) {
	var request service_admin.SysPermListRequest
	if err := c.Bind(&request); err == nil {
		res := request.GetSysPermList()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 添加权限
// @Description
// @Tags 后台/权限管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  body  service_admin.SysPermAddRequest  true  "参数"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /admin/system/perm [POST]
func PostSysPermAdd(c *gin.Context) {
	var request service_admin.SysPermAddRequest
	if err := c.Bind(&request); err == nil {
		res := request.PostSysPermAdd()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 修改权限
// @Description
// @Tags 后台/权限管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  body  service_admin.SysPermUpateRequest  true  "参数"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /admin/system/perm [PUT]
func PutSysPermUpdate(c *gin.Context) {
	var request service_admin.SysPermUpateRequest
	if err := c.Bind(&request); err == nil {
		res := request.PutSysPermUpdate()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 删除权限
// @Description
// @Tags 后台/权限管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  body  service_admin.SysPermIDRequest  true  "参数"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /admin/system/perm [DELETE]
func DelSysPermDel(c *gin.Context) {
	var request service_admin.SysPermIDRequest
	if err := c.Bind(&request); err == nil {
		res := request.DelSysPermDel()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}
