package contorllers_admin

import (
	"net/http"

	"trading_tick_server/lib/structs"
	service_admin "trading_tick_server/web/service/admin"
	service_common "trading_tick_server/web/service/common"

	"github.com/gin-gonic/gin"
)

// @Summary 生成谷歌令牌密钥
// @Description
// @Tags 后台/个人操作
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Success 200 {object} structs.Response{data=service_admin.GetBuildGoogleTotpResponse} "返回码看对照表"
// @Router /admin/build_google_totp [GET]
func GetBuildGoogleTotp(c *gin.Context) {
	var request service_admin.GetBuildGoogleTotpRequest
	user, ok := c.Get("user")
	if !ok {
		c.JSON(http.StatusOK, service_common.AdminUserDataError())
	}
	res := request.GetBuildGoogleTotp(user.(structs.SysUser))
	c.<PERSON><PERSON>(http.StatusOK, res)
}

// @Summary 提交谷歌令牌密钥
// @Description
// @Tags 后台/个人操作
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  body  service_admin.PostBuildGoogleTotpRequest  true  "验证请求"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /admin/build_google_totp [POST]
func PostBuildGoogleTotp(c *gin.Context) {
	var request service_admin.PostBuildGoogleTotpRequest
	user, ok := c.Get("user")
	if !ok {
		c.JSON(http.StatusOK, service_common.AdminUserDataError())
	}
	if err := c.Bind(&request); err == nil {
		res := request.PostBuildGoogleTotp(user.(structs.SysUser))
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 重新验证谷歌令牌
// @Description
// @Tags 后台/个人操作
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  body  service_admin.PostVerifyGoogleTotpRequest  true  "验证请求"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /admin/verify_google_totp [POST]
func PostVerifyGoogleTotp(c *gin.Context) {
	var request service_admin.PostVerifyGoogleTotpRequest
	user, ok := c.Get("user")
	if !ok {
		c.JSON(http.StatusOK, service_common.AdminUserDataError())
	}
	if err := c.Bind(&request); err == nil {
		res := request.PostVerifyGoogleTotp(user.(structs.SysUser))
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}
