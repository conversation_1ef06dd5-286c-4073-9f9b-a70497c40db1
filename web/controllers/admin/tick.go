package contorllers_admin

import (
	"net/http"
	service_admin "trading_tick_server/web/service/admin"
	service_common "trading_tick_server/web/service/common"

	"github.com/gin-gonic/gin"
)

// @Summary 产品列表
// @Description 无搜索条件。
// @Tags 后台/产品管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  query  service_admin.TickListRequest  true  "参数"
// @Success 200 {object} structs.Response{data=structs.Response} "返回码看对照表"
// @Router /admin/tick/tick [GET]
func GetTickList(c *gin.Context) {
	var request service_admin.TickListRequest
	if err := c.Bind(&request); err == nil {
		res := request.GetTickList()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 产品列表
// @Description 无搜索条件。
// @Tags 后台/产品管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  query  service_admin.TickListLogRequest  true  "参数"
// @Success 200 {object} structs.Response{data=structs.Response} "返回码看对照表"
// @Router /admin/tick/tick_log [GET]
func GetTickLogList(c *gin.Context) {
	var request service_admin.TickListLogRequest
	if err := c.Bind(&request); err == nil {
		res := request.GetTickLogList()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 产品列表
// @Description 无搜索条件。
// @Tags 后台/产品管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  query  service_admin.KlineHistoryRequest  true  "参数"
// @Success 200 {object} structs.Response{data=structs.Response} "返回码看对照表"
// @Router /admin/tick/kline_history [GET]
func GetKlineHistory(c *gin.Context) {
	var request service_admin.KlineHistoryRequest
	if err := c.Bind(&request); err == nil {
		// res := request.GetKlineHistory()
		// c.JSON(http.StatusOK, res)
		c.JSON(http.StatusOK, service_common.ParamError(err))
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 产品详情
// @Description 无搜索条件。
// @Tags 后台/产品管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  query  service_admin.TickViewRequest  true  "参数"
// @Success 200 {object} structs.Response{data=structs.Response} "返回码看对照表"
// @Router /admin/tick/view [GET]
func GetTickView(c *gin.Context) {
	var request service_admin.TickViewRequest
	if err := c.Bind(&request); err == nil {
		res := request.GetTickView()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 添加产品
// @Description
// @Tags 后台/产品管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  body  service_admin.TickAddRequest  true  "参数"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /admin/tick/tick [POST]
func PostTickAdd(c *gin.Context) {
	var request service_admin.TickAddRequest
	if err := c.Bind(&request); err == nil {
		res := request.PostTickAdd()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 修改产品
// @Description
// @Tags 后台/产品管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  body  service_admin.TickUpateRequest  true  "参数"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /admin/tick/tick [PUT]
func PutTickUpdate(c *gin.Context) {
	var request service_admin.TickUpateRequest
	if err := c.Bind(&request); err == nil {
		res := request.PutTickUpdate()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 重置缓存
// @Description
// @Tags 后台/产品管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  body  service_admin.TickResetRequest  true  "参数"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /admin/tick/tick_reset [PUT]
func PutTickReset(c *gin.Context) {
	var request service_admin.TickResetRequest
	if err := c.Bind(&request); err == nil {
		res := request.PutTickReset()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}

// @Summary 删除产品
// @Description
// @Tags 后台/产品管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  body  service_admin.TickIDRequest  true  "参数"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /admin/tick/tick [DELETE]
func DelTickDel(c *gin.Context) {
	var request service_admin.TickIDRequest
	if err := c.Bind(&request); err == nil {
		res := request.DelTickDel()
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}
