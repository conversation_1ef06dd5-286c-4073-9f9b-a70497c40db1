package service_common

import (
	"trading_tick_server/lib/cache"
	"trading_tick_server/lib/structs"

	"github.com/gin-gonic/gin"
)

const (
	CodeSuccess         = 200 // 成功代码
	CodeCommonError     = 520 // 通用错误代码
	CodeParamError      = 550 // 参数错误
	CodeNeedLogin       = 551 // 需要登录
	CodeGoogleTotpError = 552 // 需要谷歌验证
	CodeTokenError      = 553 // 令牌生成失败
	CodeNeedGoogleTotp  = 554 // 需要绑定谷歌验证
	CodePassError       = 555 // 密码错误
	CodeAuthError       = 556 // 权限不足
	CodeCacheError      = 557 // 设置缓存失败
	CodePermissionError = 558 // 取权限错误
	CodeDBError         = 559 // mysql错误

)

type CommonListRequest struct {
	// 页码 可空 默认为1
	Page int `form:"page,omitempty"`
	// 每页显示数量 可空 默认值由项目启动参数决定
	PageSize int `form:"page_size,omitempty"`
}

type CommonUriResponse struct {
	// 图片或文件访问地址
	Uri string `json:"uri"`
}

func Err(code int, msg string, err error) structs.Response {
	res := structs.Response{
		Code:    code,
		Message: msg,
	}

	if err != nil && gin.Mode() != gin.ReleaseMode {
		res.Error = err.Error()
	}
	return res
}

func NeedLogin() structs.Response {
	msg := "the user is not logged in, please log in and then perform another operation"
	return Err(CodeNeedLogin, msg, nil)
}

func ParamError(err error) structs.Response {
	msg := "The parameter is wrong, please confirm the operation process before performing this operation"
	return Err(CodeParamError, msg, err)
}

func AdminUserDataError() structs.Response {
	msg := "Failed to obtain user data, please try again later"
	return Err(CodeNeedGoogleTotp, msg, nil)
}

func GoogleTotpError() structs.Response {
	msg := "Need to bind Google Authenticator, please bind and operate"
	return Err(CodeNeedGoogleTotp, msg, nil)
}

func GoogleTotpVerifyError() structs.Response {
	msg := "Need to verify Google Authenticator, Please re-verify and operate"
	return Err(CodeGoogleTotpError, msg, nil)
}

func ClearToken(u structs.SysUser) error {
	err := cache.SysUserDel(u.Token)
	if err != nil {
		return err
	}
	err = cache.GoogleTotpHeartbeatDel(u.Token)
	if err != nil {
		return err
	}
	return nil
}

func SetHeartbeat(token string) {
	cache.GoogleTotpHeartbeatSet(token)
}

func SystemUserAuthError() structs.Response {
	msg := "Insufficient permissions"
	return Err(CodeAuthError, msg, nil)
}
