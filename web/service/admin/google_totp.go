package service_admin

import (
	"trading_tick_server/lib/cache"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/google_totp"
	"trading_tick_server/lib/mysql"
	"trading_tick_server/lib/structs"
	service_common "trading_tick_server/web/service/common"

	"github.com/sknun/cf/cast"
)

type GetBuildGoogleTotpRequest struct{}

type PostBuildGoogleTotpRequest struct {
	// 谷歌验证器密钥
	GoogleSecret string `json:"google_secret" binding:"required"`
	// 验证码
	GoogleCode string `json:"google_code" binding:"required,len=6"`
}

type PostVerifyGoogleTotpRequest struct {
	// 验证码
	GoogleCode string `json:"google_code" binding:"required,len=6"`
}

type GetBuildGoogleTotpResponse struct {
	// 谷歌验证器密钥
	GoogleSecret string `json:"google_secret"`
}

func (r *GetBuildGoogleTotpRequest) GetBuildGoogleTotp(u structs.SysUser) structs.Response {
	googleSecret, err := google_totp.GenerateKey(cast.ToString(u.ID), global.Yaml.WebServer.JWTIssuer)
	if err != nil {
		return structs.Response{Code: service_common.CodeCommonError, Message: "生成谷歌密钥失败"}
	}
	return structs.Response{Code: service_common.CodeSuccess, Data: GetBuildGoogleTotpResponse{GoogleSecret: googleSecret}}
}

func (r *PostBuildGoogleTotpRequest) PostBuildGoogleTotp(u structs.SysUser) structs.Response {
	if r.GoogleSecret == "" || r.GoogleCode == "" {
		return structs.Response{Code: service_common.CodeCommonError, Message: "谷歌密钥验证失败"}
	}
	if ok := google_totp.ValidateCode(r.GoogleSecret, r.GoogleCode); !ok {
		return structs.Response{Code: service_common.CodeCommonError, Message: "谷歌密钥验证失败"}
	}
	// 更新数据
	u.GoogleSecret = r.GoogleSecret
	mysql.M.Select("google_secret").Save(&u)
	// 设置缓存
	err := cache.SysUserSet(u.Token, u)
	if err != nil {
		return structs.Response{Code: service_common.CodeCacheError, Message: "设置缓存失败"}
	}
	return structs.Response{Code: service_common.CodeSuccess, Message: "ok"}
}

func (r *PostVerifyGoogleTotpRequest) PostVerifyGoogleTotp(u structs.SysUser) structs.Response {
	if ok := google_totp.ValidateCode(u.GoogleSecret, r.GoogleCode); !ok {
		return structs.Response{Code: service_common.CodeCommonError, Message: "谷歌密钥验证失败"}
	}
	// 更新缓存
	err := cache.GoogleTotpHeartbeatSet(u.Token)
	if err != nil {
		return structs.Response{Code: service_common.CodeCacheError, Message: "设置缓存失败"}
	}
	return structs.Response{Code: service_common.CodeSuccess, Message: "ok"}
}
