package service_admin

import (
	"fmt"
	"trading_tick_server/lib/cache"
	"trading_tick_server/lib/mysql"
	"trading_tick_server/lib/structs"
	service_common "trading_tick_server/web/service/common"

	"gorm.io/gorm"
)

type SysRoleListRequest struct {
	service_common.CommonListRequest
}

type SysRoleAddRequest struct {
	// 名称
	Name string `json:"name" binding:"required"`
	// 备注
	Description string `json:"description,omitempty"`
	// 排序字段
	SortOrder int `json:"sort_order,omitempty"`
}

type SysRoleUpateRequest struct {
	// ID
	ID uint `json:"id" binding:"required,min=1"`
	// 名称
	Name string `json:"name" binding:"required"`
	// 备注
	Description string `json:"description,omitempty"`
	// 排序字段
	SortOrder int `json:"sort_order,omitempty"`
}

type SysRoleIDRequest struct {
	// 角色ID
	ID uint `json:"id" binding:"required,min=1"`
}

type SysRoleIDFRequest struct {
	// 角色ID
	ID uint `form:"id" binding:"required,min=1"`
}

type SysRolePermSetRequest struct {
	// 角色ID
	ID uint `json:"id" binding:"required,min=1"`
	// 权限ID数组
	PermissionIDs []uint `json:"permission_ids" binding:"required,min=1,dive,gt=0"`
}

type SysRoleListResponse struct {
	// 返回当前页码
	Page int `json:"page"`
	// 返回当前页码数量
	PageSize int `json:"page_size"`
	// 角色列表
	List []structs.SysRole `json:"list"`
	// 总数量
	Total int64 `json:"total"`
}

type SysRolePermListResponse struct {
	// 角色已有的权限列表
	SysRolePermission []structs.SysPermission `json:"sys_role_permission"`
}

/*
角色列表
*/
func (r *SysRoleListRequest) GetSysRoleList() structs.Response {
	if r.Page < 1 {
		r.Page = 1
	}
	if r.PageSize < 1 || r.PageSize > 100 {
		r.PageSize = 500
	}
	var list []structs.SysRole
	var total int64
	if err := mysql.M.Model(&structs.SysRole{}).Offset((r.Page - 1) * r.PageSize).Limit(r.PageSize).Order("sort_order asc").Find(&list).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: err.Error()}
	}
	mysql.M.Model(&structs.SysRole{}).Count(&total)
	return structs.Response{Code: service_common.CodeSuccess, Data: SysRoleListResponse{Page: r.Page, PageSize: r.PageSize, List: list, Total: total}}
}

/*
添加角色
*/
func (r *SysRoleAddRequest) PostSysRoleAdd() structs.Response {
	var param structs.SysRole
	param.Name = r.Name
	param.Description = r.Description
	if r.SortOrder > 0 {
		param.SortOrder = r.SortOrder
	} else {
		param.SortOrder = mysql.MaxValue("SysRole") + 1
	}
	if err := mysql.M.Create(&param).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: "添加失败"}
	}
	return structs.Response{Code: service_common.CodeSuccess, Message: "ok"}
}

/*
修改角色
*/
func (r *SysRoleUpateRequest) PutSysRoleUpdate() structs.Response {
	var param structs.SysRole
	slices := []string{"name", "description"}

	param.ID = r.ID
	if err := mysql.M.First(&param, r.ID).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: fmt.Sprintf("无法找到角色: %v", err)}
	}
	param.Name = r.Name
	param.Description = r.Description
	if r.SortOrder > 0 {
		param.SortOrder = r.SortOrder
		slices = append(slices, "sort_order")
	}

	if err := mysql.M.Select(slices).Save(&param).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: "修改失败"}
	}
	return structs.Response{Code: service_common.CodeSuccess, Message: "ok"}
}

/*
删除角色
*/
func (r *SysRoleIDRequest) DelSysRoleDel() structs.Response {
	var count int64
	// 查询用户与角色关联表，看是否有用户关联该角色
	err := mysql.M.Model(&structs.SysUserRole{}).Where("sys_role_id = ?", r.ID).Count(&count).Error
	if err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: "删除失败"}
	}
	if count > 0 {
		return structs.Response{Code: service_common.CodeDBError, Message: "删除失败,有用户正在关联角色"}
	}
	if err := mysql.M.Model(&structs.SysRole{}).Unscoped().Delete(&structs.SysRole{}, r.ID).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: "删除失败"}
	}
	return structs.Response{Code: service_common.CodeSuccess, Message: "ok"}
}

/*
角色的权限列表
*/
func (r *SysRoleIDFRequest) GetSysRolePermList() structs.Response {
	var role structs.SysRole

	if err := mysql.M.Preload("SysRolePermission", func(db *gorm.DB) *gorm.DB {
		return db.Order("sort_order asc")
	}).First(&role, r.ID).Error; err != nil {
		fmt.Println(err)
		return structs.Response{Code: service_common.CodeDBError, Message: err.Error()}
	}
	fmt.Println(role)
	return structs.Response{Code: service_common.CodeSuccess, Data: SysRolePermListResponse{SysRolePermission: role.SysRolePermission}}
}

/*
修改角色权限
*/
func (r *SysRolePermSetRequest) PutSysRolePermSet() structs.Response {
	err := mysql.M.Transaction(func(tx *gorm.DB) error {
		// 查找角色
		var role structs.SysRole
		if err := tx.First(&role, r.ID).Error; err != nil {
			return fmt.Errorf("无法找到角色: %v", err)
		}

		// 清空角色的现有权限
		if err := tx.Model(&role).Association("SysRolePermission").Clear(); err != nil {
			return fmt.Errorf("清空现有权限失败: %v", err)
		}

		// 根据新的权限ID查找权限并关联
		var newPermissions []structs.SysPermission
		if err := tx.Where("id IN ?", r.PermissionIDs).Find(&newPermissions).Error; err != nil {
			return fmt.Errorf("无法找到新权限: %v", err)
		}

		if len(r.PermissionIDs) != len(newPermissions) {
			return fmt.Errorf("不存在的权限ID")
		}

		// 重新关联新权限
		if err := tx.Model(&role).Association("SysRolePermission").Replace(newPermissions); err != nil {
			return fmt.Errorf("关联新权限失败: %v", err)
		}

		return nil
	})

	if err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: err.Error()}
	}

	// 修改角色权限时清理所以权限记录
	cache.SystemUserAuthDelAll()

	return structs.Response{Code: service_common.CodeSuccess, Message: "ok"}
}
