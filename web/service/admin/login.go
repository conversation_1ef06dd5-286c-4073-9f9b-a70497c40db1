package service_admin

import (
	"errors"
	"fmt"
	"trading_tick_server/lib/cache"
	"trading_tick_server/lib/function"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/google_totp"
	"trading_tick_server/lib/jwtoken"
	"trading_tick_server/lib/mysql"
	"trading_tick_server/lib/structs"
	service_common "trading_tick_server/web/service/common"

	"github.com/sknun/cf/cast"
	"gorm.io/gorm"
)

type LoginRequest struct {
	// 账号 和邮箱必填写一项
	Username string `json:"username,omitempty" binding:"required_without=Email"`
	// 邮箱 和账号必填写一项
	Email string `json:"email,omitempty" binding:"required_without=Username"`
	// 密码
	Password string `json:"password" binding:"required"`
	// 谷歌验证码
	GoogleCode string `json:"google_code,omitempty"`
}

type LogoutRequest struct{}

type LoginResponse struct {
	// 用户数据
	User structs.SysUser `json:"user"`
	// 权限列表
	Permissions string `json:"permissions"`
	// 是否需要绑定谷歌验证器
	NeedBuildTotp bool `json:"need_build_totp"`
}

func (r *LoginRequest) PostLogin(ip string) structs.Response {
	if r.Password == "" {
		return structs.Response{Code: service_common.CodePassError, Message: "密码不能为空"}
	}
	var user structs.SysUser
	// 初始化查询条件
	var condition string
	var args []interface{}
	var err error

	// 动态构建查询条件
	if r.Username != "" {
		condition = "username = ? AND password = ? AND status=1"
		args = append(args, r.Username, function.HashPasswordArgon2(r.Password))
	} else if r.Email != "" {
		condition = "email = ? AND password = ? AND status=1"
		args = append(args, r.Email, function.HashPasswordArgon2(r.Password))
	} else {
		return service_common.ParamError(nil)
	}

	// 查询管理
	if err := mysql.M.Where(condition, args...).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return structs.Response{Code: service_common.CodeCommonError, Message: "未找到该用户"}
		}
		return structs.Response{Code: service_common.CodePassError, Message: "数据库连接失败"}
	}

	// 判断是否需要谷歌验证
	if user.GoogleSecret != "" {
		if len(r.GoogleCode) != 6 {
			return structs.Response{Code: service_common.CodeGoogleTotpError, Message: "需要谷歌验证码"}
		}
		if !google_totp.ValidateCode(user.GoogleSecret, r.GoogleCode) {
			return structs.Response{Code: service_common.CodeGoogleTotpError, Message: "谷歌验证码错误"}
		}
	}

	user.Token, err = jwtoken.GenerateToken(cast.ToString(user.ID))
	if err != nil {
		return structs.Response{Code: service_common.CodeTokenError, Message: "令牌生成失败"}
	}

	fmt.Println("user", user)
	permissionJson, err := function.GetSysUserPermissions(user.ID)
	if err != nil {
		return structs.Response{Code: service_common.CodePermissionError, Message: "获取权限失败"}
	}

	// 记录日志和更新数据
	mysql.M.Select("token").Save(user)
	log := structs.SysLoginLog{UserId: user.ID, LoginToken: user.Token, LoginIp: ip}
	mysql.M.Create(&log)
	// 设置缓存
	err = cache.SysUserSet(user.Token, user)
	if err != nil {
		return structs.Response{Code: service_common.CodeCacheError, Message: "设置缓存失败"}
	}
	err = cache.GoogleTotpHeartbeatSet(user.Token)
	if err != nil {
		return structs.Response{Code: service_common.CodeCacheError, Message: "设置缓存失败"}
	}

	// 判断是否要强制绑定google totp
	needBuildTotp := false
	if global.Yaml.WebServer.GoogleTotp && user.GoogleSecret == "" {
		needBuildTotp = true
	}

	// 验证成功返回用户数据、token、权限列表 并去掉不需要返回的字段
	user.Password = ""
	user.GoogleSecret = ""
	return structs.Response{Code: service_common.CodeSuccess, Data: LoginResponse{User: user, Permissions: permissionJson, NeedBuildTotp: needBuildTotp}}
}

func (r *LogoutRequest) GetLogout(u structs.SysUser) structs.Response {
	// 删除缓存
	err := service_common.ClearToken(u)
	if err != nil {
		return structs.Response{Code: service_common.CodeCacheError, Message: "清理缓存失败"}
	}
	return structs.Response{Code: service_common.CodeSuccess, Message: "ok"}
}
