package service_admin

import (
	"fmt"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/mysql"
	"trading_tick_server/lib/structs"
	service_common "trading_tick_server/web/service/common"

	"gorm.io/gorm"
)

type SysPermListRequest struct {
	service_common.CommonListRequest
}

type SysPermAddRequest struct {
	// 名称
	Name string `json:"name" binding:"required"`
	// 资源
	Resource string `json:"resource" binding:"required"`
	// menu 或 action
	Type string `json:"type" binding:"required,oneof=menu action"`
	// 父级权限ID，0表示顶级
	ParentID uint `json:"parent_id" binding:"min=0"`
	// 备注
	Description string `json:"description,omitempty"`
	// 排序字段
	SortOrder int `json:"sort_order,omitempty"`
}

type SysPermUpateRequest struct {
	// ID
	ID uint `json:"id" binding:"required,min=1"`
	// 名称
	Name string `json:"name" binding:"required"`
	// 资源
	Resource string `json:"resource" binding:"required"`
	// menu 或 action
	Type string `json:"type" binding:"required,oneof=menu action"`
	// 父级权限ID，0表示顶级
	ParentID uint `json:"parent_id" binding:"min=0"`
	// 备注
	Description string `json:"description,omitempty"`
	// 排序字段
	SortOrder int `json:"sort_order,omitempty"`
}

type SysPermIDRequest struct {
	// ID
	ID uint `json:"id" binding:"required,min=1"`
}

type SysPermListResponse struct {
	// 返回当前页码
	Page int `json:"page"`
	// 返回当前页码数量
	PageSize int `json:"page_size"`
	// 权限列表
	List []structs.SysPermission `json:"list"`
	// 总数量
	Total int64 `json:"total"`
}

/*
权限列表
*/
func (r *SysPermListRequest) GetSysPermList() structs.Response {
	if r.Page < 1 {
		r.Page = 1
	}
	if r.PageSize < 1 || r.PageSize > 100 {
		r.PageSize = 500
	}
	var list []structs.SysPermission
	var total int64
	if err := mysql.M.Model(&structs.SysPermission{}).Offset((r.Page - 1) * r.PageSize).Limit(r.PageSize).Order("sort_order asc").Find(&list).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: err.Error()}
	}
	mysql.M.Model(&structs.SysPermission{}).Count(&total)
	for k := range list {
		list[k].Type = global.SysMenuTypes[list[k].Type]
	}
	return structs.Response{Code: service_common.CodeSuccess, Data: SysPermListResponse{Page: r.Page, PageSize: r.PageSize, List: list, Total: total}}
}

/*
添加权限
*/
func (r *SysPermAddRequest) PostSysPermAdd() structs.Response {
	var param structs.SysPermission
	param.Name = r.Name
	param.Resource = r.Resource
	param.Type = r.Type
	param.ParentID = r.ParentID
	param.Description = r.Description
	if r.SortOrder > 0 {
		param.SortOrder = r.SortOrder
	} else {
		param.SortOrder = mysql.MaxValue("SysPermission") + 1
	}
	if err := mysql.M.Create(&param).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: "添加失败"}
	}
	return structs.Response{Code: service_common.CodeSuccess, Message: "ok"}
}

/*
修改权限
*/
func (r *SysPermUpateRequest) PutSysPermUpdate() structs.Response {
	var param structs.SysPermission
	slices := []string{"name", "resource", "type", "parent_id", "description"}

	param.ID = r.ID
	if err := mysql.M.First(&param, r.ID).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: fmt.Sprintf("无法找到权限: %v", err)}
	}
	param.Name = r.Name
	param.Resource = r.Resource
	param.Type = r.Type
	param.ParentID = r.ParentID
	param.Description = r.Description

	if r.SortOrder > 0 {
		param.SortOrder = r.SortOrder
		slices = append(slices, "sort_order")
	}

	if err := mysql.M.Select(slices).Save(&param).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: "修改失败"}
	}
	return structs.Response{Code: service_common.CodeSuccess, Message: "ok"}
}

/*
删除权限
*/
func (r *SysPermIDRequest) DelSysPermDel() structs.Response {
	// 启用事务删除 未使用联级
	err := mysql.M.Transaction(func(tx *gorm.DB) error {
		var permission structs.SysPermission

		// 查找对应权限
		if err := tx.First(&permission, r.ID).Error; err != nil {
			return err
		}

		// 清除该权限与所有角色的关联
		if err := tx.Where("sys_permission_id = ?", r.ID).Unscoped().Delete(&structs.SysRolePermission{}).Error; err != nil {
			return err
		}

		// 删除权限
		if err := tx.Unscoped().Delete(&permission).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: "删除失败"}
	}

	return structs.Response{Code: service_common.CodeSuccess, Message: "ok"}
}
