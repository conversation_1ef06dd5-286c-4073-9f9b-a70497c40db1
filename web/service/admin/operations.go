package service_admin

import (
	"trading_tick_server/internal/user/user_cache"
	"trading_tick_server/lib/cache"
	"trading_tick_server/lib/function"
	"trading_tick_server/lib/structs"
	service_common "trading_tick_server/web/service/common"
)

type OperationsRequest struct {
	// 操作类型
	Operation string `json:"operation" binding:"required,oneof=yaml user tick"`
}

/*
内置操作
*/
func (r *OperationsRequest) PutOperations() structs.Response {
	switch r.Operation {
	case "yaml":
		function.InitYaml()
	case "user":
		user_cache.CacheUserSliceSet()
	case "tick":
		cache.CacheTickSliceSet()
	}
	return structs.Response{Code: service_common.CodeSuccess, Message: "ok"}
}
