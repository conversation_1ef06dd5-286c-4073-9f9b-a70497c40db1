package service_admin

import (
	"context"
	"github.com/shopspring/decimal"
	"trading_tick_server/common/gmysql"
	"trading_tick_server/internal/user/user_cache"
	"trading_tick_server/lib/function"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/mysql"
	"trading_tick_server/lib/structs"
	service_common "trading_tick_server/web/service/common"

	"gorm.io/gorm"
)

type UserListRequest struct {
	service_common.CommonListRequest
	// ID
	ID int `form:"id,omitempty"`
	// 用户名
	Username string `form:"username,omitempty"`
}

type UserAddRequest struct {
	// 账号
	Username string `json:"username" binding:"required"`
	// 密码
	Password string `json:"password" binding:"required"`
	// token
	Token string `json:"token" binding:"required"`
	// 状态 0禁用 1启用
	Status        uint8  `json:"status" binding:"min=0,max=1"`
	StartDuration int32  `json:"start_duration" gorm:"column:start_duration;default:60;comment:开始控盘偏移时间(秒)"`
	EndDuration   int32  `json:"end_duration" gorm:"column:end_duration;default:60;comment:结束控盘偏移时间(秒)"`
	Remark        string `gorm:"type:varchar(100);not null;default:'';" json:"remark"`
}

type UserIDRequest struct {
	// 用户ID
	ID uint `json:"id" binding:"required,min=1"`
}

type UserViewRequest struct {
	// 用户ID
	ID uint `form:"id" binding:"required,min=1"`
}

type UserListResponse struct {
	// 返回当前页码
	Page int `json:"page"`
	// 返回当前页码数量
	PageSize int `json:"page_size"`
	// 用户列表
	List []structs.User `json:"list"`
	// 总数量
	Total int64 `json:"total"`
}

type UserViewResponse struct {
	// 用户详情数据
	Data structs.User `json:"data"`
}

// 用户Symbol列表请求
type UserSymbolListRequest struct {
	service_common.CommonListRequest
	// 用户ID
	UserID uint `form:"user_id" binding:"required,min=1"`
	// Symbol名称（可选，用于搜索）
	Symbol string `form:"symbol,omitempty"`
	// 类型（可选，用于搜索）
	Type string `form:"type,omitempty"`
}

// 用户Symbol列表响应
type UserSymbolListResponse struct {
	// 返回当前页码
	PageNum int `json:"page_num"`
	// 返回当前页码数量
	PageSize int `json:"page_size"`
	// 总页数
	TotalPage int `json:"total_page"`
	// 用户Symbol列表
	List []UserSymbol `json:"list"`
	// 总数量
	Total int64 `json:"total"`
}

type UserSymbol struct {
	UserID        uint   `gorm:"index;not null;default:0;" json:"user_id"`                     // 用户ID
	SymbolID      uint   `gorm:"index;not null;default:0;" json:"symbol_id"`                   // 产品ID
	Type          string `gorm:"type:varchar(30);not null;default:'';" json:"type"`            // 类型
	Platform      string `gorm:"type:varchar(30);not null;default:'';" json:"platform"`        // 产品平台
	Symbol        string `gorm:"type:varchar(30);not null;default:'';" json:"symbol"`          // 产品标识
	DecimalPlaces uint   `gorm:"not null;default:0;" json:"decimal_places"`                    // 小数位数
	FloatRange    string `gorm:"type:decimal(20,10);not null;default:0.0;" json:"float_range"` // 浮动绝对值范围，默认0
}

// 编辑用户Symbol请求
type UserSymbolEditRequest struct {
	// 用户ID
	UserID uint `json:"user_id" binding:"required,min=1"`
	// Symbol ID
	SymbolID uint `json:"symbol_id" binding:"required,min=1"`
	// 类型
	Type string `json:"type" binding:"required"`
	// 小数位数
	DecimalPlaces uint `json:"decimal_places"`
	// 浮动范围
	FloatRange float64 `json:"float_range"`
}

/*
会员列表
*/
func (r *UserListRequest) GetUserList() structs.Response {
	if r.Page < 1 {
		r.Page = 1
	}
	if r.PageSize < 1 || r.PageSize > 100 {
		r.PageSize = global.Yaml.WebServer.Admin.DefaultPageSize
	}

	var queryConditions []interface{}
	query := mysql.M.Model(&structs.User{})

	if r.ID > 0 {
		queryConditions = append(queryConditions, "id = ?", r.ID)
	}
	if r.Username != "" {
		queryConditions = append(queryConditions, "username = ?", r.Username)
	}

	if len(queryConditions) > 0 {
		query = query.Where(queryConditions[0], queryConditions[1:]...)
	}

	var list []structs.User
	var total int64
	query.Count(&total)
	if err := query.Preload("UserSymbols").Offset((r.Page - 1) * r.PageSize).Limit(r.PageSize).Order("id asc").Find(&list).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: err.Error()}
	}
	// 根据 user.UserSymbols 中的数据进行 tick 匹配
	for i := 0; i < len(list); i++ { // 所有用户
		var symbolList []string
		for _, us := range list[i].UserSymbols {
			symbolList = append(symbolList, us.Symbol) // 提取该用户所有的 symbol
		}

		if len(symbolList) > 0 {
			var ticks []structs.Tick
			if err := mysql.M.Model(&structs.Tick{}).Select("id").Where("symbol IN ?", symbolList).Find(&ticks).Error; err != nil {
				return structs.Response{Code: service_common.CodeDBError, Message: err.Error()}
			}
			for _, t := range ticks {
				list[i].Symbols = append(list[i].Symbols, t.ID) // 提取该用户所有的 tickID
			}
		}
	}

	return structs.Response{Code: service_common.CodeSuccess, Data: UserListResponse{Page: r.Page, PageSize: r.PageSize, List: list, Total: total}}
}

/*
会员详情列表
*/
func (r *UserViewRequest) GetUserView() structs.Response {
	var param structs.User

	if err := mysql.M.First(&param, r.ID).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: "用户不存在"}
	}
	return structs.Response{Code: service_common.CodeSuccess, Data: UserViewResponse{Data: param}}
}

/*
添加会员
*/
func (r *UserAddRequest) PostUserAdd() structs.Response {
	var param structs.User
	param.Username = r.Username
	param.Password = function.HashPasswordArgon2(r.Password)
	param.Token = r.Token
	param.Status = r.Status
	if r.StartDuration > 0 {
		param.StartDuration = r.StartDuration
	}
	if r.EndDuration > 0 {
		param.EndDuration = r.EndDuration
	}
	param.Remark = r.Remark

	// 启用事务
	err := mysql.M.Transaction(func(tx *gorm.DB) error {
		// 更新会员信息
		if err := tx.Create(&param).Error; err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: "添加失败"}
	}
	user_cache.CacheUserSliceSet()
	return structs.Response{Code: service_common.CodeSuccess, Message: "ok"}
}

/*
删除会员
*/
func (r *UserIDRequest) DelUserDel() structs.Response {
	err := mysql.M.Transaction(func(tx *gorm.DB) error {
		// 先删除 user_symbol 表中的相关记录
		if err := tx.Where("user_id = ?", r.ID).Delete(&structs.UserSymbol{}).Error; err != nil {
			return err
		}

		// 查询 user
		var user structs.User
		if err := tx.First(&user, r.ID).Error; err != nil {
			return err
		}

		// 软删除 user（Unscoped 代表强制物理删除，可根据需要调整）
		if err := tx.Unscoped().Delete(&user).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: "删除失败"}
	}

	user_cache.CacheUserSliceSet()
	return structs.Response{Code: service_common.CodeSuccess, Message: "ok"}
}

/*
获取用户Symbol列表
*/
func (r *UserSymbolListRequest) GetUserSymbolList() structs.Response {
	if r.Page < 1 {
		r.Page = 1
	}
	if r.PageSize < 1 || r.PageSize > 1000 {
		r.PageSize = 1000
	}

	// 构建查询条件
	condition := map[string]interface{}{
		"user_id": r.UserID,
	}

	if r.Symbol != "" {
		condition["symbol"] = r.Symbol
	}
	if r.Type != "" {
		condition["type"] = r.Type
	}

	// 使用 QueryPage 进行分页查询
	result, err := gmysql.QueryPage[structs.UserSymbol](
		context.Background(),
		mysql.M,
		r.Page,
		r.PageSize,
		condition,
		"symbol_id DESC",
	)

	if err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: err.Error()}
	}
	// 构建响应数据
	var userSymbols []UserSymbol
	for _, us := range result.List {
		userSymbols = append(userSymbols, UserSymbol{
			UserID:        us.UserID,
			SymbolID:      us.SymbolID,
			Type:          us.Type,
			Platform:      us.Platform,
			Symbol:        us.Symbol,
			DecimalPlaces: us.DecimalPlaces,
			FloatRange:    decimal.NewFromFloat(us.FloatRange).String(),
		})
	}

	return structs.Response{
		Code: service_common.CodeSuccess,
		Data: UserSymbolListResponse{
			PageNum:   result.PageNum,
			PageSize:  result.PageSize,
			TotalPage: result.TotalPage,
			List:      userSymbols,
			Total:     result.Total,
		},
	}
}

/*
编辑用户Symbol
*/
func (r *UserSymbolEditRequest) EditUserSymbol() structs.Response {
	result := mysql.M.Model(&structs.UserSymbol{}).
		Where("user_id = ? AND symbol_id = ? AND type = ?", r.UserID, r.SymbolID, r.Type).
		Updates(map[string]interface{}{
			"decimal_places": r.DecimalPlaces,
			"float_range":    r.FloatRange,
		})

	if result.Error != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: "编辑失败: " + result.Error.Error()}
	}

	// 更新缓存
	user_cache.CacheUserSliceSet()
	return structs.Response{Code: service_common.CodeSuccess, Message: "编辑成功"}
}
