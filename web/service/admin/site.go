package service_admin

import (
	"trading_tick_server/lib/mysql"
	"trading_tick_server/lib/structs"
	service_common "trading_tick_server/web/service/common"
)

type SiteConfigListRequest struct{}

// 提交时只需要id和最终值的数据即可
type SiteConfigUpateRequest struct {
	Configs []SiteConfigUpateRequestList
}

type SiteConfigUpateRequestList struct {
	// ID
	ID uint `json:"id" binding:"required,min=1"`
	// 值
	ConfigValue string `json:"config_value" binding:"required"`
}

type SiteConfigListResponse struct {
	// 配置数据
	Data []structs.SiteConfigGroup `json:"data"`
}

/*
站点配置列表
*/
func (r *SiteConfigListRequest) GetSiteConfigList() structs.Response {
	var configs []structs.SiteConfig
	if err := mysql.M.Where("use_state = ?", 1).Order("group_name, sort_order").Find(&configs).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: err.Error()}
	}

	groupMap := make(map[string][]structs.SiteConfigTree)
	for _, config := range configs {
		configTree := structs.SiteConfigTree{
			ID:          config.ID,
			Title:       config.Title,
			ConfigName:  config.ConfigName,
			ConfigValue: config.ConfigValue,
			ConfigType:  config.ConfigType,
			ValueScope:  config.ValueScope,
			TitleScope:  config.TitleScope,
			ConfigDesc:  config.ConfigDesc,
		}
		groupMap[config.GroupName] = append(groupMap[config.GroupName], configTree)
	}

	var group []structs.SiteConfigGroup
	for groupName, configs := range groupMap {
		group = append(group, structs.SiteConfigGroup{
			GroupName: groupName,
			Configs:   configs,
		})
	}

	return structs.Response{Code: service_common.CodeSuccess, Data: SiteConfigListResponse{Data: group}}
}

/*
修改站点配置
*/
func (r *SiteConfigUpateRequest) PutSiteConfigUpdate() structs.Response {
	var param []map[string]interface{}

	if len(r.Configs) < 1 {
		return structs.Response{Code: service_common.CodeCommonError, Message: "参数缺失"}
	}

	for _, v := range r.Configs {
		param = append(param, map[string]interface{}{
			"id":           v.ID,
			"config_value": v.ConfigValue,
		})
	}

	sql := mysql.ParseUpdateSql("SiteConfig", param)
	if err := mysql.M.Exec(sql).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: "更新失败"}
	}
	return structs.Response{Code: service_common.CodeSuccess, Message: "ok"}
}
