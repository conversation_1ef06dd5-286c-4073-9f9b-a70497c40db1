package service_admin

import (
	"fmt"
	"trading_tick_server/lib/cache"
	"trading_tick_server/lib/function"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/google_totp"
	"trading_tick_server/lib/mysql"
	"trading_tick_server/lib/structs"
	service_common "trading_tick_server/web/service/common"

	"gorm.io/gorm"
)

type SysUserListRequest struct {
	service_common.CommonListRequest
}

type SysUserAddRequest struct {
	// 账号
	Username string `json:"username" binding:"required,min=6,max=20"`
	// 密码
	Password string `json:"password" binding:"required,min=6,max=20"`
	// 邮箱
	Email string `json:"email" binding:"required,min=6,max=50,email"`
	// 状态 0-1
	Status uint8 `json:"status" binding:"min=0,max=1"`
}

type SysUserUpateRequest struct {
	// ID
	ID uint `json:"id" binding:"required,min=1"`
	// 账号
	Username string `json:"username,omitempty"`
	// 密码 不修改则留空
	Password string `json:"password,omitempty"`
	// 邮箱
	Email string `json:"email,omitempty"`
	// 状态 0-1
	Status uint8 `json:"status" binding:"min=0,max=1"`
}

type SysUserIDRequest struct {
	// ID
	ID uint `json:"id" binding:"required,min=1"`
}

type SysUserIDFRequest struct {
	// 后台用户ID
	ID uint `form:"id" binding:"required,min=1"`
}

type SysUserStatusRequest struct {
	// ID
	ID uint `json:"id" binding:"required,min=1"`
	// 状态 0-1
	Status uint8 `json:"status" binding:"min=0,max=1"`
}

type SysUserRoleSetRequest struct {
	// ID
	ID uint `json:"id" binding:"required,min=1"`
	// 角色ID数组
	RoleIDs []uint `json:"role_ids" binding:"required,min=1,dive,gt=0"`
}

type ResetPasswordRequest struct {
	// 旧密码
	OldPass string `json:"old_pass" binding:"required,min=6,max=20"`
	// 新密码
	NewPass string `json:"new_pass" binding:"required,min=6,max=20"`
	// 验证码
	GoogleCode string `json:"google_code" binding:"required,len=6"`
}

type SysUserRoleListResponse struct {
	// 系统用户已有角色列表
	SysUserRole []structs.SysRole `json:"sys_user_role"`
}

type SysUserListResponse struct {
	// 返回当前页码
	Page int `json:"page"`
	// 返回当前页码数量
	PageSize int `json:"page_size"`
	// 系统用户列表
	List []structs.SysUser `json:"list"`
	// 总数量
	Total int64 `json:"total"`
}

type SysUserAddResponse struct {
	// 返回创建系统用户ID
	ID uint `json:"id"`
}

/*
用户列表
*/
func (r *SysUserListRequest) GetSysUserList() structs.Response {
	if r.Page < 1 {
		r.Page = 1
	}
	if r.PageSize < 1 || r.PageSize > 100 {
		r.PageSize = global.Yaml.WebServer.Admin.DefaultPageSize
	}
	var list []structs.SysUser
	var total int64
	if err := mysql.M.Model(&structs.SysUser{}).Offset((r.Page - 1) * r.PageSize).Limit(r.PageSize).Find(&list).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: err.Error()}
	}
	mysql.M.Model(&structs.SysUser{}).Count(&total)
	return structs.Response{Code: service_common.CodeSuccess, Data: SysUserListResponse{Page: r.Page, PageSize: r.PageSize, List: list, Total: total}}
}

/*
添加用户
*/
func (r *SysUserAddRequest) PostSysUserAdd() structs.Response {
	var param structs.SysUser
	if r.Status == 1 {
		param.Status = 1
	} else {
		param.Status = 0
	}
	param.Email = r.Email
	param.Username = r.Username
	param.Password = function.HashPasswordArgon2(r.Password)
	if err := mysql.M.Create(&param).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: "添加失败"}
	}
	return structs.Response{Code: service_common.CodeSuccess, Message: "ok", Data: SysUserAddResponse{ID: param.ID}}
}

/*
修改用户
*/
func (r *SysUserUpateRequest) PutSysUserUpdate() structs.Response {
	var param structs.SysUser
	param.ID = r.ID
	if err := mysql.M.First(&param, r.ID).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: fmt.Sprintf("无法找到用户: %v", err)}
	}
	slices := []string{"status"}
	if r.Status == 1 {
		param.Status = 1
	} else {
		param.Status = 0
	}

	if r.Email != "" {
		param.Email = r.Email
		slices = append(slices, "email")
	}

	if r.Username != "" {
		param.Username = r.Username
		slices = append(slices, "username")
	}

	if r.Password != "" {
		param.Password = function.HashPasswordArgon2(r.Password)
		slices = append(slices, "password")
	}

	if err := mysql.M.Select(slices).Save(&param).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: "修改失败"}
	}
	return structs.Response{Code: service_common.CodeSuccess, Message: "ok"}
}

/*
删除用户
*/
func (r *SysUserIDRequest) DelSysUserDel() structs.Response {
	// 启用事务删除 未使用联级
	err := mysql.M.Transaction(func(tx *gorm.DB) error {
		var user structs.SysUser

		// 查找用户
		if err := tx.First(&user, r.ID).Error; err != nil {
			return err
		}

		// 删除关联的角色记录
		if err := tx.Model(&user).Association("SysUserRole").Clear(); err != nil {
			return err
		}

		// 删除用户
		if err := tx.Unscoped().Delete(&user).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		fmt.Println(err)
		return structs.Response{Code: service_common.CodeDBError, Message: "删除失败"}
	}

	return structs.Response{Code: service_common.CodeSuccess, Message: "ok"}
}

/*
更改用户状态
*/
func (r *SysUserStatusRequest) PutSysUserStatus() structs.Response {
	var u structs.SysUser
	u.ID = r.ID
	if err := mysql.M.First(&u, r.ID).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: fmt.Sprintf("无法找到用户: %v", err)}
	}
	slices := []string{"status"}
	if r.Status == 1 {
		u.Status = 1
	} else {
		u.Status = 0
	}
	if err := mysql.M.Select(slices).Save(&u).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: "修改失败"}
	}
	return structs.Response{Code: service_common.CodeSuccess, Message: "ok"}
}

/*
重置用户谷歌密钥
*/
func (r *SysUserIDRequest) PutSysUserResetGS() structs.Response {
	var u structs.SysUser
	u.ID = r.ID
	if err := mysql.M.First(&u, r.ID).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: fmt.Sprintf("无法找到用户: %v", err)}
	}

	if err := mysql.M.Select("google_secret").Save(&u).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: "修改失败"}
	}
	return structs.Response{Code: service_common.CodeSuccess, Message: "ok"}
}

/*
用户的角色列表
*/
func (r *SysUserIDFRequest) GetSysUserRoleList() structs.Response {
	var user structs.SysUser

	if err := mysql.M.Preload("SysUserRole", func(db *gorm.DB) *gorm.DB {
		return db.Order("sort_order asc")
	}).First(&user, r.ID).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: err.Error()}
	}
	return structs.Response{Code: service_common.CodeSuccess, Data: SysUserRoleListResponse{SysUserRole: user.SysUserRole}}
}

/*
修改用户角色
*/
func (r *SysUserRoleSetRequest) PutSysUserRoleSet() structs.Response {
	var user structs.SysUser
	err := mysql.M.Transaction(func(tx *gorm.DB) error {
		// 查找用户
		if err := tx.First(&user, r.ID).Error; err != nil {
			return fmt.Errorf("无法找到用户: %v", err)
		}

		// 清空用户的现有角色
		if err := tx.Model(&user).Association("SysUserRole").Clear(); err != nil {
			return fmt.Errorf("清空现有角色失败: %v", err)
		}

		// 根据新的角色ID查找角色并关联
		var newRoles []structs.SysRole
		if err := tx.Where("id IN ?", r.RoleIDs).Find(&newRoles).Error; err != nil {
			return fmt.Errorf("无法找到新角色: %v", err)
		}
		if len(r.RoleIDs) != len(newRoles) {
			return fmt.Errorf("不存在的角色ID")
		}
		fmt.Println("新角色", newRoles)

		// 重新关联新角色
		if err := tx.Model(&user).Association("SysUserRole").Replace(newRoles); err != nil {
			return fmt.Errorf("关联新角色失败: %v", err)
		}

		return nil
	})

	if err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: err.Error()}
	}

	// 清理权限缓存
	cache.SystemUserAuthDel(user.ID)

	return structs.Response{Code: service_common.CodeSuccess, Message: "ok"}
}

/*
重置密码
*/
func (r *ResetPasswordRequest) PostResetPassword(u structs.SysUser) structs.Response {
	if r.OldPass == r.NewPass {
		return structs.Response{Code: service_common.CodeCommonError, Message: "新旧密码不能相同"}
	}
	if ok := google_totp.ValidateCode(u.GoogleSecret, r.GoogleCode); !ok {
		return structs.Response{Code: service_common.CodeCommonError, Message: "谷歌密钥验证失败"}
	}
	if function.HashPasswordArgon2(r.OldPass) != u.Password {
		return structs.Response{Code: service_common.CodeCommonError, Message: "原密码验证失败"}
	}
	// 更新密码
	u.Password = function.HashPasswordArgon2(r.NewPass)
	mysql.M.Select("password").Save(&u)
	// 需要退出重新登录
	err := service_common.ClearToken(u)
	if err != nil {
		return structs.Response{Code: service_common.CodeCacheError, Message: "清理缓存失败"}
	}
	return structs.Response{Code: service_common.CodeSuccess, Message: "ok"}
}
