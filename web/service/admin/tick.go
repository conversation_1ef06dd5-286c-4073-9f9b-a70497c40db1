package service_admin

import (
	"encoding/json"
	"fmt"
	"log"
	"trading_tick_server/lib/cache"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/mysql"
	"trading_tick_server/lib/structs"
	service_common "trading_tick_server/web/service/common"

	"gorm.io/gorm"
)

type TickListRequest struct {
	service_common.CommonListRequest
	// ID
	ID int `form:"id,omitempty"`
	// 产品标识
	Symbol string `form:"symbol,omitempty"`
	// 类型
	Type string `form:"type,omitempty"`
	// 平台
	Platform string `form:"platform,omitempty"`
}

type TickListLogRequest struct {
	service_common.CommonListRequest
	// ID
	ID int `form:"id,omitempty"`
	// 平台
	Platform string `form:"platform,omitempty"`
	// 产品标识
	Symbol string `form:"symbol,omitempty"`
	// 类型 1 上下两条k线产生的误差超过设定范围 2 插针记录 上条为插针改变的 下条为原始未改变的
	LogType uint8 `json:"log_type,omitempty"`
	// 插针ID
	ShadowID uint `json:"shadow_id,omitempty"`
	// 时间分辨率
	Resolutions string `form:"resolutions,omitempty"`
	// 是否为一分钟的开始
	IsOpen bool `form:"is_open,omitempty"`
}

type KlineHistoryRequest struct {
	// 用户ID
	UserID uint `form:"user_id,omitempty"`
	// k线的最大时间
	KlineTimestampEnd int64 `form:"kline_timestamp_end,omitempty"`
	// 产品标识 Symbol
	Code string `form:"code,omitempty"`
	// 数量
	Count int `form:"count,omitempty"`
	// 时间分辨率
	Resolution string `form:"resolution,omitempty"`
	// k线类型 1 三方数据 2 本方数据 3 本方数据+用户插针
	KlineType int `form:"kline_type,omitempty"`
}

type KlineHistoryListResponse struct {
	// K线列表
	List []structs.KlineDataRet `json:"list"`
}

type TickAddRequest struct {
	// 产品名
	Symbolname string `json:"symbolname,omitempty"`
	// 标识
	Symbol string `json:"symbol,omitempty"`
	// 替换标识
	ReplaceSymbol string `json:"replace_symbol,omitempty"`
	// 类型
	Type string `json:"type,omitempty"`
	// 子类型
	SubType string `json:"sub_type,omitempty"`
	// 平台
	Platform string `json:"platform,omitempty"`
	// 状态 0禁用 1启用
	Status uint8 `json:"status" binding:"min=0,max=1"`
	// 最大更新数据量上限
	Maximum uint32 `json:"maximum,omitempty"`
	// 指定最早初始时间
	DesignatedTime int64 `json:"designated_time,omitempty"`
	// 时区
	TimeZone string `json:"time_zone,omitempty"`
	// 默认小数位数
	DefaultDecimalPlaces uint `json:"default_decimal_places,omitempty"`
	// 可选择平台(可用平台)
	AvailablePlatforms []string `json:"available_platforms"`
}

type TickUpateRequest struct {
	// ID
	ID uint `json:"id" binding:"required,min=1"`
	// 产品名
	Symbolname string `json:"symbolname,omitempty"`
	// 标识
	Symbol string `json:"symbol,omitempty"`
	// 替换标识
	ReplaceSymbol string `json:"replace_symbol,omitempty"`
	// 类型
	Type string `json:"type,omitempty"`
	// 子类型
	SubType string `json:"sub_type,omitempty"`
	// 平台
	Platform string `json:"platform,omitempty"`
	// 状态 0禁用 1启用
	Status uint8 `json:"status" binding:"min=0,max=1"`
	// 最大更新数据量上限
	Maximum uint32 `json:"maximum,omitempty"`
	// 指定最早初始时间
	DesignatedTime int64 `json:"designated_time,omitempty"`
	// 时区
	TimeZone string `json:"time_zone,omitempty"`
	// 默认小数位数
	DefaultDecimalPlaces uint `json:"default_decimal_places,omitempty"`
	//
}

type TickResetRequest struct {
}

type TickViewRequest struct {
	// 用户ID
	ID uint `form:"id" binding:"required,min=1"`
}

type TickIDRequest struct {
	// 用户ID
	ID uint `json:"id" binding:"required,min=1"`
}

type TickListResponse struct {
	// 返回当前页码
	Page int `json:"page"`
	// 返回当前页码数量
	PageSize int `json:"page_size"`
	// 产品列表
	List []structs.Tick `json:"list"`
	// 总数量
	Total int64 `json:"total"`
}

type TickLogListResponse struct {
	// 返回当前页码
	Page int `json:"page"`
	// 返回当前页码数量
	PageSize int `json:"page_size"`
	// 产品列表
	List []structs.TickShadowLog `json:"list"`
	// 总数量
	Total int64 `json:"total"`
}

type TickViewResponse struct {
	// 产品详情
	Data structs.Tick `json:"data"`
}

/*
产品列表
*/
func (r *TickListRequest) GetTickList() structs.Response {
	if r.Page < 1 {
		r.Page = 1
	}
	if r.PageSize < 1 || r.PageSize > 100 {
		r.PageSize = global.Yaml.WebServer.Admin.DefaultPageSize
	}

	var queryConditions []interface{}
	query := mysql.M.Model(&structs.Tick{})

	if r.ID > 0 {
		queryConditions = append(queryConditions, "id = ?", r.ID)
	}
	if r.Symbol != "" {
		queryConditions = append(queryConditions, "symbol = ?", r.Symbol)
	}
	if r.Type != "" {
		queryConditions = append(queryConditions, "type = ?", r.Type)
	}

	if len(queryConditions) > 0 {
		query = query.Where(queryConditions[0], queryConditions[1:]...)
	}

	var list []structs.Tick
	var total int64
	query.Count(&total)
	if err := query.Offset((r.Page - 1) * r.PageSize).Limit(r.PageSize).Order("id asc").Find(&list).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: err.Error()}
	}
	return structs.Response{Code: service_common.CodeSuccess, Data: TickListResponse{Page: r.Page, PageSize: r.PageSize, List: list, Total: total}}
}

/*
产品日志列表
*/
func (r *TickListLogRequest) GetTickLogList() structs.Response {
	if r.Page < 1 {
		r.Page = 1
	}
	if r.PageSize < 1 || r.PageSize > 2000 {
		r.PageSize = global.Yaml.WebServer.Admin.DefaultPageSize
	}

	var queryConditions []interface{}
	query := mysql.M.Model(&structs.TickShadowLog{})
	log.Println(r.IsOpen)
	if r.ID > 0 {
		queryConditions = append(queryConditions, "id = ?", r.ID)
	}
	if r.Symbol != "" {
		queryConditions = append(queryConditions, "symbol = ?", r.Symbol)
	}
	if r.Platform != "" {
		queryConditions = append(queryConditions, "platform = ?", r.Platform)
	}
	if r.LogType > 0 {
		queryConditions = append(queryConditions, "log_type = ?", r.LogType)
	}
	if r.ShadowID > 0 {
		queryConditions = append(queryConditions, "shadow_id = ?", r.ShadowID)
	}
	if r.Resolutions != "" {
		queryConditions = append(queryConditions, "resolutions = ?", r.Resolutions)
	}
	if r.IsOpen {
		queryConditions = append(queryConditions, "is_open = ?", r.IsOpen)
	}
	if len(queryConditions) > 0 {
		query = query.Where(queryConditions[0], queryConditions[1:]...)
	}

	var list []structs.TickShadowLog
	var total int64
	var order string
	if r.ShadowID > 0 {
		order = "shadow_id asc"
	} else {
		order = "id DESC"
	}
	if err := query.Offset((r.Page - 1) * r.PageSize).Limit(r.PageSize).Order(order).Find(&list).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: err.Error()}
	}
	query.Count(&total)
	return structs.Response{Code: service_common.CodeSuccess, Data: TickLogListResponse{Page: r.Page, PageSize: r.PageSize, List: list, Total: total}}
}

/*
产品详情列表
*/
func (r *TickViewRequest) GetTickView() structs.Response {
	var param structs.Tick

	if err := mysql.M.First(&param, r.ID).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: "产品不存在"}
	}
	return structs.Response{Code: service_common.CodeSuccess, Data: TickViewResponse{Data: param}}
}

/*
添加产品
*/
func (r *TickAddRequest) PostTickAdd() structs.Response {
	var param structs.Tick
	param.Symbolname = r.Symbolname
	param.Symbol = r.Symbol
	param.ReplaceSymbol = r.ReplaceSymbol
	param.Type = r.Type
	param.SubType = r.SubType
	param.Platform = r.Platform
	param.Status = r.Status
	param.Maximum = r.Maximum
	param.DesignatedTime = r.DesignatedTime
	param.TimeZone = r.TimeZone
	param.DefaultDecimalPlaces = r.DefaultDecimalPlaces
	// 将 []string 转成 JSON 格式的 []byte
	jsonBytes, err := json.Marshal(r.AvailablePlatforms)
	if err != nil {
		panic(err)
	}
	param.AvailablePlatforms = jsonBytes
	// 启用事务
	err = mysql.M.Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(&param).Error; err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: "添加失败"}
	}
	cache.CacheTickSliceSet()
	return structs.Response{Code: service_common.CodeSuccess, Message: "ok"}
}

/*
修改产品
*/
func (r *TickUpateRequest) PutTickUpdate() structs.Response {
	var param structs.Tick
	param.ID = r.ID
	if err := mysql.M.First(&param, r.ID).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: fmt.Sprintf("无法找到产品: %v", err)}
	}
	param.Symbolname = r.Symbolname
	param.Symbol = r.Symbol
	param.ReplaceSymbol = r.ReplaceSymbol
	param.Type = r.Type
	param.SubType = r.SubType
	param.Platform = r.Platform
	param.Status = r.Status
	param.Maximum = r.Maximum
	param.DesignatedTime = r.DesignatedTime
	param.TimeZone = r.TimeZone
	param.DefaultDecimalPlaces = r.DefaultDecimalPlaces
	if err := mysql.M.Save(&param).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: "修改失败"}
	}
	cache.CacheTickSliceSet()
	return structs.Response{Code: service_common.CodeSuccess, Message: "ok"}
}

/*
重置产品缓存
*/
func (r *TickResetRequest) PutTickReset() structs.Response {
	cache.CacheTickSliceSet()
	return structs.Response{Code: service_common.CodeSuccess, Message: "ok"}
}

/*
删除产品
*/
func (r *TickIDRequest) DelTickDel() structs.Response {
	// 启用事务删除 未使用联级
	err := mysql.M.Transaction(func(tx *gorm.DB) error {
		var param structs.Tick

		if err := tx.First(&param, r.ID).Error; err != nil {
			return err
		}

		if err := tx.Unscoped().Delete(&param).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: "删除失败"}
	}
	cache.CacheTickSliceSet()
	return structs.Response{Code: service_common.CodeSuccess, Message: "ok"}
}
