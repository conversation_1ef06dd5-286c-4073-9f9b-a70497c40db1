package service_api

import (
	"sort"
	"strings"
	"time"
	"trading_tick_server/internal/user/user_cache"
	"trading_tick_server/lib/cache"
	"trading_tick_server/lib/decouplingfunction"
	"trading_tick_server/lib/function"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/influxdb"
	"trading_tick_server/lib/logger"
	"trading_tick_server/lib/redis"
	"trading_tick_server/lib/structs"
	service_common "trading_tick_server/web/service/common"

	"github.com/sknun/cf/cast"
	"go.uber.org/zap"
)

type KlineHistoryRequest struct {
	// 用户ID
	UserID uint `form:"user_id,omitempty"`
	// token
	Token string `form:"token,omitempty"`
	// 截止时间
	KlineTimestampEnd int64 `form:"kline_timestamp_end,omitempty"`
	// 产品标识 Symbol
	Code string `form:"code,omitempty"`
	// 数量
	Count int `form:"count,omitempty"`
	// 时间分辨率
	Resolution string `form:"resolution,omitempty"`
}

type KlineHistoryBatchRequest struct {
	// 用户ID
	UserID uint `form:"user_id,omitempty"`
	// token
	Token string `form:"token,omitempty"`
	// 截止时间
	KlineTimestampEnd int64 `form:"kline_timestamp_end,omitempty"`
	// 产品标识 Symbol
	Codes string `form:"codes,omitempty"`
	// 数量
	Count int `form:"count,omitempty"`
	// 时间分辨率
	Resolution string `form:"resolution,omitempty"`
}

type KlineHistoryListResponse struct {
	// 下次取历史k线的时间戳
	NextTimestamp int64 `json:"next_timestamp"`
	// K线列表
	List []structs.KlineDataRet `json:"list"`
}

type KlineHistoryListBatchResponse struct {
	// 产品名
	Symbol string `json:"symbol"`
	// K线列表
	List []structs.KlineDataRet `json:"list"`
}

/*
产品列表
*/
func (r *KlineHistoryRequest) GetKlineHistory() structs.Response {
	u := user_cache.CacheUserGetOne(cast.ToString(r.UserID))
	if u.ID < 1 || u.Token != r.Token {
		return structs.Response{Code: 0, Message: "非法操作"}
	}
	limitTime := time.Now().Truncate(time.Minute).Unix()
	if r.KlineTimestampEnd == 0 || r.KlineTimestampEnd > limitTime {
		r.KlineTimestampEnd = limitTime
	}
	if r.Count < 1 || r.Count > global.KlineSelectLimit {
		r.Count = global.KlineSelectLimit
	}
	// 取出用户支持的k线平台和产品
	platform := ""
	var decimalPlaces uint
	for _, v := range u.UserSymbols {
		if v.Symbol == r.Code && v.Type == "kline" {
			platform = v.Platform
			decimalPlaces = v.DecimalPlaces
			break
		}
	}
	if platform == "" {
		return structs.Response{Code: 0, Message: "未找到产品"}
	}
	if !function.ResolutionIsLegal(r.Resolution) {
		return structs.Response{Code: 0, Message: "非法的时间颗粒"}
	}
	tick := cache.CacheTickGetOne(function.GetPlatformSymbol(platform, r.Code))
	if tick.ID < 1 {
		return structs.Response{Code: 0, Message: "未找到产品2"}
	}
	var tickTimeZone string
	if r.Resolution != global.Resolutions.Minute1 && r.Resolution != global.Resolutions.Minute5 &&
		r.Resolution != global.Resolutions.Minute15 {
		tickTimeZone = tick.TimeZone
		if tickTimeZone == "" {
			return structs.Response{Code: 0, Message: "不支持的时区"}
		}
	}
	// 对 endTime 加一个刻度
	endTime := addTimeIncrement(r.KlineTimestampEnd, r.Resolution)
	endTime = addTimeIncrement(endTime, r.Resolution)
	query := structs.QueryKlineParam{
		Platform:    platform,
		Symbol:      r.Code,
		Resolutions: r.Resolution,
		TimeZone:    tickTimeZone,
		EndTime:     endTime,
		Count:       r.Count,
	}
	d, minTime, maxTime, err := influxdb.GetHistoryKlineData(query)
	//if r.Resolution == global.Resolutions.Month {
	//	global.Lg.Info("取月k线记录", zap.Any("query", query), zap.Any("data", d), zap.Int64("minTime", minTime), zap.Int64("maxTime", maxTime))
	//}
	if err != nil {
		global.Lg.Error(
			"取k线记录失败",
			zap.Any("query", query),
			zap.String("component", "GetKlineHistory"),
			zap.Error(err),
		)
		return structs.Response{Code: 0, Message: "查询失败"}
	}
	// 对 maxTime 加一个刻度
	maxTimeWithOffset := addTimeIncrement(maxTime, r.Resolution)
	maxTimeWithOffset = addTimeIncrement(maxTimeWithOffset, r.Resolution)
	// 查询客户插针
	s := redis.KlineShadowQueryData(cast.ToString(r.UserID), platform, r.Code, tick.TimeZone, r.Resolution, minTime, maxTimeWithOffset)
	// 获取产品的小数位数
	dp := decimalPlaces
	if len(s) > 0 {
		// 使用 map 按 table:time 分组存储插针数据的最高和最低价
		shadowPriceMap := make(map[string]struct {
			High float64
			Low  float64
			Open float64
		})

		// 生成 Redis 表名
		redisTable := decouplingfunction.GetRedisTable(cast.ToString(r.UserID), platform, r.Code, tick.TimeZone, r.Resolution)

		// 收集所有插针数据，按时间分组，只存储最高和最低价
		for _, v2 := range s {
			key := redisTable + ":" + cast.ToString(v2.Time)
			entry := shadowPriceMap[key]

			if v2.Direction == "down" {
				// 更新最低价
				if v2.Low > 0 && (entry.Low == 0 || v2.Low < entry.Low) {
					entry.Low = v2.Low
				}
			} else {
				// 更新最高价
				if v2.High > 0 && (entry.High == 0 || v2.High > entry.High) {
					entry.High = v2.High
				}
			}
			entry.Open = v2.Open
			shadowPriceMap[key] = entry
		}

		// 应用插针数据到K线数据
		for k, v := range d {
			// 先处理 Open/Close（保持原有逻辑）
			for _, v2 := range s {
				if v.Time == v2.Time {
					if v2.UseOpen && v2.UseClose {
						d[k].Open = function.FloorPriceDecimal(v2.Open, int32(dp))
						d[k].Close = function.FloorPriceDecimal(v2.Close, int32(dp))
						d[k].Turnover = v2.Turnover
						d[k].Volume = v2.Volume
						d[k].High = function.FloorPriceDecimal(v2.High, int32(dp))
						d[k].Low = function.FloorPriceDecimal(v2.Low, int32(dp))
						key := redisTable + ":" + cast.ToString(v2.Time)
						if shadowData, exists := shadowPriceMap[key]; exists {
							d[k].Open = function.FloorPriceDecimal(shadowData.Open, int32(dp))
						}
						break
					} else if v2.UseOpen {
						d[k].Open = function.FloorPriceDecimal(v2.Open, int32(dp))
					} else if v2.UseClose {
						d[k].Close = function.FloorPriceDecimal(v2.Close, int32(dp))
					}
					key := redisTable + ":" + cast.ToString(v2.Time)
					if shadowData, exists := shadowPriceMap[key]; exists {
						if shadowData.Low > 0 {
							d[k].Low = min(function.FloorPriceDecimal(shadowData.Low, int32(dp)), d[k].Low)
						}
						if shadowData.High > 0 {
							d[k].High = max(function.FloorPriceDecimal(shadowData.High, int32(dp)), d[k].High)
						}
						if v2.UseOpen {
							d[k].Open = function.FloorPriceDecimal(shadowData.Open, int32(dp))
						}
					}
					break
				}
			}
		}

		// 检查是否有插针数据没有对应的K线数据，如果有则补充
		if len(s) > 0 {
			// 创建K线时间的映射，便于快速查找
			klineTimeMap := make(map[int64]bool)
			for _, kline := range d {
				klineTimeMap[kline.Time] = true
			}

			// 检查插针数据中是否有K线数据中没有的时间点
			for _, shadowData := range s {
				if !klineTimeMap[shadowData.Time] {
					// 找到了缺失的刻度，添加到K线数据中
					newKline := structs.KlineDataRet{
						Time:     shadowData.Time,
						Open:     function.FloorPriceDecimal(shadowData.Open, int32(dp)),
						Close:    function.FloorPriceDecimal(shadowData.Close, int32(dp)),
						High:     function.FloorPriceDecimal(shadowData.High, int32(dp)),
						Low:      function.FloorPriceDecimal(shadowData.Low, int32(dp)),
						Volume:   shadowData.Volume,
						Turnover: shadowData.Turnover,
					}

					// 应用插针价格数据
					key := redisTable + ":" + cast.ToString(shadowData.Time)
					if shadowPriceData, exists := shadowPriceMap[key]; exists {
						if shadowPriceData.Low > 0 {
							newKline.Low = min(function.FloorPriceDecimal(shadowPriceData.Low, int32(dp)), newKline.Low)
						}
						if shadowPriceData.High > 0 {
							newKline.High = max(function.FloorPriceDecimal(shadowPriceData.High, int32(dp)), newKline.High)
						}
					}

					// 添加到K线数据数组
					d = append(d, newKline)
				}
			}

			// 重新按时间排序
			sort.Slice(d, func(i, j int) bool {
				return d[i].Time < d[j].Time
			})
		}
	}
	for k, v := range d {
		if k > 0 {
			if global.Yaml.InfluxDB.LimitDiff > 0 && function.DecimalSubAbs(d[k-1].Close, v.Open) > global.Yaml.InfluxDB.LimitDiff {
				d[k].Open = d[k-1].Close
				logger.View(
					"k线差值太大",
					zap.Any("diff", function.DecimalSubAbs(d[k-1].Close, v.Open)),
					zap.Any("Resolution", r.Resolution),
					zap.Any("Code", r.Code),
				)
			}
		}
	}
	return structs.Response{Code: service_common.CodeSuccess, Data: KlineHistoryListResponse{NextTimestamp: minTime, List: d}}
}

// 根据不同的时间间隔添加相应的时间刻度
func addTimeIncrement(timestamp int64, resolution string) int64 {
	switch resolution {
	case global.Resolutions.Minute1:
		return timestamp + 60 // 加1分钟
	case global.Resolutions.Minute5:
		return timestamp + 300 // 加5分钟
	case global.Resolutions.Minute15:
		return timestamp + 900 // 加15分钟
	case global.Resolutions.Minute30:
		return timestamp + 1800 // 加30分钟
	case global.Resolutions.Hour1:
		return timestamp + 3600 // 加1小时
	case global.Resolutions.Hour4:
		return timestamp + 14400 // 加4小时
	case global.Resolutions.Day:
		return timestamp + 86400 // 加1天
	case global.Resolutions.Week:
		return timestamp + 604800 // 加1周
	case global.Resolutions.Month:
		// 对于月份，使用更精确的计算
		t := time.Unix(timestamp, 0)
		nextMonth := t.AddDate(0, 1, 0)
		return nextMonth.Unix()
	default:
		return timestamp + 60 // 默认加1分钟
	}
}

type KlineHistoryBatch struct {
	Platform      string
	Symbol        string
	TimeZone      string
	Resolutions   string
	EndTime       int64
	Count         int
	DecimalPlaces uint
}

/*
产品列表
*/
func (r *KlineHistoryBatchRequest) GetKlineHistoryBatch() structs.Response {
	u := user_cache.CacheUserGetOne(cast.ToString(r.UserID))
	if u.ID < 1 || u.Token != r.Token {
		return structs.Response{Code: 0, Message: "非法操作"}
	}
	limitTime := time.Now().Truncate(time.Minute).Unix()
	if r.KlineTimestampEnd == 0 || r.KlineTimestampEnd > limitTime {
		r.KlineTimestampEnd = limitTime
	}
	if r.Count < 1 || r.Count > global.KlineSelectLimit {
		r.Count = global.KlineSelectLimit
	}
	// 取出用户支持的k线平台和产品
	var batch []KlineHistoryBatch
	platform := ""
	codes := strings.Split(r.Codes, ",")
	if !function.ResolutionIsLegal(r.Resolution) {
		return structs.Response{Code: 0, Message: "非法的时间颗粒"}
	}
	for _, v := range codes {
		var exist bool
		for _, v2 := range u.UserSymbols {
			if v2.Symbol == v && v2.Type == "kline" {
				exist = true
				var tickTimeZone string
				if r.Resolution != global.Resolutions.Minute1 && r.Resolution != global.Resolutions.Minute5 &&
					r.Resolution != global.Resolutions.Minute15 {
					tick := cache.CacheTickGetOne(function.GetPlatformSymbol(platform, v))
					if tick.ID < 1 {
						return structs.Response{Code: 0, Message: "未找到产品2"}
					}
					tickTimeZone = tick.TimeZone
					if tickTimeZone == "" {
						return structs.Response{Code: 0, Message: "不支持的时区"}
					}
				}
				batch = append(batch, KlineHistoryBatch{
					Platform:      v2.Platform,
					Symbol:        v2.Symbol,
					Resolutions:   r.Resolution,
					TimeZone:      tickTimeZone,
					EndTime:       r.KlineTimestampEnd,
					Count:         r.Count,
					DecimalPlaces: v2.DecimalPlaces,
				})
			}
		}
		if !exist {
			return structs.Response{Code: 0, Message: "未授权的产品"}
		}
	}
	var klineBatch []KlineHistoryListBatchResponse
	for _, v := range batch {
		// 对 endTime 加一个刻度
		endTime := addTimeIncrement(r.KlineTimestampEnd, r.Resolution)
		endTime = addTimeIncrement(endTime, r.Resolution)
		query := structs.QueryKlineParam{
			Platform:    v.Platform,
			Symbol:      v.Symbol,
			Resolutions: r.Resolution,
			TimeZone:    v.TimeZone,
			EndTime:     endTime,
			Count:       r.Count,
		}
		d, minTime, maxTime, err := influxdb.GetHistoryKlineData(query)
		if err != nil {
			global.Lg.Error(
				"取1分钟k线记录失败",
				zap.Any("query", query),
				zap.String("component", "GetKlineHistory"),
				zap.Error(err),
			)
			return structs.Response{Code: 0, Message: "查询失败"}
		}
		// 对 maxTime 加一个刻度
		maxTimeWithOffset := addTimeIncrement(maxTime, r.Resolution)
		maxTimeWithOffset = addTimeIncrement(maxTimeWithOffset, r.Resolution)
		// 查询客户插针
		s := redis.KlineShadowQueryData(cast.ToString(r.UserID), v.Platform, v.Symbol, v.TimeZone, r.Resolution, minTime, maxTimeWithOffset)
		// 获取产品的小数位数
		dp := v.DecimalPlaces
		if len(s) > 0 {
			// 使用 map 按 table:time 分组存储插针数据的最高价、最低价和开盘价
			shadowPriceMap := make(map[string]struct {
				High float64
				Low  float64
				Open float64
			})

			// 生成 Redis 表名
			redisTable := decouplingfunction.GetRedisTable(cast.ToString(r.UserID), v.Platform, v.Symbol, v.TimeZone, r.Resolution)

			// 收集所有插针数据，按时间分组，只存储最高和最低价
			for _, v2 := range s {
				key := redisTable + ":" + cast.ToString(v2.Time)
				entry := shadowPriceMap[key]

				if v2.Direction == "down" {
					// 更新最低价
					if v2.Low > 0 && (entry.Low == 0 || v2.Low < entry.Low) {
						entry.Low = v2.Low
					}
				} else {
					// 更新最高价
					if v2.High > 0 && (entry.High == 0 || v2.High > entry.High) {
						entry.High = v2.High
					}
				}
				entry.Open = v2.Open
				shadowPriceMap[key] = entry
			}

			// 应用插针数据到K线数据
			for k, v := range d {
				// 先处理 Open/Close（保持原有逻辑）
				for _, v2 := range s {
					if v.Time == v2.Time {
						if v2.UseOpen && v2.UseClose {
							d[k].Open = function.FloorPriceDecimal(v2.Open, int32(dp))
							d[k].Close = function.FloorPriceDecimal(v2.Close, int32(dp))
							d[k].Turnover = v2.Turnover
							d[k].Volume = v2.Volume
							d[k].High = function.FloorPriceDecimal(v2.High, int32(dp))
							d[k].Low = function.FloorPriceDecimal(v2.Low, int32(dp))
							key := redisTable + ":" + cast.ToString(v2.Time)
							if shadowData, exists := shadowPriceMap[key]; exists {
								d[k].Open = function.FloorPriceDecimal(shadowData.Open, int32(dp))
							}
							break
						} else if v2.UseOpen {
							d[k].Open = function.FloorPriceDecimal(v2.Open, int32(dp))
						} else if v2.UseClose {
							d[k].Close = function.FloorPriceDecimal(v2.Close, int32(dp))
						}
						key := redisTable + ":" + cast.ToString(v2.Time)
						if shadowData, exists := shadowPriceMap[key]; exists {
							if shadowData.Low > 0 {
								d[k].Low = min(function.FloorPriceDecimal(shadowData.Low, int32(dp)), d[k].Low)
							}
							if shadowData.High > 0 {
								d[k].High = max(function.FloorPriceDecimal(shadowData.High, int32(dp)), d[k].High)
							}
							if v2.UseOpen {
								d[k].Open = function.FloorPriceDecimal(shadowData.Open, int32(dp))
							}
						}
						break
					}
				}
			}

			// 检查是否有插针数据没有对应的K线数据，如果有则补充
			if len(s) > 0 {
				// 创建K线时间的映射，便于快速查找
				klineTimeMap := make(map[int64]bool)
				for _, kline := range d {
					klineTimeMap[kline.Time] = true
				}

				// 检查插针数据中是否有K线数据中没有的时间点
				for _, shadowData := range s {
					if !klineTimeMap[shadowData.Time] {
						// 找到了缺失的刻度，添加到K线数据中
						newKline := structs.KlineDataRet{
							Time:     shadowData.Time,
							Open:     function.FloorPriceDecimal(shadowData.Open, int32(dp)),
							Close:    function.FloorPriceDecimal(shadowData.Close, int32(dp)),
							High:     function.FloorPriceDecimal(shadowData.High, int32(dp)),
							Low:      function.FloorPriceDecimal(shadowData.Low, int32(dp)),
							Volume:   shadowData.Volume,
							Turnover: shadowData.Turnover,
						}

						// 应用插针价格数据
						key := redisTable + ":" + cast.ToString(shadowData.Time)
						if shadowPriceData, exists := shadowPriceMap[key]; exists {
							if shadowPriceData.Low > 0 {
								newKline.Low = min(function.FloorPriceDecimal(shadowPriceData.Low, int32(dp)), newKline.Low)
							}
							if shadowPriceData.High > 0 {
								newKline.High = max(function.FloorPriceDecimal(shadowPriceData.High, int32(dp)), newKline.High)
							}
						}

						// 添加到K线数据数组
						d = append(d, newKline)
					}
				}

				// 重新按时间排序
				sort.Slice(d, func(i, j int) bool {
					return d[i].Time < d[j].Time
				})
			}
		}
		klineBatch = append(klineBatch, KlineHistoryListBatchResponse{
			Symbol: v.Symbol,
			List:   d,
		})
	}

	return structs.Response{Code: service_common.CodeSuccess, Data: klineBatch}
}
