package service_api

import (
	"trading_tick_server/internal/data_transfer_station/service"
	"trading_tick_server/internal/data_transfer_station/types"
	"trading_tick_server/internal/user/user_cache"
	"trading_tick_server/lib/structs"
	ciclientgrpc "trading_tick_server/tickserver/ci_client_grpc"
	service_common "trading_tick_server/web/service/common"

	"github.com/sknun/cf/cast"
)

type KconnectionDeliveryRequest struct {
	// 用户ID
	UserID uint `form:"user_id,omitempty"`
	// token
	Token string `form:"token,omitempty"`
	// 你的服务器地址
	YourSever string `form:"your_sever,omitempty"`
	// 你的验证token
	YourToken string `form:"your_token,omitempty"`
}

func (r *KconnectionDeliveryRequest) GetKconnectionDelivery() structs.Response {
	u := user_cache.CacheUserGetOne(cast.ToString(r.UserID))
	if u.ID < 1 || u.Token != r.Token {
		return structs.Response{Code: 0, Message: "非法操作"}
	}
	if r.YourSever == "" || r.YourToken == "" {
		return structs.Response{Code: 0, Message: "your_sever、your_token参数缺失"}
	}
	err := ciclientgrpc.CiClientGrpc.AddConnection(cast.ToString(u.ID), r.YourSever, r.YourToken)
	if err != nil {
		return structs.Response{Code: 0, Message: "创建连接失败", Error: err.Error()}
	}
	/*
		链接创建成功
		创建该用户的数据传输房间
	*/
	/*
		先看一下是否存在房间
	*/
	_, ok := service.GetStation[types.ExtraDataPkg]().GetUserHub(cast.ToString(r.UserID))
	if !ok {
		service.GetStation[types.ExtraDataPkg]().NewUserHub(cast.ToString(r.UserID), 1)
	} else {
		return structs.Response{Code: service_common.CodeSuccess, Message: "链接成功,该用户已经存在数据传输房间"}
	}
	return structs.Response{Code: service_common.CodeSuccess, Message: "ok"}
}
