package service_api

import (
	ciclientgrpc "trading_tick_server/tickserver/ci_client_grpc"
)

// K线历史数据适配器，实现 gRPC 接口
type KlineHistoryAdapter struct{}

// 实现 KlineHistoryInterface 接口
func (adapter *KlineHistoryAdapter) GetKlineHistory(userID uint, token, symbol, resolution string, count int, timestampEnd int64) (code int, message string, data interface{}) {
	// 创建请求对象
	klineReq := KlineHistoryRequest{
		UserID:            userID,
		Token:             token,
		Code:              symbol,
		Resolution:        resolution,
		Count:             count,
		KlineTimestampEnd: timestampEnd,
	}

	// 调用真实的业务逻辑
	response := klineReq.GetKlineHistory()

	return response.Code, response.Message, response.Data
}

// 初始化函数，设置 gRPC 处理器
func init() {
	// 创建适配器
	adapter := &KlineHistoryAdapter{}

	// 设置到 gRPC 系统中
	ciclientgrpc.SetKlineHistoryHandler(adapter)
}
