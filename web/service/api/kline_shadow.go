package service_api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"strings"
	"time"
	kcc_service "trading_tick_server/internal/kline_control_center/service"
	"trading_tick_server/internal/user/user_cache"
	"trading_tick_server/lib/cache"
	"trading_tick_server/lib/cacheimport"
	"trading_tick_server/lib/function"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/mysql"
	"trading_tick_server/lib/redis"
	"trading_tick_server/lib/structs"
	"trading_tick_server/lib/symbol_price"
	"trading_tick_server/tickserver/ci_client_grpc/operate_func"
	service_common "trading_tick_server/web/service/common"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/shopspring/decimal"
	"github.com/sknun/cf/cast"
	"gorm.io/gorm"
)

type KlineShadowListRequest struct {
	// 用户ID
	UserID uint `form:"user_id,omitempty"`
	// token
	Token string `form:"token,omitempty"`
	service_common.CommonListRequest
}

type KlineShadowAddRequest struct {
	// 用户ID
	UserID uint `json:"user_id" binding:"required"`
	// token
	Token string `json:"token" binding:"required"`
	// 名称
	Symbol string `json:"symbol" binding:"required"`
	// 类型 intuitive fluctuation
	Type string `json:"type" binding:"required"`
	// 插针模式 模式1: 普通模式 模式2: 缩短K线模式
	Mode uint8 `json:"mode" binding:"required,oneof=1 2"`
	// 插针开始时间
	StartTime int64 `json:"start_time" binding:"required"`
	// 爬坡时间(秒)
	RiseTime int `json:"rise_time" binding:"required"`
	// 峰值时间(秒)
	PeakTime int `json:"peak_time" binding:"required"`
	// 回落时间(秒)
	FallTime int `json:"fall_time" binding:"required"`
	// 插针幅度 正数为上插 负数为下插 0为非法
	SpikeFactor string `json:"spike_factor" binding:"required"`
	// 实时k线的close值 intuitive类型下才需要传递
	Candlestick string `json:"candlestick,omitempty"`
	// 买跌浮动值 intuitive类型下才需要传递
	AsksPriceMin string `json:"asks_price_min,omitempty"`
	// 买跌浮动值 intuitive类型下才需要传递
	AsksPriceMax string `json:"asks_price_max,omitempty"`
	// 爬坡期的命中最小值 范围0.01-1之间 对应百分比1%到100%
	RiseHitMin string `json:"rise_hit_min,omitempty"`
	// 爬坡期的命中最大值 范围0.01-1之间 对应百分比1%到100%
	RiseHitMax string `json:"rise_hit_max,omitempty"`
	// 峰值期的命中最小值 范围0.01-1之间 对应百分比1%到100%
	PeakHitMin string `json:"peak_hit_min,omitempty"`
	// 峰值期的命中最大值 范围0.01-1之间 对应百分比1%到100%
	PeakHitMax string `json:"peak_hit_max,omitempty"`
	// 峰值期浮动值, 比如当前峰值价格为10,这个值设定2,则峰值期的价格范围为8-12,每次随机
	PeakFloat string `json:"peak_float,omitempty"`
	// 回落期的命中最小值 范围0.01-1之间 对应百分比1%到100%
	FallHitMin string `json:"fall_hit_min,omitempty"`
	// 回落期的命中最大值 范围0.01-1之间 对应百分比1%到100%
	FallHitMax string `json:"fall_hit_max,omitempty"`
	// 爬坡期的命中后取值范围最小值 最小0.01 无上限
	RiseValueMin string `json:"rise_value_min,omitempty"`
	// 爬坡期的命中后取值范围最大值 最小0.01 无上限
	RiseValueMax string `json:"rise_value_max,omitempty"`
	// 峰值期的命中后取值范围最小值 最小0.01 无上限
	PeakValueMin string `json:"peak_value_min,omitempty"`
	// 峰值期的命中后取值范围最大值 最小0.01 无上限
	PeakValueMax string `json:"peak_value_max,omitempty"`
	// 回落期的命中后取值范围最小值 最小0.01 无上限
	FallValueMin string `json:"fall_value_min,omitempty"`
	// 回落期的命中后取值范围最大值 最小0.01 无上限
	FallValueMax string `json:"fall_value_max,omitempty"`
	// 上影线最大值
	UpperShadowMax string `json:"upper_shadow_max,omitempty"`
	// 下影线最大值
	LowerShadowMax string `json:"lower_shadow_max,omitempty"`
}

type KlineShadowIDRequest struct {
	// 用户ID
	UserID uint `json:"user_id" binding:"required"`
	// token
	Token string `json:"token" binding:"required"`
	// ID
	ID uint `json:"id" binding:"required,min=1"`
	// 是否同时删除插针的k线数据
	DelData uint8 `json:"del_data,omitempty"`
}

type KlineShadowSymbolRequest struct {
	// 用户ID
	UserID uint `json:"user_id" binding:"required"`
	// token
	Token string `json:"token" binding:"required"`
	// 要修复的产品 多个用,分开
	Symbols string `json:"symbols" binding:"required"`
}

type KlineShadowListResponse struct {
	// 返回当前页码
	Page int `json:"page"`
	// 返回当前页码数量
	PageSize int `json:"page_size"`
	// 列表
	List []structs.UserShadow `json:"list"`
	// 总数量
	Total int64 `json:"total"`
}

func (r *KlineShadowListRequest) GetKlineShadowList() structs.Response {
	u := user_cache.CacheUserGetOne(cast.ToString(r.UserID))
	if u.ID < 1 || u.Token != r.Token {
		return structs.Response{Code: 0, Message: "非法操作"}
	}
	if r.Page < 1 {
		r.Page = 1
	}
	if r.PageSize < 1 || r.PageSize > 100 {
		r.PageSize = 100
	}
	var list []structs.UserShadow
	var total int64
	if err := mysql.M.Model(&structs.UserShadow{}).Offset((r.Page-1)*r.PageSize).Where("user_id=?", u.ID).
		Limit(r.PageSize).Order("id desc").Find(&list).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: err.Error()}
	}
	mysql.M.Model(&structs.UserShadow{}).Where("user_id=?", u.ID).Count(&total)
	return structs.Response{Code: service_common.CodeSuccess, Data: KlineShadowListResponse{Page: r.Page, PageSize: r.PageSize, List: list, Total: total}}
}

func (r *KlineShadowAddRequest) PostKlineShadowAdd() structs.Response {
	u := user_cache.CacheUserGetOne(cast.ToString(r.UserID))
	if u.ID < 1 || u.Token != r.Token {
		return structs.Response{Code: 0, Message: "非法操作"}
	}
	var param structs.UserShadow
	param.UserID = r.UserID
	param.Symbol = r.Symbol
	//param.StartTime = r.StartTime
	//使用当前秒级时间戳
	param.StartTime = time.Now().Unix()
	param.RiseTime = r.RiseTime
	param.PeakTime = r.PeakTime
	param.FallTime = r.FallTime
	param.SpikeFactor = r.SpikeFactor
	param.Type = r.Type
	param.Candlestick = r.Candlestick
	param.AsksPriceMin = r.AsksPriceMin
	param.AsksPriceMax = r.AsksPriceMax
	param.RiseHitMin = r.RiseHitMin
	param.RiseHitMax = r.RiseHitMax
	param.PeakHitMin = r.PeakHitMin
	param.PeakHitMax = r.PeakHitMax
	param.FallHitMin = r.FallHitMin
	param.FallHitMax = r.FallHitMax
	param.RiseValueMin = r.RiseValueMin
	param.RiseValueMax = r.RiseValueMax
	param.PeakValueMin = r.PeakValueMin
	param.PeakValueMax = r.PeakValueMax
	// 峰值期浮动值
	param.PeakFloat = r.PeakFloat
	param.FallValueMin = r.FallValueMin
	param.FallValueMax = r.FallValueMax
	param.UpperShadowMax = r.UpperShadowMax
	param.LowerShadowMax = r.LowerShadowMax
	var existSymbol bool
	// 查询哪个平台有当前请求的这个产品的数据
	/*	for _, v := range cache.CacheTickSliceGet() {
		fmt.Printf("🤔这个ChacheTickSliceGet 里面是啥?%v\n", v)
	}*/

	for _, v := range cache.CacheTickSliceGet() {
		if v.Symbol == param.Symbol {
			existSymbol = true
			param.Platform = v.Platform
			break
		}
	}
	if !existSymbol {
		return structs.Response{Code: 0, Message: "产品不存在"}
	}
	if param.SpikeFactor == "0" || param.SpikeFactor == "" {
		return structs.Response{Code: 0, Message: "非法的插针幅度"}
	}
	// 判断提交的价格和最新价格是否有出入
	closePrice := cast.ToString(symbol_price.Get(param.Platform, param.Symbol))
	// 现在不再使用客户端传的价格了,直接使用最新的价格来进行控盘
	if closePrice != "" && closePrice != "0" {
		param.Candlestick = closePrice
	} else {
		return structs.Response{Code: service_common.CodeDBError, Message: fmt.Sprintf("获取最新价格失败, 价格 : %v", closePrice)}
	}

	//fmt.Printf("✅插针提交价格:" + r.Candlestick + ",实时价格:" + closePrice + "\n")
	/*	if closePrice == r.Candlestick {
			global.Lg.Info("✅插针提交价格:" + r.Candlestick + ",实时价格:" + closePrice)
		} else {
			global.Lg.Info("❌插针提交价格:" + r.Candlestick + ",实时价格:" + closePrice)
		}*/
	// 计算有三个期间的完整蜡烛图的数量
	riseCandles, peakCandles, fallCandles := operate_func.CalcCandles(r.StartTime, r.RiseTime, r.PeakTime, r.FallTime)
	if global.Yaml.Logging.AddShadowLog {
		global.Lg.Info("🚀=============== addshadow--插针开始 =============================")
		global.Lg.Info(fmt.Sprintf("addshadow--上升期:%d, 峰值期:%d, 回落期:%d", riseCandles, peakCandles, fallCandles))
	}
	// 计算这些蜡烛图中有哪些需要反向操作
	riseChange := operate_func.MarkBars(riseCandles)
	peakChange := operate_func.MarkBars(peakCandles)
	fallChange := operate_func.MarkBars(fallCandles)
	if global.Yaml.Logging.AddShadowLog {
		global.Lg.Info(fmt.Sprintf("addshadow--上升期修改:%v, 峰值期修改:%v, 回落期修改:%v", riseChange, peakChange, fallChange))
	}
	// 组存储结构 ,三个期间完整的k线数据和是否需要修改
	var riseCandlesInfo, peakCandlesInfo, fallCandlesInfo []structs.CandlesInfo
	for i := 0; i < len(riseCandles); i++ {
		riseCandlesInfo = append(riseCandlesInfo, structs.CandlesInfo{StartTime: riseCandles[i], EndTime: riseCandles[i] + 60, IsChange: riseChange[i]})
	}
	for i := 0; i < len(peakCandles); i++ {
		peakCandlesInfo = append(peakCandlesInfo, structs.CandlesInfo{StartTime: peakCandles[i], EndTime: peakCandles[i] + 60, IsChange: peakChange[i]})
	}
	for i := 0; i < len(fallCandles); i++ {
		fallCandlesInfo = append(fallCandlesInfo, structs.CandlesInfo{StartTime: fallCandles[i], EndTime: fallCandles[i] + 60, IsChange: fallChange[i]})
	}
	// 🚀 核心: 在这里根据插针的信息来计算这次的插针中完整的蜡烛图的信息
	// 包括每个完整的蜡烛图的预测开盘价, 预测收盘价,开盘时间,结束时间,以及是否命中要反向操作
	/*
		数据准备:1. 插针开始价格 decimal 2. 插针幅度 decimal 3. 插针峰值价格 decimal
		4. 1 分钟秒数 decimal 5. 每秒价格改变的值(价格步长) decimal
	*/
	controlStartPrice := decimal.NewFromFloat(cast.ToFloat64(param.Candlestick))
	controlSpikeFactor := decimal.NewFromFloat(cast.ToFloat64(param.SpikeFactor))
	controlPeakPrice := controlStartPrice.Add(controlSpikeFactor)
	OneMinSec := decimal.NewFromInt(60)
	// 计算每秒应该爬升多少(步长) = Spike Factor 插针幅度(要涨多少点) / 爬坡时间
	risePricePerSec := decimal.NewFromFloat(cast.ToFloat64(param.SpikeFactor)).Div(decimal.NewFromInt(int64(param.RiseTime)))
	if global.Yaml.Logging.AddShadowLog {
		global.Lg.Info(fmt.Sprintf("addshadow--插针开始时间: %v, 上升期时间: %v, 峰值期时间: %v, 回落期时间: %v \n", param.StartTime, param.RiseTime, param.PeakTime, param.FallTime))
		global.Lg.Info(fmt.Sprintf("addshadow--每秒价格改变的值(价格步长): %v, 插针模式: %v \n", risePricePerSec, r.Mode))
		global.Lg.Info(fmt.Sprintf("addshadow--60 * 步长 * 2.5 =  %v \n", risePricePerSec.Mul(OneMinSec).Mul(decimal.NewFromFloat(2.5))))
		global.Lg.Info(fmt.Sprintf("addshadow--30 * 步长 =  %v \n", risePricePerSec.Mul(decimal.NewFromInt(30))))
		global.Lg.Info(fmt.Sprintf("addshadow--插针起始价格: %v, 插针幅度: %v, 插针峰值价格: %v \n", param.Candlestick, param.SpikeFactor, controlStartPrice.Add(controlSpikeFactor)))
	}
	// 📈爬坡期的计算
	for riseCandleK, riseCandleV := range riseCandlesInfo {
		// 是否是第一根完整的K线
		if riseCandleK == 0 {
			// ✅计算原始线性开盘价格 = (这条k线的开始时间 - 插针开始时间) * 价格步长
			passedTime := riseCandleV.StartTime - param.StartTime                                                                                  // 插针开始经过的时间
			riseCandlesInfo[riseCandleK].DefaultOpenPrice = controlStartPrice.Add(risePricePerSec.Mul(decimal.NewFromInt(passedTime))).Truncate(3) // 开盘价 = 插针开始价格+价格步长 * 经过的时间
			// 调整开盘价格
			// 开盘价调整,开盘价格上下浮动一个范围暂时设定 10秒* 价格步长的时间浮动范围
			// 最高开盘价 = 开盘价 + 30 * 价格步长
			maxDefaultOpenPrice := riseCandlesInfo[riseCandleK].DefaultOpenPrice.Add(risePricePerSec.Mul(decimal.NewFromInt(global.Yaml.Kline.FluctuateStepSize)))
			// 最低开盘价 = 开盘价 - 30 * 价格步长
			// minDefaultOpenPrice := riseCandlesInfo[riseCandleK].DefaultOpenPrice.Sub(risePricePerSec.Mul(decimal.NewFromInt(global.Yaml.Kline.FluctuateStepSize)))
			// 第一根完整K线的最低开盘价就是预期价格,不然会导致比插针开盘价还低的情况出现
			minDefaultOpenPrice := riseCandlesInfo[riseCandleK].DefaultOpenPrice
			// 随机一个开盘价
			riseCandlesInfo[riseCandleK].DefaultOpenPrice = function.ShouldHitValueDecimal(minDefaultOpenPrice, maxDefaultOpenPrice).Truncate(3)

			// ✅计算收盘价格
			// 判断是否需要反向操作
			if riseCandleV.IsChange { // 需要反向操作
				// 计算反向操作的价格 = 开盘价格 -  步长* 30(秒)
				riseCandlesInfo[riseCandleK].DefaultClosePrice = riseCandlesInfo[riseCandleK].DefaultOpenPrice.Sub(risePricePerSec.Mul(decimal.NewFromInt(global.Yaml.Kline.FluctuateStepSize))).Truncate(3)
				// 收盘价调整,收盘价格上下浮动一个范围暂时设定 10秒* 价格步长的时间浮动范围
				// 最低收盘价 = 收盘价 - 10 * 价格步长
				maxDefaultClosePrice := riseCandlesInfo[riseCandleK].DefaultClosePrice.Add(risePricePerSec.Mul(decimal.NewFromInt(global.Yaml.Kline.FluctuateStepSize)))
				// 最高收盘价 = 收盘价 + 10 * 价格步长
				minDefaultClosePrice := riseCandlesInfo[riseCandleK].DefaultClosePrice.Sub(risePricePerSec.Mul(decimal.NewFromInt(global.Yaml.Kline.FluctuateStepSize)))
				// 随机一个收盘价
				riseCandlesInfo[riseCandleK].DefaultClosePrice = function.ShouldHitValueDecimal(minDefaultClosePrice, maxDefaultClosePrice).Truncate(3)
			} else { // 不需要反向操作
				// 计算原始线性收盘价格 =  开盘价格 +( 60 * 价格步长)
				riseCandlesInfo[riseCandleK].DefaultClosePrice = riseCandlesInfo[riseCandleK].DefaultOpenPrice.Add(risePricePerSec.Mul(decimal.NewFromInt(60))).Truncate(3)
				// 收盘价调整,收盘价格上下浮动一个范围暂时设定 10秒* 价格步长的时间浮动范围
				// 最低收盘价 = 收盘价 - 设定步长 * 价格步长
				maxDefaultClosePrice := riseCandlesInfo[riseCandleK].DefaultClosePrice.Add(risePricePerSec.Mul(decimal.NewFromInt(global.Yaml.Kline.FluctuateStepSize)))
				// 最高收盘价 = 收盘价 + 设定步长 * 价格步长
				minDefaultClosePrice := riseCandlesInfo[riseCandleK].DefaultClosePrice.Sub(risePricePerSec.Mul(decimal.NewFromInt(global.Yaml.Kline.FluctuateStepSize)))
				// 随机一个收盘价
				riseCandlesInfo[riseCandleK].DefaultClosePrice = function.ShouldHitValueDecimal(minDefaultClosePrice, maxDefaultClosePrice).Truncate(3)
				/*	if riseCandlesInfo[riseCandleK].DefaultOpenPrice.LessThanOrEqual(riseCandlesInfo[riseCandleK].DefaultClosePrice) {
					fmt.Printf("第一根完整K线之前在 - 控盘的情况下,没有反拉,close 价格应该低于 open, 但是出现了 open 小于 close 的情况, open : %v, close : %v\n", riseCandlesInfo[riseCandleK].DefaultOpenPrice, riseCandlesInfo[riseCandleK].DefaultClosePrice)
				}*/
			}
		} else { // 不是第一根完整的K线的情况
			// ✅计算原始线性开盘价格 = 上一根完整K线的收盘价
			riseCandlesInfo[riseCandleK].DefaultOpenPrice = riseCandlesInfo[riseCandleK-1].DefaultClosePrice

			// 判断是否需要反向操作
			if riseCandleV.IsChange { // 需要反向操作
				if global.Yaml.Logging.AddShadowLog {
					global.Lg.Info("addshadow--❌反向操作日志 ====================")
				}
				// 计算反向操作的价格 = 开盘价格 -  步长* 30(秒)
				riseCandlesInfo[riseCandleK].DefaultClosePrice = riseCandlesInfo[riseCandleK].DefaultOpenPrice.Sub(risePricePerSec.Mul(decimal.NewFromInt(global.Yaml.Kline.FluctuateStepSize))).Truncate(3)
				if global.Yaml.Logging.AddShadowLog {
					global.Lg.Info(fmt.Sprintf("addshadow--当前K线 %v号K线, 原始预计收盘价格为 : %v", riseCandleK+1, riseCandlesInfo[riseCandleK].DefaultClosePrice))
				}
				// 收盘价调整,收盘价格上下浮动一个范围暂时设定 10秒* 价格步长的时间浮动范围
				// 最高收盘价 = 收盘价 + 28 * 价格步长(也就是说最高收盘价就跌两个步长,这样保证肯定会跌)
				maxDefaultClosePrice := riseCandlesInfo[riseCandleK].DefaultClosePrice.Add(risePricePerSec.Mul(decimal.NewFromInt(28)))
				// 最低收盘价 = 收盘价 - 10 * 价格步长(这样保证不会跌的太多,给后面爬升带来压力)
				minDefaultClosePrice := riseCandlesInfo[riseCandleK].DefaultClosePrice.Sub(risePricePerSec.Mul(decimal.NewFromInt(10)))
				if global.Yaml.Logging.AddShadowLog {
					global.Lg.Info(fmt.Sprintf("addshadow--随机范围为: 最低收盘价: %v, 最高收盘价: %v ", minDefaultClosePrice, maxDefaultClosePrice))
				}
				// 随机一个收盘价
				riseCandlesInfo[riseCandleK].DefaultClosePrice = function.ShouldHitValueDecimal(minDefaultClosePrice, maxDefaultClosePrice).Truncate(3)
			} else { // 不需要反向操作
				// 计算原始线性收盘价格(这根k线按照线性预期应该结束的价格) = (这条K线的收盘时间 - 插针开始时间) * 价格步长 + 开盘价格
				// 经过的时间
				if global.Yaml.Logging.AddShadowLog {
					global.Lg.Info("addshadow--✅正向操作日志 ====================")
				}
				passedTime := riseCandleV.EndTime - param.StartTime
				// 计算收盘价格(预计值)
				riseCandlesInfo[riseCandleK].DefaultClosePrice = controlStartPrice.Add(risePricePerSec.Mul(decimal.NewFromInt(passedTime))).Truncate(3)
				if global.Yaml.Logging.AddShadowLog {
					global.Lg.Info(fmt.Sprintf("addshadow--当前K线 %v号K线, 原始预计收盘价格为 : %v", riseCandleK+1, riseCandlesInfo[riseCandleK].DefaultClosePrice))
					// 如果插针模式是 2 并且预计收盘价格高于前一根完整K线的收盘价格的 60 * 步长 * 2.5(也就是说这根K线可能处于前两根完整反向K线的后面那么这时需要缩小一下这根K线的长度)
					global.Lg.Info(fmt.Sprintf("addshadow--差值计算 : %v", riseCandlesInfo[riseCandleK].DefaultClosePrice.Sub(riseCandlesInfo[riseCandleK-1].DefaultClosePrice)))
				}

				// 现在 meta trader v1 后台都是 2
				// 就是这个 if r.Mode 2 而导致的在没有反拉的时候价格依然会不正确
				/*	if r.Mode == 2 && riseCandlesInfo[riseCandleK].DefaultClosePrice.
					Sub(riseCandlesInfo[riseCandleK-1].DefaultClosePrice).
					GreaterThan(risePricePerSec.Mul(OneMinSec).Mul(decimal.NewFromFloat(2.5))) {
					// 先缩小 60 秒的步长试一试,看看客户反应
					if global.Yaml.Logging.AddShadowLog {
						global.Lg.Info(fmt.Sprintf("进入模式 2 调整, 上一根K线收盘价: %v, 当前K线收盘价: %v", riseCandlesInfo[riseCandleK-1].DefaultClosePrice, riseCandlesInfo[riseCandleK].DefaultClosePrice))
					}
					riseCandlesInfo[riseCandleK].DefaultClosePrice = riseCandlesInfo[riseCandleK].DefaultClosePrice.Sub(risePricePerSec.Mul(decimal.NewFromFloat(60 * 2.5)))
					if global.Yaml.Logging.AddShadowLog {
						global.Lg.Info(fmt.Sprintf("addshadow--调整后当前K线收盘价: %v", riseCandlesInfo[riseCandleK].DefaultClosePrice))
					}
				}*/
				// 收盘价调整,收盘价格上下浮动一个范围暂时设定 10秒* 价格步长的时间浮动范围
				// 最低收盘价 = 收盘价 - 30 * 价格步长
				maxDefaultClosePrice := riseCandlesInfo[riseCandleK].DefaultClosePrice.Add(risePricePerSec.Mul(decimal.NewFromInt(global.Yaml.Kline.FluctuateStepSize)))
				// 最高收盘价 = 收盘价 + 30 * 价格步长
				minDefaultClosePrice := riseCandlesInfo[riseCandleK].DefaultClosePrice.Sub(risePricePerSec.Mul(decimal.NewFromInt(global.Yaml.Kline.FluctuateStepSize)))
				if global.Yaml.Logging.AddShadowLog {
					global.Lg.Info(fmt.Sprintf("addshadow--设定最高价和最低价的配置步数为, : %v, 价格为 : %v", global.Yaml.Kline.FluctuateStepSize, risePricePerSec.Mul(decimal.NewFromInt(global.Yaml.Kline.FluctuateStepSize))))
					global.Lg.Info(fmt.Sprintf("addshadow--最低收盘价: %v, 最高收盘价: %v , 价差: %v", minDefaultClosePrice, maxDefaultClosePrice, maxDefaultClosePrice.Sub(minDefaultClosePrice)))
				}
				// 随机一个收盘价
				randomClosePrice := function.ShouldHitValueDecimal(minDefaultClosePrice, maxDefaultClosePrice).Truncate(3)
				if global.Yaml.Logging.AddShadowLog {
					global.Lg.Info(fmt.Sprintf("addshadow--随机收盘价: %v", randomClosePrice))
				}
				// 如果是正向控制,并且这个收盘价高于峰值,那么就取峰值, 如果是负向控制,并且这个收盘价低于峰值,那么就取峰值
				if (randomClosePrice.GreaterThan(controlPeakPrice) && controlSpikeFactor.GreaterThan(decimal.Zero)) ||
					(randomClosePrice.LessThan(controlPeakPrice) && controlSpikeFactor.LessThan(decimal.Zero)) {
					riseCandlesInfo[riseCandleK].DefaultClosePrice = controlPeakPrice
				} else {
					riseCandlesInfo[riseCandleK].DefaultClosePrice = randomClosePrice
				}

				/*	if riseCandlesInfo[riseCandleK].DefaultOpenPrice.LessThanOrEqual(riseCandlesInfo[riseCandleK].DefaultClosePrice) {
					fmt.Printf("❌在 - 控盘的情况下,没有反拉,close 价格应该低于 open, 但是出现了 open 小于 close 的情况,K线 ID :%v, open : %v, close : %v\n", riseCandleK, riseCandlesInfo[riseCandleK].DefaultOpenPrice, riseCandlesInfo[riseCandleK].DefaultClosePrice)
				}*/
			}
		}
		if global.Yaml.Logging.AddShadowLog {
			global.Lg.Info(fmt.Sprintf("addshadow--爬坡期第%d根完整K线: 开盘价:%s, 收盘价:%s, 开盘时间:%d, 结束时间:%d, 是否需要反向操作:%v\n\n\n", riseCandleK+1, riseCandlesInfo[riseCandleK].DefaultOpenPrice, riseCandlesInfo[riseCandleK].DefaultClosePrice, riseCandleV.StartTime, riseCandleV.EndTime, riseCandleV.IsChange))
		}
	}

	// 📉回落期的计算
	// 计算回落期步长(每一秒价格改变的值,涨或跌) = 插针幅度/回落时间
	fallPricePerSec := decimal.NewFromFloat(cast.ToFloat64(param.SpikeFactor)).Div(decimal.NewFromInt(int64(param.FallTime)))
	for fallCandleK, fallCandleV := range fallCandlesInfo { // 回落期只计算反向拉的K线
		if fallCandleV.IsChange { // 如果这根完整的K线需要反向拉
			// 计算这根K线的起始时间和结束时间
			fallCandleV.EndTime = fallCandleV.StartTime + 60
			// 如果这根k线要反向操作,那么这根k线的开盘价就是上一根完整K线的收盘价,而收盘价格可以取 峰值上下浮动的一个点
			// 计算浮动范围最低点
			minPrice := decimal.Zero
			// 计算浮动范围最高点
			maxPrice := fallPricePerSec.Mul(decimal.NewFromInt(50))
			// 范围中随机一个点作为这条反向k线的close 价格的 - 值
			// 也就是说当使用的时候需要用上一条k线的收盘价 + 这个值 做为这条k线的收盘价格
			v := function.ShouldHitValueDecimal(minPrice, maxPrice)
			if global.Yaml.Logging.AddShadowLog {
				global.Lg.Info(fmt.Sprintf("addshadow--小 %v", minPrice))
				global.Lg.Info(fmt.Sprintf("addshadow--大 %v", maxPrice))
				global.Lg.Info(fmt.Sprintf("addshadow--😄 %v", v))
			}
			fallCandlesInfo[fallCandleK].DefaultClosePrice = v
			if global.Yaml.Logging.AddShadowLog {
				global.Lg.Info(fmt.Sprintf("addshadow--😍 %v", fallCandlesInfo[fallCandleK].DefaultClosePrice))
			}
		}
		if global.Yaml.Logging.AddShadowLog {
			global.Lg.Info(fmt.Sprintf("addshadow--回落期间第%d根完整K线: 开盘价:%s, 收盘价:%s, 开盘时间:%d, 结束时间:%d, 是否需要反向操作:%v",
				fallCandleK+1,
				fallCandlesInfo[fallCandleK].DefaultOpenPrice,
				fallCandlesInfo[fallCandleK].DefaultClosePrice,
				fallCandleV.StartTime,
				fallCandleV.EndTime,
				fallCandleV.IsChange))
		}
	}

	param.RiseCandlesInfo, _ = json.Marshal(riseCandlesInfo)
	param.PeakCandlesInfo, _ = json.Marshal(peakCandlesInfo)
	param.FallCandlesInfo, _ = json.Marshal(fallCandlesInfo)

	// 启用事务 改用表锁 LOCK TABLES my_table READ; UNLOCK TABLES;
	table := mysql.GetFullTable("UserShadow")
	var shadow structs.UserShadow
	errTx := mysql.M.Transaction(func(tx *gorm.DB) error {
		// 加表锁
		if err := tx.Exec("LOCK TABLES " + table + " write;").Error; err != nil {
			return errors.New("锁表失败")
		}
		// 读取数据
		tx.Table(table).Where("user_id=? AND symbol=? AND status=0", u.ID, param.Symbol).Order("start_time DESC").Find(&shadow)
		if shadow.ID > 0 {
			newStartTime := shadow.StartTime + int64(shadow.RiseTime) + int64(shadow.PeakTime) + int64(shadow.FallTime)
			log.Println(newStartTime, param.StartTime)
			if newStartTime >= param.StartTime {
				if err := tx.Exec("UNLOCK TABLES;").Error; err != nil {
					return errors.New("解锁失败")
				}
				return errors.New("时间范围内有操作存在")
			}
		}
		// 计算1m的开始和结束时间线
		param.EndTime = param.StartTime + int64(param.RiseTime) + int64(param.PeakTime) + int64(param.FallTime)
		param.ShadowStartTime = function.ZeroSecondOfTimestamp(param.StartTime)
		param.ShadowEndTime = function.ZeroSecondOfTimestamp(param.StartTime + int64(param.RiseTime) + int64(param.PeakTime) + int64(param.FallTime))
		if err := tx.Create(&param).Error; err != nil {
			if err := tx.Exec("UNLOCK TABLES;").Error; err != nil {
				return errors.New("解锁失败")
			}
			return errors.New("添加失败")
		}
		// 执行插针
		if err := function.KlineShadowOperate(param); err != nil {
			if err := tx.Exec("UNLOCK TABLES;").Error; err != nil {
				return errors.New("解锁失败")
			}
			return errors.New("添加到缓存失败")
		}
		// 没有错误在更新后解锁
		if err := tx.Exec("UNLOCK TABLES;").Error; err != nil {
			return errors.New("解锁失败")
		}
		return nil
	})

	if errTx != nil {
		return structs.Response{Code: 0, Message: errTx.Error()}
	}

	/*	fmt.Printf("插针开始预设K线情况, 上升期: %v\n", riseCandlesInfo)
		fmt.Printf("插针峰值预设K线情况, 峰值期: %v\n", peakCandlesInfo)
		fmt.Printf("插针回落预设K线情况, 回落期: %v\n", fallCandlesInfo)*/

	return structs.Response{
		Code: service_common.CodeSuccess,
		Data: gin.H{
			"id":         param.ID,
			"start_time": param.StartTime,
			"end_time":   param.EndTime,
		},
		Message: "ok"}
}

func (r *KlineShadowIDRequest) DelKlineShadowDel() structs.Response {
	u := user_cache.CacheUserGetOne(cast.ToString(r.UserID))
	if u.ID < 1 || u.Token != r.Token {
		return structs.Response{Code: 0, Message: "非法操作"}
	}
	var param structs.UserShadow
	// 数据库软删除
	err := mysql.M.Transaction(func(tx *gorm.DB) error {
		if err := tx.Where("user_id=?", r.UserID).Find(&param, r.ID).Error; err != nil {
			return err
		}
		if err := tx.Delete(&param).Error; err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: "删除失败"}
	}
	// 取出时区
	var timeZone string
	for _, v := range cache.CacheTickSliceGet() {
		if v.Platform == param.Platform && v.Symbol == param.Symbol {
			timeZone = v.TimeZone
		}
	}
	fmt.Printf("⏰删除控盘时获取到的时区: %v\n", timeZone)
	// 删除缓存操作
	function.KlineShadowOperateDel(param)
	if r.DelData == 1 {
		go func() {
			// 这他妈什么👻代码
			time.Sleep(time.Second * 1)
			mysql.M.Where("shadow_id=?", param.ID).Delete(&structs.TickShadowLog{})
			redis.KlineShadowDeleteData(param.ID, cast.ToString(r.UserID), param.Platform, param.Symbol, timeZone, param.StartTime, param.EndTime)
			time.Sleep(time.Second * 2)
			mysql.M.Where("shadow_id=?", param.ID).Delete(&structs.TickShadowLog{})
			redis.KlineShadowDeleteData(param.ID, cast.ToString(r.UserID), param.Platform, param.Symbol, timeZone, param.StartTime, param.EndTime)
		}()
	}
	return structs.Response{Code: service_common.CodeSuccess, Message: "ok"}
}

func (r *KlineShadowSymbolRequest) DelKlineShadowFix() structs.Response {
	u := user_cache.CacheUserGetOne(cast.ToString(r.UserID))
	if u.ID < 1 || u.Token != r.Token {
		return structs.Response{Code: 0, Message: "非法操作"}
	}

	symbols := strings.Split(r.Symbols, ",")
	if len(symbols) < 1 {
		return structs.Response{Code: 0, Message: "未提交任何产品"}
	}

	// 使用事务保证原子性
	err := mysql.M.Transaction(func(tx *gorm.DB) error {
		// 第1步：删除立即模式和渐变模式控制历史
		err := kcc_service.DelControlHistoryAll(context.Background(), cast.ToString(r.UserID), r.Symbols)
		if err != nil {
			return fmt.Errorf("删除控制历史失败: %w", err)
		}

		// 第2步：在事务中删除数据库记录
		for _, v := range symbols {
			// 删除插针日志表
			if err := tx.Where("user_id=? AND symbol=?", r.UserID, v).Unscoped().Delete(&structs.TickShadowLog{}).Error; err != nil {
				return fmt.Errorf("删除插针日志失败 symbol=%s: %w", v, err)
			}

			// 删除用户插针表
			if err := tx.Where("user_id=? AND symbol=?", r.UserID, v).Unscoped().Delete(&structs.UserShadow{}).Error; err != nil {
				return fmt.Errorf("删除用户插针失败 symbol=%s: %w", v, err)
			}
		}

		return nil
	})

	if err != nil {
		global.Lg.Error("DelKlineShadowFix 事务失败", zap.Error(err))
		return structs.Response{Code: service_common.CodeDBError, Message: "删除失败: " + err.Error()}
	}

	// 第3步：事务成功后，清理缓存和Redis
	for _, v := range symbols {
		// 删除Redis缓存
		if err := cacheimport.UserKlineShadowDelKey(cast.ToString(r.UserID), v); err != nil {
			global.Lg.Warn("删除插针缓存失败", zap.String("userID", cast.ToString(r.UserID)), zap.String("symbol", v), zap.Error(err))
		}

		// 删除Redis中的所有K线数据
		for _, vKlineSymbol := range u.UserSymbols {
			if v == vKlineSymbol.Symbol && vKlineSymbol.Type == "kline" {
				// 取出时区
				var timeZone string
				for _, vTick := range cache.CacheTickSliceGet() {
					if vTick.Platform == vKlineSymbol.Platform && vTick.Symbol == v {
						timeZone = vTick.TimeZone
						break
					}
				}
				// 同步删除Redis数据
				func() {
					defer func() {
						if panicErr := recover(); panicErr != nil {
							global.Lg.Warn("删除Redis K线数据发生panic",
								zap.String("userID", cast.ToString(r.UserID)),
								zap.String("platform", vKlineSymbol.Platform),
								zap.String("symbol", v),
								zap.Any("panic", panicErr))
						}
					}()
					redis.KlineShadowDeleteAll(cast.ToString(r.UserID), vKlineSymbol.Platform, v, timeZone)
				}()
				break
			}
		}
	}

	return structs.Response{Code: service_common.CodeSuccess, Message: "ok"}
}
