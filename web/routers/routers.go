package routers

import (
	_ "trading_tick_server/docs"
	"trading_tick_server/internal/base_data"
	kcc_controller "trading_tick_server/internal/kline_control_center/controller"
	koi_controller "trading_tick_server/internal/kline_offset_immediate/controller"
	kol_controller "trading_tick_server/internal/kline_offset_linear/controller"
	user_controller "trading_tick_server/internal/user/controller"
	"trading_tick_server/lib/global"
	controllers_admin "trading_tick_server/web/controllers/admin"
	controllers_api "trading_tick_server/web/controllers/api"
	"trading_tick_server/web/middlewares"

	"github.com/gin-gonic/gin"
	httpSwagger "github.com/swaggo/http-swagger"
)

func Run() *gin.Engine {
	gin.SetMode(global.Yaml.WebServer.Mode)
	router := gin.New()

	router.Static(global.Yaml.WebServer.LocalPath, global.PathUploads)

	router.Use(middlewares.Cors(), middlewares.Logger(), middlewares.Recovery())

	router.GET("/swagger/*any", gin.WrapH(httpSwagger.WrapHandler))

	// Admin routes
	admin := router.Group("/admin")
	{
		admin.POST("/login", controllers_admin.PostLogin)
		admin.PUT("/tick_reset", controllers_admin.PutTickReset)
		admin.GET("/tick_log", controllers_admin.GetTickLogList)
		admin.GET("/kline_history", controllers_admin.GetKlineHistory)
		admin.Use(middlewares.AdminAuth(), middlewares.AdminLog(), middlewares.WrapWithAOP())
		{
			// 个人操作
			admin.GET("/logout", controllers_admin.GetLogout)
			admin.GET("/build_google_totp", controllers_admin.GetBuildGoogleTotp)
			admin.POST("/build_google_totp", controllers_admin.PostBuildGoogleTotp)
			admin.POST("/verify_google_totp", controllers_admin.PostVerifyGoogleTotp)
			admin.POST("/reset_password", controllers_admin.PostResetPassword)
			// 上传
			admin.POST("/upload_file", controllers_admin.PostUploadFile)
			admin.POST("/upload_image", controllers_admin.PostUploadImage)

			// 后台系统用户管理
			system := admin.Group("/system")
			{
				// 系统用户
				system.GET("/user", controllers_admin.GetSysUserList)
				system.POST("/user", controllers_admin.PostSysUserAdd)
				system.PUT("/user", controllers_admin.PutSysUserUpdate)
				system.DELETE("/user", controllers_admin.DelSysUserDel)
				system.PUT("/user/status", controllers_admin.PutSysUserStatus)
				system.PUT("/user/reset_gs", controllers_admin.PutSysUserResetGS)
				system.GET("/user/role_list", controllers_admin.GetSysUserRoleList)
				system.PUT("/user/role_set", controllers_admin.PutSysUserRoleSet)

				// 角色
				system.GET("/role", controllers_admin.GetSysRoleList)
				system.POST("/role", controllers_admin.PostSysRoleAdd)
				system.PUT("/role", controllers_admin.PutSysRoleUpdate)
				system.DELETE("/role", controllers_admin.DelSysRoleDel)
				system.GET("/role/perm_list", controllers_admin.GetSysRolePermList)
				system.PUT("/role/perm_set", controllers_admin.PutSysRolePermSet)

				// 权限
				system.GET("/perm", controllers_admin.GetSysPermList)
				system.POST("/perm", controllers_admin.PostSysPermAdd)
				system.PUT("/perm", controllers_admin.PutSysPermUpdate)
				system.DELETE("/perm", controllers_admin.DelSysPermDel)

				// 站点配置
				system.GET("/siteconfig", controllers_admin.GetSiteConfigList)
				system.PUT("/siteconfig", controllers_admin.PutSiteConfigUpdate)
				// 内置操作
				system.PUT("/operations", controllers_admin.PutOperations)
			}

			// 用户管理
			users := admin.Group("/users")
			{
				users.GET("/users", controllers_admin.GetUserList)
				users.GET("/view", controllers_admin.GetUserView) // 用户详情
				users.POST("/users", controllers_admin.PostUserAdd)
				users.PUT("/users", user_controller.PutUser) // 修改用户信息
				users.DELETE("/users", controllers_admin.DelUserDel)
				users.GET("/symbol_list", controllers_admin.GetUserSymbolList) // 获取用户Symbol列表
				users.PUT("/symbol_edit", controllers_admin.PutUserSymbolEdit) // 编辑用户Symbol
			}

			// 产品管理
			tick := admin.Group("/tick")
			{
				tick.GET("/tick", controllers_admin.GetTickList)
				tick.GET("/view", controllers_admin.GetTickView)  // 产品详情
				tick.POST("/tick", controllers_admin.PostTickAdd) // 添加产品
				tick.PUT("/tick", controllers_admin.PutTickUpdate)
				tick.DELETE("/tick", controllers_admin.DelTickDel)
			}
			// 全局数据
			data := admin.Group("/data")
			{
				data.GET("", base_data.GetData)
				// 历史k线获取
				data.GET("/kline_history", controllers_api.GetKlineHistory)
				data.GET("/kline_history_batch", controllers_api.GetKlineHistoryBatch)
			}
		}
	}
	// api routes
	api := router.Group("/api")
	{
		// 历史k线获取
		api.GET("/kline_history", controllers_api.GetKlineHistory)
		api.GET("/kline_history_batch", controllers_api.GetKlineHistoryBatch)
		// k线插针
		api.GET("/kline_shadow", controllers_api.GetKlineShadowList)
		api.POST("/kline_shadow", controllers_api.PostKlineShadowAdd)
		api.DELETE("/kline_shadow", controllers_api.DelKlineShadowDel)
		api.DELETE("/kline_shadow/fix", controllers_api.DelKlineShadowFix)
		/*
			偏移量K线插针(立即模式)
		*/
		api.POST("/kline_offset_immediate", koi_controller.PostKlineOffsetImmediate) // up down
		api.GET("/kline_offset_immediate", koi_controller.GetKlineOffsetImmediateList)
		api.DELETE("/kline_offset_immediate", koi_controller.DelKlineOffsetImmediate)             // over(结束)
		api.GET("/kline_offset_immediate_history", koi_controller.GetKlineOffsetImmediateHistory) // 获取历史偏移量
		/*
			偏移量K线插针(线性模式)
		*/
		api.POST("/kline_offset_linear", kol_controller.PostKlineOffsetLinear) // up down
		api.GET("/kline_offset_linear", kol_controller.GetKlineOffsetLinearList)
		api.DELETE("/kline_offset_linear", kol_controller.DelKlineOffsetLinear) // over(结束)

		/*
			K线控制中心
		*/
		api.GET("/kline_offset_history", kcc_controller.GetKlineOffsetHistory)                 // 获取历史偏移量
		api.DELETE("/kline_offset_history_single", kcc_controller.DelKlineOffsetHistorySingle) // 删除单个

		// 连接
		api.GET("/connection_delivery", controllers_api.GetKconnectionDelivery)
	}
	return router
}
