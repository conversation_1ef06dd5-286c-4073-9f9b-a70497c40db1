// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: internal_grpc.proto

package internal_grpc_proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 请求消息
// type 0 连接通知 1 实时k线 2 盘口数据 3 k线历史数据推送
// 内容
type InternalDataSourceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          int32                  `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalDataSourceRequest) Reset() {
	*x = InternalDataSourceRequest{}
	mi := &file_internal_grpc_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalDataSourceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalDataSourceRequest) ProtoMessage() {}

func (x *InternalDataSourceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_grpc_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalDataSourceRequest.ProtoReflect.Descriptor instead.
func (*InternalDataSourceRequest) Descriptor() ([]byte, []int) {
	return file_internal_grpc_proto_rawDescGZIP(), []int{0}
}

func (x *InternalDataSourceRequest) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *InternalDataSourceRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 响应消息
type InternalDataSourceReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       string                 `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalDataSourceReply) Reset() {
	*x = InternalDataSourceReply{}
	mi := &file_internal_grpc_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalDataSourceReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalDataSourceReply) ProtoMessage() {}

func (x *InternalDataSourceReply) ProtoReflect() protoreflect.Message {
	mi := &file_internal_grpc_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalDataSourceReply.ProtoReflect.Descriptor instead.
func (*InternalDataSourceReply) Descriptor() ([]byte, []int) {
	return file_internal_grpc_proto_rawDescGZIP(), []int{1}
}

func (x *InternalDataSourceReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 空的响应消息
type EmptyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmptyResponse) Reset() {
	*x = EmptyResponse{}
	mi := &file_internal_grpc_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmptyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyResponse) ProtoMessage() {}

func (x *EmptyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_internal_grpc_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyResponse.ProtoReflect.Descriptor instead.
func (*EmptyResponse) Descriptor() ([]byte, []int) {
	return file_internal_grpc_proto_rawDescGZIP(), []int{2}
}

var File_internal_grpc_proto protoreflect.FileDescriptor

const file_internal_grpc_proto_rawDesc = "" +
	"\n" +
	"\x13internal_grpc.proto\x12\x13internal_grpc_proto\"I\n" +
	"\x19InternalDataSourceRequest\x12\x12\n" +
	"\x04type\x18\x01 \x01(\x05R\x04type\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"3\n" +
	"\x17InternalDataSourceReply\x12\x18\n" +
	"\amessage\x18\x01 \x01(\tR\amessage\"\x0f\n" +
	"\rEmptyResponse2\x92\x01\n" +
	"\x19InternalDataSourceService\x12u\n" +
	"\x15SayInternalDataSource\x12..internal_grpc_proto.InternalDataSourceRequest\x1a,.internal_grpc_proto.InternalDataSourceReplyB\x18Z\x16./;internal_grpc_protob\x06proto3"

var (
	file_internal_grpc_proto_rawDescOnce sync.Once
	file_internal_grpc_proto_rawDescData []byte
)

func file_internal_grpc_proto_rawDescGZIP() []byte {
	file_internal_grpc_proto_rawDescOnce.Do(func() {
		file_internal_grpc_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_internal_grpc_proto_rawDesc), len(file_internal_grpc_proto_rawDesc)))
	})
	return file_internal_grpc_proto_rawDescData
}

var file_internal_grpc_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_internal_grpc_proto_goTypes = []any{
	(*InternalDataSourceRequest)(nil), // 0: internal_grpc_proto.InternalDataSourceRequest
	(*InternalDataSourceReply)(nil),   // 1: internal_grpc_proto.InternalDataSourceReply
	(*EmptyResponse)(nil),             // 2: internal_grpc_proto.EmptyResponse
}
var file_internal_grpc_proto_depIdxs = []int32{
	0, // 0: internal_grpc_proto.InternalDataSourceService.SayInternalDataSource:input_type -> internal_grpc_proto.InternalDataSourceRequest
	1, // 1: internal_grpc_proto.InternalDataSourceService.SayInternalDataSource:output_type -> internal_grpc_proto.InternalDataSourceReply
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_internal_grpc_proto_init() }
func file_internal_grpc_proto_init() {
	if File_internal_grpc_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_internal_grpc_proto_rawDesc), len(file_internal_grpc_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_internal_grpc_proto_goTypes,
		DependencyIndexes: file_internal_grpc_proto_depIdxs,
		MessageInfos:      file_internal_grpc_proto_msgTypes,
	}.Build()
	File_internal_grpc_proto = out.File
	file_internal_grpc_proto_goTypes = nil
	file_internal_grpc_proto_depIdxs = nil
}
