// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: internal_grpc.proto

package internal_grpc_proto

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	InternalDataSourceService_SayInternalDataSource_FullMethodName = "/internal_grpc_proto.InternalDataSourceService/SayInternalDataSource"
)

// InternalDataSourceServiceClient is the client API for InternalDataSourceService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 定义服务
type InternalDataSourceServiceClient interface {
	SayInternalDataSource(ctx context.Context, in *InternalDataSourceRequest, opts ...grpc.CallOption) (*InternalDataSourceReply, error)
}

type internalDataSourceServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewInternalDataSourceServiceClient(cc grpc.ClientConnInterface) InternalDataSourceServiceClient {
	return &internalDataSourceServiceClient{cc}
}

func (c *internalDataSourceServiceClient) SayInternalDataSource(ctx context.Context, in *InternalDataSourceRequest, opts ...grpc.CallOption) (*InternalDataSourceReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(InternalDataSourceReply)
	err := c.cc.Invoke(ctx, InternalDataSourceService_SayInternalDataSource_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// InternalDataSourceServiceServer is the server API for InternalDataSourceService service.
// All implementations must embed UnimplementedInternalDataSourceServiceServer
// for forward compatibility.
//
// 定义服务
type InternalDataSourceServiceServer interface {
	SayInternalDataSource(context.Context, *InternalDataSourceRequest) (*InternalDataSourceReply, error)
	mustEmbedUnimplementedInternalDataSourceServiceServer()
}

// UnimplementedInternalDataSourceServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedInternalDataSourceServiceServer struct{}

func (UnimplementedInternalDataSourceServiceServer) SayInternalDataSource(context.Context, *InternalDataSourceRequest) (*InternalDataSourceReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SayInternalDataSource not implemented")
}
func (UnimplementedInternalDataSourceServiceServer) mustEmbedUnimplementedInternalDataSourceServiceServer() {
}
func (UnimplementedInternalDataSourceServiceServer) testEmbeddedByValue() {}

// UnsafeInternalDataSourceServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to InternalDataSourceServiceServer will
// result in compilation errors.
type UnsafeInternalDataSourceServiceServer interface {
	mustEmbedUnimplementedInternalDataSourceServiceServer()
}

func RegisterInternalDataSourceServiceServer(s grpc.ServiceRegistrar, srv InternalDataSourceServiceServer) {
	// If the following call pancis, it indicates UnimplementedInternalDataSourceServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&InternalDataSourceService_ServiceDesc, srv)
}

func _InternalDataSourceService_SayInternalDataSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InternalDataSourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InternalDataSourceServiceServer).SayInternalDataSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: InternalDataSourceService_SayInternalDataSource_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InternalDataSourceServiceServer).SayInternalDataSource(ctx, req.(*InternalDataSourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// InternalDataSourceService_ServiceDesc is the grpc.ServiceDesc for InternalDataSourceService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var InternalDataSourceService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "internal_grpc_proto.InternalDataSourceService",
	HandlerType: (*InternalDataSourceServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SayInternalDataSource",
			Handler:    _InternalDataSourceService_SayInternalDataSource_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "internal_grpc.proto",
}
