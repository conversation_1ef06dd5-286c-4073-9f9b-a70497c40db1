syntax = "proto3";

package internal_grpc_proto;

option go_package = "./;internal_grpc_proto"; 

// 定义服务
service InternalDataSourceService {
    rpc SayInternalDataSource (InternalDataSourceRequest) returns (InternalDataSourceReply);
}

/*
请求消息
type 0 连接通知 1 实时k线 2 盘口数据 3 k线历史数据推送
内容
*/
message InternalDataSourceRequest {
    int32 type = 1;
    string message = 2;
}

// 响应消息
message InternalDataSourceReply {
    string message = 1;
}

// 空的响应消息
message EmptyResponse {}

// protoc --go_out=. --go-grpc_out=. internal_grpc.proto