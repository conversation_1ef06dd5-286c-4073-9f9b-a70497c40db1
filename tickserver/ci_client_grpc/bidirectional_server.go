package ciclientgrpc

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/sknun/cf/cast"
	"go.uber.org/zap"
	"trading_tick_server/lib/global"
	"trading_tick_server/tickserver/ci_client_grpc/internal_grpc_proto"
)

// 双向流处理器
type BidirectionalServer struct {
	internal_grpc_proto.UnimplementedInternalDataSourceServiceServer
}

// 处理K线历史数据请求
func (s *BidirectionalServer) handleKlineHistoryRequest(message string) *internal_grpc_proto.InternalDataSourceReply {
	fmt.Printf("处理K线历史数据请求: %s\n", message)

	// 解析请求消息
	var requestData map[string]interface{}
	if err := json.Unmarshal([]byte(message), &requestData); err != nil {
		return &internal_grpc_proto.InternalDataSourceReply{
			Message: fmt.Sprintf("解析请求失败: %v", err),
		}
	}

	// 提取请求参数
	userID, _ := requestData["user_id"].(string)
	token, _ := requestData["token"].(string)
	symbol, _ := requestData["code"].(string)
	resolution, _ := requestData["resolution"].(string)
	count, _ := requestData["count"].(float64)
	timestampEnd, _ := requestData["kline_timestamp_end"].(float64)

	// 调用现成的 GetKlineHistory 接口
	fmt.Printf("gRPC请求参数 - 用户: %s, 产品: %s, 刻度: %s, 数量: %.0f\n",
		userID, symbol, resolution, count)

	var code int
	var responseMessage string
	var data interface{}

	// 调用全局的K线历史数据处理器
	if globalKlineHandler != nil {
		code, responseMessage, data = globalKlineHandler.GetKlineHistory(
			cast.ToUint(userID),
			token,
			symbol,
			resolution,
			int(count),
			int64(timestampEnd),
		)
	} else {
		// 如果没有设置处理器，返回错误
		code = 0
		responseMessage = "K线历史数据处理器未设置"
		data = nil
	}

	// 构造 gRPC 响应
	grpcResponse := map[string]interface{}{
		"requester_token":   requestData["requester_token"],
		"requester_user_id": requestData["requester_user_id"],
		"symbol":            symbol,
		"resolution":        resolution,
		"code":              code,
		"message":           responseMessage,
		"data":              data,
	}
	// 下发数据
	var m internal_grpc_proto.InternalDataSourceRequest
	b, _ := json.Marshal(grpcResponse)
	m.Message = string(b)
	m.Type = 3
	//global.Lg.Info("k线历史数据下发", zap.String("message", m.Message))
	err := CiClientGrpc.SendMessage(cast.ToString(userID), &m)
	if err != nil && err.Error() != "user has no active connection" {
		global.Lg.Error("k线历史数据下发失败", zap.Error(err))
	}

	return &internal_grpc_proto.InternalDataSourceReply{
		Message: responseMessage,
	}
}

// 创建新的双向流服务器
func NewBidirectionalServer() *BidirectionalServer {
	return &BidirectionalServer{}
}

// 实现一元RPC，支持所有功能
func (s *BidirectionalServer) SayInternalDataSource(ctx context.Context, req *internal_grpc_proto.InternalDataSourceRequest) (*internal_grpc_proto.InternalDataSourceReply, error) {
	fmt.Printf("收到RPC消息 Type: %d, Message: %s\n", req.Type, req.Message)

	// 处理不同类型的消息
	switch req.Type {
	case 3:
		// K线历史数据请求 - 返回真实数据
		fmt.Println("处理K线历史数据请求")
		reply := s.handleKlineHistoryRequest(req.Message)
		return reply, nil
	default:
		fmt.Printf("未知消息类型: %d\n", req.Type)
		return &internal_grpc_proto.InternalDataSourceReply{
			Message: `{"action":"error","message":"unknown message type"}`,
		}, nil
	}
}
