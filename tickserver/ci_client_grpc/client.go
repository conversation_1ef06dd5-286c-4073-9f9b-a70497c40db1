package ciclientgrpc

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"
	"trading_tick_server/tickserver/ci_client_grpc/internal_grpc_proto"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"
)

// 客户端管理器
var CiClientGrpc *UserConnManager

type ConnInfo struct {
	Conn   *grpc.ClientConn
	Client internal_grpc_proto.InternalDataSourceServiceClient
	Token  string
}

type UserConnManager struct {
	mu    sync.RWMutex
	conns map[string]*ConnInfo // key为用户ID
}

// 创建一个新的连接管理器 只需要调用一次
func NewUserConnManager() *UserConnManager {
	return &UserConnManager{
		conns: make(map[string]*ConnInfo),
	}
}

/*
添加客户端
用户ID 服务器地址 服务器的token 非用户表的token
*/
func (m *UserConnManager) AddConnection(userID, serverAddr, token string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if oldConn, ok := m.conns[userID]; ok {
		oldConn.Conn.Close()
		delete(m.conns, userID)
	}

	// 清理地址格式：移除 http:// 或 https:// 前缀
	cleanAddr := serverAddr
	if strings.HasPrefix(serverAddr, "http://") {
		cleanAddr = strings.TrimPrefix(serverAddr, "http://")
	} else if strings.HasPrefix(serverAddr, "https://") {
		cleanAddr = strings.TrimPrefix(serverAddr, "https://")
	}

	interceptor := newUnaryClientInterceptor(token)

	conn, err := grpc.NewClient(
		cleanAddr,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithUnaryInterceptor(interceptor),
	)
	if err != nil {
		return fmt.Errorf("failed to connect to %s: %w", cleanAddr, err)
	}
	client := internal_grpc_proto.NewInternalDataSourceServiceClient(conn)
	var msg internal_grpc_proto.InternalDataSourceRequest
	msg.Type = 0
	msg.Message = `{"type":"probe"}`
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	_, err = client.SayInternalDataSource(ctx, &msg)
	if err != nil {
		return fmt.Errorf("failed to connect to %s: %w", serverAddr, err)
	}

	connInfo := &ConnInfo{
		Conn:   conn,
		Client: client,
		Token:  token,
	}

	m.conns[userID] = connInfo

	return nil
}

// 移除连接
func (m *UserConnManager) RemoveConnection(userID string) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if info, ok := m.conns[userID]; ok {
		info.Conn.Close()
		delete(m.conns, userID)
	}
}

// 处理具体的消息内容
func (m *UserConnManager) processIncomingMessage(userID string, message string) {
	// 尝试解析JSON消息
	var messageData map[string]interface{}
	if err := json.Unmarshal([]byte(message), &messageData); err != nil {
		fmt.Printf("用户 %s 消息解析失败: %v\n", userID, err)
		return
	}

	// 根据action字段进行不同处理
	action, ok := messageData["action"].(string)
	if !ok {
		fmt.Printf("用户 %s 消息缺少action字段\n", userID)
		return
	}

	switch action {
	case "kline_history_response":
		m.handleKlineHistoryResponse(userID, messageData)
	case "heartbeat":
		m.handleHeartbeat(userID, messageData)
	default:
		fmt.Printf("用户 %s 未知的消息类型: %s\n", userID, action)
	}
}

// 处理K线历史数据响应
func (m *UserConnManager) handleKlineHistoryResponse(userID string, data map[string]interface{}) {
	symbol, _ := data["symbol"].(string)
	requestType, _ := data["request_type"].(string)
	code, _ := data["code"].(float64)

	fmt.Printf("用户 %s K线历史数据响应 - 产品: %s, 类型: %s, 状态码: %.0f\n",
		userID, symbol, requestType, code)

	if code == 1 {
		// 处理成功响应
		fmt.Printf("用户 %s K线数据获取成功\n", userID)
		if klineData, ok := data["data"]; ok {
			fmt.Printf("K线数据: %v\n", klineData)
		}
	} else {
		// 处理失败响应
		message, _ := data["message"].(string)
		fmt.Printf("用户 %s K线数据获取失败: %s\n", userID, message)
	}
}

// 处理心跳消息
func (m *UserConnManager) handleHeartbeat(userID string, data map[string]interface{}) {
	fmt.Printf("用户 %s 心跳消息\n", userID)

	// 发送心跳响应
	responseData := map[string]interface{}{
		"action":    "heartbeat_response",
		"timestamp": time.Now().Unix(),
	}
	responseBytes, _ := json.Marshal(responseData)
	msg := &internal_grpc_proto.InternalDataSourceRequest{
		Type:    0,
		Message: string(responseBytes),
	}
	m.SendStreamMessage(userID, msg)
}

// 向指定userID的服务器发送消息
func (m *UserConnManager) SendMessage(userID string, req *internal_grpc_proto.InternalDataSourceRequest) error {
	m.mu.RLock()
	info, ok := m.conns[userID]
	m.mu.RUnlock()

	if !ok {
		return errors.New("user has no active connection")
	}
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	_, err := info.Client.SayInternalDataSource(ctx, req)
	if err != nil {
		// 发送失败时，关闭并移除该连接
		fmt.Printf("❌Failed to send message to %s: %v\n", userID, err)
		m.RemoveConnection(userID)
		return err
	}
	return nil
}

// 通过双向流发送消息
func (m *UserConnManager) SendStreamMessage(userID string, msg *internal_grpc_proto.InternalDataSourceRequest) error {
	m.mu.RLock()
	info, ok := m.conns[userID]
	m.mu.RUnlock()

	if !ok {
		return errors.New("user has no active connection")
	}

	// 使用一元RPC发送消息
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	resp, err := info.Client.SayInternalDataSource(ctx, msg)
	if err != nil {
		return fmt.Errorf("RPC发送失败: %w", err)
	}

	fmt.Printf("RPC响应: %s\n", resp.Message)
	return nil
}

// 取目前客户端连接列表
func (m *UserConnManager) GetAllUserIDs() []string {
	m.mu.RLock()
	defer m.mu.RUnlock()

	userIDs := make([]string, 0, len(m.conns))
	for userID := range m.conns {
		userIDs = append(userIDs, userID)
	}
	return userIDs
}

// 初始化函数
func init() {
	CiClientGrpc = &UserConnManager{
		conns: make(map[string]*ConnInfo),
	}
}

// 在每次调用时，把token注入到metadata
func newUnaryClientInterceptor(token string) grpc.UnaryClientInterceptor {
	return func(
		ctx context.Context,
		method string,
		req, reply interface{},
		cc *grpc.ClientConn,
		invoker grpc.UnaryInvoker,
		opts ...grpc.CallOption,
	) error {
		md, ok := metadata.FromOutgoingContext(ctx)
		if !ok {
			md = metadata.New(nil)
		}
		md.Set("authorization", token)
		ctx = metadata.NewOutgoingContext(ctx, md)
		return invoker(ctx, method, req, reply, cc, opts...)
	}
}
