package tick_tradech

import (
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/logger"
	"trading_tick_server/lib/structs"
	"trading_tick_server/tickserver/ci_client_grpc/operate_struct"

	"github.com/sknun/cf/cast"
	"go.uber.org/zap"
)

/*
有插针操作,先给close一个初始值=tmp.Candlestick 是插针开始时的k线实时价格,也就是后台插针消息指定的插针开始值
开始值也是上条k线的结束值
*/
func getCandleOpen(tmp structs.UserShadow, item *operate_struct.TradeChSubData, currentKline map[string]map[int64]structs.KlineShadowData) (float64, float64) {
	// 初始化默认值
	close := cast.ToFloat64(tmp.Candlestick)
	candleOpen := close

	logger.DataFlow("开始获取K线开盘价和收盘价",
		zap.Float64("初始close价格", close),
		zap.Float64("初始candleOpen价格", candleOpen),
		zap.Int("Base数据长度", len(item.Base)))

	// 检查Base数据是否为空
	if len(item.Base) == 0 {
		global.Lg.Error("❌K线切片为空，这是严重错误",
			zap.Any("tmp", tmp),
			zap.Any("item", item))
		return close, candleOpen
	}

	currentTime := item.Base[0].Time
	prevTime := currentTime - 60

	global.Lg.Debug("时间信息",
		zap.Int64("当前时间", currentTime),
		zap.Int64("上一分钟时间", prevTime))

	// 获取当前分钟的K线数据
	currentMinuteKline, exists := currentKline[global.Resolutions.Minute1][currentTime]
	if exists {
		global.Lg.Debug("找到当前分钟K线数据",
			zap.Int64("时间", currentTime),
			zap.Float64("Open", currentMinuteKline.Open),
			zap.Float64("Close", currentMinuteKline.Close))
	}

	// 获取上一分钟的K线数据
	prevMinuteKline, prevExists := currentKline[global.Resolutions.Minute1][prevTime]
	if prevExists {
		global.Lg.Debug("找到上一分钟K线数据",
			zap.Int64("时间", prevTime),
			zap.Float64("Close", prevMinuteKline.Close))
	}

	// 确定Close价格
	originalClose := close
	if exists && currentMinuteKline.Close > 0 {
		// 取到当前k的Close值
		close = currentMinuteKline.Close
		logger.DataFlow("使用当前分钟K线Close价格",
			zap.Float64("原始价格", originalClose),
			zap.Float64("新价格", close),
			zap.Int64("时间", currentTime))
	} else if prevExists && prevMinuteKline.Close > 0 {
		// 如果当前k线没有Close值，说明当前数据为这分钟的第一条，则取上条k线的Close值
		close = prevMinuteKline.Close
		logger.DataFlow("使用上一分钟K线Close价格作为当前Close",
			zap.Float64("原始价格", originalClose),
			zap.Float64("新价格", close),
			zap.Int64("上一分钟时间", prevTime))
	} else {
		global.Lg.Warn("未找到有效的Close价格，使用默认值",
			zap.Float64("默认价格", close),
			zap.Bool("当前分钟存在", exists),
			zap.Bool("上一分钟存在", prevExists))
	}

	// 确定Open价格
	originalCandleOpen := candleOpen
	if exists && currentMinuteKline.Open > 0 {
		// 找到这分钟的Open值
		candleOpen = currentMinuteKline.Open
		logger.DataFlow("使用当前分钟K线Open价格",
			zap.Float64("原始价格", originalCandleOpen),
			zap.Float64("新价格", candleOpen),
			zap.Int64("时间", currentTime))
	} else if prevExists && prevMinuteKline.Close > 0 {
		// 如果当前k线没有Open值，说明当前数据为这分钟的第一条，则取上条k线的Close值，因为上条的Close值也是这条的Open值
		candleOpen = prevMinuteKline.Close
		logger.DataFlow("使用上一分钟K线Close价格作为当前Open",
			zap.Float64("原始价格", originalCandleOpen),
			zap.Float64("新价格", candleOpen),
			zap.Int64("上一分钟时间", prevTime))
	} else {
		global.Lg.Warn("未找到有效的Open价格，使用默认值",
			zap.Float64("默认价格", candleOpen),
			zap.Bool("当前分钟存在", exists),
			zap.Bool("上一分钟存在", prevExists))
	}

	logger.DataFlow("K线价格获取完成",
		zap.Float64("最终Close价格", close),
		zap.Float64("最终Open价格", candleOpen),
		zap.Int64("处理时间", currentTime))

	return close, candleOpen
}
