package tick_tradech

import (
	"strconv"
	"trading_tick_server/internal/data_transfer_station/model"
	"trading_tick_server/internal/data_transfer_station/service"
	"trading_tick_server/internal/data_transfer_station/types"
	"trading_tick_server/internal/user/user_cache"
	"trading_tick_server/lib/function"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/influxdb"
	"trading_tick_server/lib/logger"
	"trading_tick_server/lib/mysql"
	"trading_tick_server/lib/structs"
	"trading_tick_server/lib/symbol_price"
	ciclientgrpc "trading_tick_server/tickserver/ci_client_grpc"
	"trading_tick_server/tickserver/ci_client_grpc/operate_func"
	"trading_tick_server/tickserver/ci_client_grpc/operate_global"
	opt "trading_tick_server/tickserver/ci_client_grpc/operate_struct"
	pb "trading_tick_server/tickserver/data_source_grpc/data_source_proto"

	"github.com/shopspring/decimal"
	"github.com/sknun/cf/cast"
	"go.uber.org/zap"
)

// K线时间间隔（秒）
const intervalDuration int64 = 60

// 成交报价 需要保存和计算 然后下发
func ProcessTickTradeCh(
	item *pb.ReceiveDataTrade, // 当前成交数据
	kline map[string]*structs.KlineRetData, // 各时间周期的K线数据
	tickKey string, // 产品唯一标识
	laData *pb.ReceiveDataTrade, // 上次成交数据（用于对比）
	tickTradeCount, // 成交计数器
	intervalLastStart *int64, // 上次K线开始时间
	timeZone string, // 时区
) {
	if operate_func.IntEqual(item.Price, laData.Price) {
		// 因为价格没有变化只记录但不阻挡下发
		operate_global.ShardCount.Inc(item.Platform + item.Symbol + "-TickTrade-NoChange")
	}
	// 这个记录是干什么的
	symbol_price.Set(item.Platform, item.Symbol, item.Price)
	operate_global.ShardCount.Inc(item.Platform + item.Symbol + "-TickTrade-Count")
	// 更新最后一次成交价
	laData.Price = item.Price
	var base []structs.KlineRetData
	// k线时间
	timestampSec := item.Time / 1000
	// 1分钟k线的初始时间
	intervalStart := (timestampSec / intervalDuration) * intervalDuration
	// 当前k线是否为初始化
	var isInit bool
	// 更新 K 线数据
	/*
		1. 取当前时间戳所在的1分钟K线
	*/
	other, err := function.GetKLineStartTimes(intervalStart, timeZone)
	if err != nil {
		global.Lg.Error("❌取时间戳所在k线的其它时间颗粒失败", zap.Int64("intervalStart", intervalStart), zap.Error(err))
		return
	}
	if kline[global.Resolutions.Minute1].Time != intervalStart {
		if *intervalLastStart == 0 {
			*intervalLastStart = intervalStart
		} else {
			if intervalStart < *intervalLastStart {
				// 下条数据的时间比上条小 做返回不给
				operate_global.ShardCount.Inc(item.Platform + item.Symbol + "-TickTrade-TimeBefore")
				return
			} else {
				*intervalLastStart = intervalStart
			}
		}
		var data []pb.ReceiveDataKline
		// 保存1分钟k线到data中 为存储到数据做准备
		if kline[global.Resolutions.Minute1].Time > 0 {
			data = append(data, pb.ReceiveDataKline{
				Platform:    item.Platform,
				Symbol:      kline[global.Resolutions.Minute1].Code,
				Resolutions: kline[global.Resolutions.Minute1].Interval,
				Time:        kline[global.Resolutions.Minute1].Time,
				Open:        kline[global.Resolutions.Minute1].Open,
				Close:       kline[global.Resolutions.Minute1].Close,
				High:        kline[global.Resolutions.Minute1].High,
				Low:         kline[global.Resolutions.Minute1].Low,
				Volume:      kline[global.Resolutions.Minute1].Volume,
				Turnover:    kline[global.Resolutions.Minute1].Turnover,
			})
		}
		// 初始化新的k线
		isInit = true
		*tickTradeCount++
		kline[global.Resolutions.Minute1].ID = strconv.FormatInt(*tickTradeCount, 10)
		kline[global.Resolutions.Minute1].Code = item.Symbol
		kline[global.Resolutions.Minute1].Interval = global.Resolutions.Minute1
		kline[global.Resolutions.Minute1].Open = item.Price
		kline[global.Resolutions.Minute1].High = item.Price
		kline[global.Resolutions.Minute1].Low = item.Price
		kline[global.Resolutions.Minute1].Close = item.Price
		kline[global.Resolutions.Minute1].Volume = item.Volume
		kline[global.Resolutions.Minute1].Turnover = item.Turnover
		kline[global.Resolutions.Minute1].Time = intervalStart
		base = append(base, *kline[global.Resolutions.Minute1])
		// 初始化其它k线
		for k, v := range other {
			// 判断是否需要初始化新的k线
			if kline[k].Time != v {
				// // 保存其它k线到data中 为存储到数据做准备
				if kline[k].Time > 0 {
					data = append(data, pb.ReceiveDataKline{
						Platform:    item.Platform,
						Symbol:      kline[k].Code,
						Resolutions: kline[k].Interval,
						Time:        kline[k].Time,
						Open:        kline[k].Open,
						Close:       kline[k].Close,
						High:        kline[k].High,
						Low:         kline[k].Low,
						Volume:      kline[k].Volume,
						Turnover:    kline[k].Turnover,
					})
				}
				// 取本地存储的数据
				klineData, err := influxdb.GetOneKlineData(function.GetInfluxDBTable(item.Platform, item.Symbol, timeZone, k), v)
				if err != nil {
					kline[k].Time = v
					kline[k].Open = kline[global.Resolutions.Minute1].Open
					kline[k].Close = kline[global.Resolutions.Minute1].Close
					kline[k].High = kline[global.Resolutions.Minute1].High
					kline[k].Low = kline[global.Resolutions.Minute1].Low
					kline[k].Volume = kline[global.Resolutions.Minute1].Volume
					kline[k].Turnover = kline[global.Resolutions.Minute1].Turnover
				} else {
					kline[k].Time = klineData.Time
					kline[k].Open = klineData.Open
					kline[k].Close = klineData.Close
					kline[k].High = klineData.High
					kline[k].Low = klineData.Low
					kline[k].Volume = klineData.Volume
					kline[k].Turnover = klineData.Turnover
				}
				kline[k].Code = item.Symbol
				kline[k].Interval = k
				if operate_func.IntGreater(item.Price, kline[k].High) {
					kline[k].High = item.Price
				}
				if operate_func.IntLess(item.Price, kline[k].Low) {
					kline[k].Low = item.Price
				}
				kline[k].Close = item.Price
				kline[k].Volume += item.Volume
				kline[k].Turnover += item.Turnover
				kline[k].ID = strconv.FormatInt(*tickTradeCount, 10)
				base = append(base, *kline[k])
			}
		}
		// 存储实时K线
		if len(data) > 0 {
			err = influxdb.WriteKlineData(data, timeZone)
			if err != nil {
				global.Lg.Error("存储实时k线失败", zap.Any("data", data), zap.Error(err))
			}
		}
	} else {
		*tickTradeCount++
		kline[global.Resolutions.Minute1].ID = strconv.FormatInt(*tickTradeCount, 10)
		// 更新当前 K 线
		if operate_func.IntGreater(item.Price, kline[global.Resolutions.Minute1].High) {
			kline[global.Resolutions.Minute1].High = item.Price
		}
		if operate_func.IntLess(item.Price, kline[global.Resolutions.Minute1].Low) {
			kline[global.Resolutions.Minute1].Low = item.Price
		}
		kline[global.Resolutions.Minute1].Close = item.Price
		kline[global.Resolutions.Minute1].Volume += item.Volume
		kline[global.Resolutions.Minute1].Turnover += item.Turnover
		base = append(base, *kline[global.Resolutions.Minute1])
		// 更新其它k线
		for k := range other {
			if operate_func.IntGreater(item.Price, kline[k].High) {
				kline[k].High = item.Price
			}
			if operate_func.IntLess(item.Price, kline[k].Low) {
				kline[k].Low = item.Price
			}
			kline[k].Close = item.Price
			kline[k].Volume += item.Volume
			kline[k].ID = strconv.FormatInt(*tickTradeCount, 10)
			base = append(base, *kline[k])
		}
	}
	logger.DataFlow("K线数据组装完成", zap.Any("base", base))

	// base是组装完的所有时间颗粒的k线，进行下发处理 下发实时k线 获取所有客户端
	/*
		有用户才会下发
	*/
	for _, v := range ciclientgrpc.CiClientGrpc.GetAllUserIDs() {
		user := user_cache.CacheUserGetOne(v)
		if user.ID < 1 {
			continue
		}

		var userSymbols []structs.UserSymbol
		err := mysql.M.Model(&structs.UserSymbol{}).Where("user_id = ?", user.ID).Find(&userSymbols).Error
		if err == nil {
			user.UserSymbols = userSymbols
		}
		// 为每个用户取自己的channel
		userKey := item.Platform + "_" + item.Symbol + "_" + cast.ToString(user.ID)
		ch, loaded := operate_global.TickTradeSubChannels.LoadOrStore(userKey, make(chan *opt.TradeChSubData, 30))
		userChan := ch.(chan *opt.TradeChSubData)
		userChan <- &opt.TradeChSubData{User: user, Base: base, Item: item, IsInit: isInit}

		if !loaded {
			// 初始化当前用户的插针k线数据
			currentKline := map[string]map[int64]structs.KlineShadowData{}
			currentKline[global.Resolutions.Minute1] = map[int64]structs.KlineShadowData{}
			currentKline[global.Resolutions.Minute5] = map[int64]structs.KlineShadowData{}
			currentKline[global.Resolutions.Minute15] = map[int64]structs.KlineShadowData{}
			currentKline[global.Resolutions.Minute30] = map[int64]structs.KlineShadowData{}
			currentKline[global.Resolutions.Hour1] = map[int64]structs.KlineShadowData{}
			currentKline[global.Resolutions.Hour4] = map[int64]structs.KlineShadowData{}
			currentKline[global.Resolutions.Day] = map[int64]structs.KlineShadowData{}
			currentKline[global.Resolutions.Week] = map[int64]structs.KlineShadowData{}
			currentKline[global.Resolutions.Month] = map[int64]structs.KlineShadowData{}
			var fallStartTime = new(int64)
			var fallPeakValue = new(decimal.Decimal)
			/*
				在创建携程时就给定房间
			*/
			dataRoom, ok := service.GetStationProductRoom[types.ExtraDataPkg](cast.ToString(user.ID), item.Symbol)
			if !ok {
				// 这里应该是没有房间了
				global.Lg.Error("没有找到数据中转站房间", zap.Any("item", item))
				return
			}
			global.Lg.Debug("😊创建用户插针k线数据协程",
				zap.Uint("用户ID", user.ID),
				zap.String("产品", item.Symbol))
			go processTickTradeCategorySub(userChan, currentKline, fallStartTime, fallPeakValue, timeZone, dataRoom)
		}
	}
}

func processTickTradeCategorySub(
	channel chan *opt.TradeChSubData,
	currentKline map[string]map[int64]structs.KlineShadowData,
	fallStartTime *int64,
	fallPeakValue *decimal.Decimal,
	timeZone string,
	dataRoom *model.DataRoom[types.ExtraDataPkg],
) {
	for item := range channel {
		if item.User.KlineSymbol[function.GetPlatformSymbol(item.Item.Platform, item.Item.Symbol)] > 0 {
			tickTradeChSub(item, currentKline, fallStartTime, fallPeakValue, timeZone, dataRoom)
		}
	}
}
