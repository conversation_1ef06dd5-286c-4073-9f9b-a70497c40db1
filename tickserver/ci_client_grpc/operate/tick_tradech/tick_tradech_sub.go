package tick_tradech

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"
	"trading_tick_server/internal/data_transfer_station/model"
	"trading_tick_server/internal/data_transfer_station/types"
	"trading_tick_server/lib/cacheimport"
	"trading_tick_server/lib/function"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/logger"
	"trading_tick_server/lib/mysql"
	"trading_tick_server/lib/redis"
	"trading_tick_server/lib/structs"
	ciclientgrpc "trading_tick_server/tickserver/ci_client_grpc"
	"trading_tick_server/tickserver/ci_client_grpc/internal_grpc_proto"
	"trading_tick_server/tickserver/ci_client_grpc/operate_func"
	"trading_tick_server/tickserver/ci_client_grpc/operate_global"
	"trading_tick_server/tickserver/ci_client_grpc/operate_struct"
	pbdsp "trading_tick_server/tickserver/data_source_grpc/data_source_proto"

	"github.com/shopspring/decimal"
	"github.com/sknun/cf/cast"
	"go.uber.org/zap"
)

/*
处理实时K线流数据 - 插针控盘在这里执行
*/
func tickTradeChSub(
	item *operate_struct.TradeChSubData, // 用户成交数据
	currentKline map[string]map[int64]structs.KlineShadowData, // 插针K线缓存
	fallStartTime *int64, // 回落开始时间
	fallPeakValue *decimal.Decimal, // 回落峰值
	timeZone string, // 时区
	dataRoom *model.DataRoom[types.ExtraDataPkg], // 数据传输房间
) {

	// 开始处理流数据
	logger.DataFlow("开始处理实时K线流数据",
		zap.Uint("用户ID", item.User.ID),
		zap.String("平台", item.Item.Platform),
		zap.String("交易对", item.Item.Symbol),
		zap.Float64("价格", item.Item.Price),
		zap.Int64("时间戳", item.Item.Time),
		zap.String("格式化时间", time.Unix(item.Item.Time/1000, 0).Format("2006-01-02 15:04:05.000")),
		zap.Bool("是否初始化", item.IsInit),
		zap.String("时区", timeZone),
	)

	var send []structs.KlineRetData
	var err error
	var shadow bool
	var newPrice float64
	var tmp structs.UserShadow
	var data types.ExtraDataPkg
	userDecimalPlaces := 8 // 默认使用8位精度
	if item.User.UserSymbols != nil {
		for _, userSymbol := range item.User.UserSymbols {
			if userSymbol.Symbol == item.Base[0].Code && userSymbol.Type == "kline" {
				userDecimalPlaces = int(userSymbol.DecimalPlaces)
				break
			}
		}
	}

	// 价格有效性检查
	p := decimal.NewFromFloat(item.Item.Price)
	if p.LessThanOrEqual(decimal.Zero) {
		global.Lg.Warn("价格异常，记录到异常数据表",
			zap.Float64("异常价格", item.Item.Price),
			zap.String("平台", item.Item.Platform),
			zap.String("交易对", item.Item.Symbol),
		)

		// 记录异常数据
		mysql.M.Create(&structs.WorryDataRecord{
			DataSourceName: item.Item.Platform,
			ProductName:    item.Item.Symbol,
			IsControl:      false,
			ItemTime:       item.Item.Time,
			Price:          decimal.NewFromFloat(item.Item.Price),
		})
		return
	}

	// 从缓存中查看该用户的这个产品(Symbol)是否有插针操作
	tmp = cacheimport.UserKlineShadowGet(cast.ToString(item.User.ID), item.Item.Symbol)
	/*
		是否存在高级控盘
	*/
	if tmp.ID > 0 {
		// 目前只处理这一种插针
		if tmp.Type == "intuitive" {
			var stage int
			send, newPrice, stage, err = intuitiveShadow(tmp, item, currentKline, fallStartTime, fallPeakValue, timeZone, int32(userDecimalPlaces))
			// 0 值容错
			if decimal.NewFromFloat(newPrice).LessThanOrEqual(decimal.Zero) {
				controlDetail, _ := json.Marshal(tmp)
				// 直接返回这条不要了
				mysql.M.Create(&structs.WorryDataRecord{
					DataSourceName:    item.Item.Platform,
					ProductName:       item.Item.Symbol,
					IsControl:         true,
					ItemTime:          item.Item.Time,
					Price:             decimal.NewFromFloat(item.Item.Price),
					NewPrice:          decimal.NewFromFloat(newPrice),
					Stage:             stage,
					ControlDetailJson: controlDetail,
				})
				return
			}

			if err == nil {
				shadow = true
				logger.DataFlow("插针处理成功，将使用插针数据")
			} else {
				global.Lg.Error("插针处理失败，将使用原始数据", zap.Error(err))
			}
		}
	}

	// 准备发送数据
	var m internal_grpc_proto.InternalDataSourceRequest
	var b []byte

	if !shadow {
		// 检查是否有缓存的开盘价
		for k := range item.Base {
			if currentKline[item.Base[k].Interval][item.Base[k].Time].Open > 0 {
				oldOpen := item.Base[k].Open
				item.Base[k].Open = currentKline[item.Base[k].Interval][item.Base[k].Time].Open
				logger.DataFlow("使用缓存的开盘价",
					zap.String("时间间隔", item.Base[k].Interval),
					zap.Int64("K线时间", item.Base[k].Time),
					zap.Float64("原开盘价", oldOpen),
					zap.Float64("缓存开盘价", item.Base[k].Open),
				)
			}
		}

		// 设置数据传输站的精度
		dataRoom.OriginalDecimalPlaces = userDecimalPlaces

		logger.DataFlow("数据精度信息",
			zap.Int("用户配置精度", userDecimalPlaces),
			zap.Float64("开盘价", item.Base[0].Open),
			zap.Float64("收盘价", item.Base[0].Close),
		)

		// 构建额外数据包
		extraDataPkg := types.ExtraDataPkg{
			OriginalData: &item.Base,
			ExtraData: &types.ExtraData{
				Platform:              item.Item.Platform,
				Symbol:                item.Base[0].Code,
				TotalOffset:           decimal.Zero,
				CurrentOffset:         decimal.Zero,
				RealPrice:             decimal.NewFromFloat(item.Base[0].Close),
				ControlPrice:          decimal.NewFromFloat(item.Base[0].Close),
				OriginalDecimalPlaces: int32(userDecimalPlaces), // 🔧 使用用户配置的精度
				TimeZone:              timeZone,
			},
		}

		logger.DataFlow("写入数据传输站",
			zap.String("交易对", extraDataPkg.ExtraData.Symbol),
			zap.String("真实价格", extraDataPkg.ExtraData.RealPrice.String()),
			zap.String("控制价格", extraDataPkg.ExtraData.ControlPrice.String()),
		)

		// 写入数据传输站
		dataRoom.Write(extraDataPkg)

		// 从数据传输站读取处理后的数据
		data = *(dataRoom.Read())

		logger.DataFlow("从数据传输站读取数据",
			zap.String("交易对", data.ExtraData.Symbol),
			zap.String("总偏移", data.ExtraData.TotalOffset.String()),
			zap.String("当前偏移", data.ExtraData.CurrentOffset.String()),
			zap.String("真实价格", data.ExtraData.RealPrice.String()),
			zap.String("控制价格", data.ExtraData.ControlPrice.String()),
		)

		/*	if item.Base[0].Code == "BTCUSDT" && item.User.ID == 5 {
			fmt.Printf("3️⃣%v, 用户 %v 房间读取到数据,平台 : %v, 产品 : %v, 价格 : %v \n", time.Now().Format("2006-01-02 15:04:05.000"), data.ExtraData.Platform, data.ExtraData.Symbol, data.ExtraData.ControlPrice)
		}*/

		operate_func.ProtectKlinePrice(*data.OriginalData, cast.ToString(item.User.ID), item.Item.Symbol)
		/*
			临时使用
		*/
		var controlMsg internal_grpc_proto.InternalDataSourceRequest
		controlMsg.Type = 100
		extraData := *data.ExtraData
		if extraData.ControlPrice.GreaterThan(decimal.Zero) {
			controlMsg.Message = extraData.Symbol + ":" + extraData.TotalOffset.String() + ":" + extraData.CurrentOffset.String() + ":" + extraData.RealPrice.String() + ":" + extraData.ControlPrice.String()
			err = ciclientgrpc.CiClientGrpc.SendMessage(cast.ToString(item.User.ID), &controlMsg)
			if err != nil && err.Error() != "user has no active connection" {
				global.Lg.Error("一个价格成交数据下发失败", zap.Error(err))
			}
		}

		/*
			给盘口数据添加要使用到的信息,临时这么做
		*/
		dataRoom.AsksPriceMin = data.ExtraData.AsksPriceMin
		dataRoom.AsksPriceMax = data.ExtraData.AsksPriceMax
		dataRoom.OrderBookDiffMin = data.ExtraData.OrderBookDiffMin
		dataRoom.OrderBookDiffMax = data.ExtraData.OrderBookDiffMax
		dataRoom.ControlPrice = data.ExtraData.ControlPrice.Truncate(int32(userDecimalPlaces)) // 🔧 使用用户配置的精度
	} else { // 老控盘区域 Start
		// 计算盘口价格
		asksPriceMin := cast.ToFloat64(tmp.AsksPriceMin)
		asksPriceMax := cast.ToFloat64(tmp.AsksPriceMax)
		asksPrice := function.FloatRange(asksPriceMin, asksPriceMax)

		// 构建盘口数据
		bData := pbdsp.OrderBookPrice{
			Symbol: item.Item.Symbol,
			Time:   item.Item.Time,
			Asks: []pbdsp.OrderBookData{{
				Price: newPrice + asksPrice,
			}},
			Bids: []pbdsp.OrderBookData{{
				Price: newPrice,
			}},
		}

		// 发送盘口数据
		var mOrderBook internal_grpc_proto.InternalDataSourceRequest
		mOrderBook.Type = 2
		bBook, err := json.Marshal(bData)
		if err != nil {
			global.Lg.Error("盘口数据JSON序列化失败", zap.Any("原始数据", item), zap.Any("盘口数据", bData), zap.Error(err))
		} else {
			mOrderBook.Message = string(bBook)
			err = ciclientgrpc.CiClientGrpc.SendMessage(cast.ToString(item.User.ID), &mOrderBook)
			if err != nil && err.Error() != "user has no active connection" {
				global.Lg.Error("盘口数据发送失败", zap.Error(err))
			} else {
				operate_global.ShardCount.Inc(item.Item.Platform + item.Item.Symbol + "-TickOB-" + cast.ToString(item.User.ID) + "-Count")
			}
		}
	} //  老控盘区域 End

	// 在发送前统一处理精度
	if !shadow {
		// 无控盘时，处理data.OriginalData
		operate_func.ChangeKlineDecimalPlaces(*data.OriginalData, int32(userDecimalPlaces))
		b, err = json.Marshal(data.OriginalData)
	} else {
		// 有控盘时，处理send数据
		operate_func.ChangeKlineDecimalPlaces(send, int32(userDecimalPlaces))
		b, err = json.Marshal(send)
	}

	if err != nil {
		global.Lg.Error("K线数据JSON序列化失败", zap.Error(err))
		return
	}

	// 发送K线数据
	m.Message = string(b)
	m.Type = 1
	err = ciclientgrpc.CiClientGrpc.SendMessage(cast.ToString(item.User.ID), &m)
	if err != nil && err.Error() != "user has no active connection" {
		global.Lg.Error("K线数据发送失败", zap.Error(err))
	} else {
		operate_global.ShardCount.Inc(item.Item.Platform + item.Item.Symbol + "-TickTrade-" + cast.ToString(item.User.ID) + "-Count")
	}
}

/*
直观插针算法实现
*/
func intuitiveShadow(
	tmp structs.UserShadow,
	item *operate_struct.TradeChSubData,
	currentKline map[string]map[int64]structs.KlineShadowData,
	fallStartTime *int64,
	fallPeakValue *decimal.Decimal,
	timeZone string,
	dpKline int32,
) ([]structs.KlineRetData, float64, int, error) {
	logger.DataFlow("开始执行直观插针算法",
		zap.Uint("插针ID", tmp.ID),
		zap.Uint("用户ID", item.User.ID),
		zap.String("交易对", item.Item.Symbol),
		zap.Int64("当前时间", item.Item.Time),
		zap.String("格式化时间", time.Unix(item.Item.Time/1000, 0).Format("2006-01-02 15:04:05.000")),
	)

	var send []structs.KlineRetData
	var newPrice float64

	// 检查K线缓存状态
	if global.Yaml.Logging.ExecShadowLog && len(currentKline[global.Resolutions.Minute1]) < 1 {
		global.Lg.Warn("K线缓存为空，这只应该在首次执行时出现", zap.Any("K线缓存", currentKline))
	}

	logger.DataFlow("当前K线缓存状态",
		zap.Int("1分钟K线数量", len(currentKline[global.Resolutions.Minute1])),
		zap.String("时区", timeZone),
		zap.Int32("小数位数", dpKline),
	)

	// 计算当前插针阶段
	stage, err := function.CalculateFloatValue(&tmp, tmp.StartTime*1000, item.Item.Time, tmp.RiseTime, tmp.PeakTime, tmp.FallTime)
	if err != nil {
		global.Lg.Error("插针阶段计算失败",
			zap.Error(err),
			zap.Uint("插针ID", tmp.ID),
			zap.Int64("开始时间", tmp.StartTime),
			zap.Int64("当前时间", item.Item.Time),
			zap.Int("爬坡时间", tmp.RiseTime),
			zap.Int("峰值时间", tmp.PeakTime),
			zap.Int("回落时间", tmp.FallTime),
		)

		// 清理插针缓存和状态
		cacheimport.UserKlineShadowDelKey(cast.ToString(item.User.ID), item.Item.Symbol)
		mysql.M.Model(&structs.UserShadow{}).Where("id = ?", tmp.ID).Update("status", 1)

		logger.DataFlow("清理插针缓存和状态",
			zap.Uint("插针ID", tmp.ID),
			zap.Uint("用户ID", item.User.ID),
			zap.String("交易对", item.Item.Symbol),
		)

		return send, newPrice, stage, err
	}
	// 有插针操作,先取出当前分钟k线的开盘价和最新价格
	close, candleOpen := getCandleOpen(tmp, item, currentKline)

	logger.DataFlow("获取K线价格信息",
		zap.Float64("收盘价", close),
		zap.Float64("开盘价", candleOpen),
	)

	if global.Yaml.Logging.ExecShadowLog {
		if close < 0.1 {
			global.Lg.Error("获取的收盘价异常", zap.Float64("收盘价", close), zap.Any("插针配置", tmp), zap.Any("数据项", item))
		}
		if candleOpen < 0.1 {
			global.Lg.Error("获取的开盘价异常", zap.Float64("开盘价", candleOpen), zap.Any("插针配置", tmp), zap.Any("数据项", item))
		}
	}

	// 根据不同阶段计算价格
	switch stage {
	case 1:
		// 爬坡期
		logger.DataFlow("进入爬坡期计算")
		newPrice, err = riseNewPrice(candleOpen, tmp, item)
		if err != nil {
			global.Lg.Error("爬坡期价格计算失败", zap.Error(err))
			return send, newPrice, stage, err
		}

		logger.DataFlow("爬坡期价格计算完成",
			zap.Float64("新价格", newPrice),
			zap.Float64("开盘价", candleOpen),
		)

		if newPrice < 0.1 {
			global.Lg.Error("爬坡期计算结果异常", zap.Float64("新价格", newPrice), zap.Float64("开盘价", candleOpen))
		}

	case 2:
		// 峰值期
		logger.DataFlow("进入峰值期计算")
		newPrice = peakNewPrice(tmp)

		logger.DataFlow("峰值期价格计算完成",
			zap.Float64("新价格", newPrice),
			zap.String("插针幅度", tmp.SpikeFactor),
			zap.String("起始价格", tmp.Candlestick),
		)

		if newPrice < 0.1 {
			global.Lg.Error("峰值期计算结果异常", zap.Float64("新价格", newPrice))
		}

	case 3:
		// 回落期
		logger.DataFlow("进入回落期计算")
		newPrice, err = fallNewPrice(close, candleOpen, tmp, item, fallStartTime, fallPeakValue)
		if err != nil {
			global.Lg.Error("回落期价格计算失败", zap.Error(err))
			return send, newPrice, stage, err
		}

		logger.DataFlow("回落期价格计算完成",
			zap.Float64("新价格", newPrice),
			zap.Float64("收盘价", close),
			zap.Float64("开盘价", candleOpen),
			zap.Int64("回落开始时间", *fallStartTime),
			zap.String("回落峰值", fallPeakValue.String()),
		)

		if newPrice < 0.1 {
			global.Lg.Error("回落期计算结果异常", zap.Float64("新价格", newPrice))
		}

	default:
		global.Lg.Error("插针阶段异常", zap.String("阶段", getStageName(stage)))
		return send, newPrice, stage, errors.New("不在三个时期内，严重错误")
	}

	if global.Yaml.Logging.ExecShadowLog && newPrice < 0.1 {
		global.Lg.Error("最终价格计算异常", zap.Float64("新价格", newPrice), zap.String("阶段", getStageName(stage)))
	}

	// 计算影线价格
	newHigh := newPrice + function.FloatRange(0, cast.ToFloat64(tmp.UpperShadowMax))
	newLow := newPrice - function.FloatRange(0, cast.ToFloat64(tmp.LowerShadowMax))

	logger.DataFlow("计算影线价格",
		zap.Float64("新价格", newPrice),
		zap.Float64("上影线最大值", cast.ToFloat64(tmp.UpperShadowMax)),
		zap.Float64("下影线最大值", cast.ToFloat64(tmp.LowerShadowMax)),
		zap.Float64("最高价(截取前)", newHigh),
		zap.Float64("最低价(截取前)", newLow),
	)

	var isOpen bool

	// 处理所有时间颗粒
	logger.DataFlow("开始处理所有时间颗粒", zap.Int("K线数量", len(item.Base)))
	for kBase, vBase := range item.Base {
		logger.DataFlow("处理K线颗粒",
			zap.Int("索引", kBase),
			zap.String("时间间隔", vBase.Interval),
			zap.Int64("K线时间", vBase.Time),
			zap.String("格式化时间", time.Unix(vBase.Time/1000, 0).Format("2006-01-02 15:04:05")),
			zap.Float64("原始开盘价", vBase.Open),
			zap.Float64("原始收盘价", vBase.Close),
			zap.Float64("原始最高价", vBase.High),
			zap.Float64("原始最低价", vBase.Low),
		)

		var UseOpen, UseClose bool

		// K线初始化处理
		if item.IsInit {
			logger.DataFlow("K线初始化模式")
			vBase.High = newHigh
			vBase.Low = newLow
			isOpen = true

			// 使用上一K线的收盘价作为当前K线的开盘价
			truncatedTime := function.TruncateTIme(vBase.Time, timeZone, vBase.Interval)
			if currentKline[vBase.Interval][truncatedTime].Close > 0 {
				oldOpen := vBase.Open
				vBase.Open = currentKline[vBase.Interval][truncatedTime].Close

				logger.DataFlow("使用上一K线收盘价作为开盘价",
					zap.String("时间间隔", vBase.Interval),
					zap.Int64("截取时间", truncatedTime),
					zap.Float64("原开盘价", oldOpen),
					zap.Float64("新开盘价", vBase.Open),
				)
			} else {
				global.Lg.Warn("上条K线收盘价不存在",
					zap.String("时间间隔", vBase.Interval),
					zap.Int64("截取时间", truncatedTime),
				)
				if global.Yaml.Logging.ExecShadowLog {
					if vBase.Interval == "1m" {
						global.Lg.Error("1分钟K线上条收盘价缺失", zap.Any("数据项", item))
					}
				}
			}
		} else {
			// K线更新模式
			logger.DataFlow("K线更新模式")

			// 使用缓存中的价格
			cachedKline := currentKline[vBase.Interval][vBase.Time]
			if cachedKline.High > 0 {
				oldHigh := vBase.High
				vBase.High = cachedKline.High
				logger.DataFlow("使用缓存最高价", zap.Float64("原最高价", oldHigh), zap.Float64("缓存最高价", vBase.High))
			}
			if cachedKline.Low > 0 {
				oldLow := vBase.Low
				vBase.Low = cachedKline.Low
				logger.DataFlow("使用缓存最低价", zap.Float64("原最低价", oldLow), zap.Float64("缓存最低价", vBase.Low))
			}
			if cachedKline.Open > 0 {
				oldOpen := vBase.Open
				vBase.Open = cachedKline.Open
				logger.DataFlow("使用缓存开盘价", zap.Float64("原开盘价", oldOpen), zap.Float64("缓存开盘价", vBase.Open))
			} else {
				global.Lg.Warn("缓存开盘价缺失", zap.String("时间间隔", vBase.Interval))
			}

			// 随机更新最高最低价
			shouldUpdateHigh := function.RandomBool() && operate_func.IntGreater(newHigh, vBase.High)
			shouldUpdateLow := function.RandomBool() && operate_func.IntLess(newLow, vBase.Low)

			logger.DataFlow("随机更新判断",
				zap.Bool("应更新最高价", shouldUpdateHigh),
				zap.Bool("应更新最低价", shouldUpdateLow),
				zap.Float64("新最高价", newHigh),
				zap.Float64("当前最高价", vBase.High),
				zap.Float64("新最低价", newLow),
				zap.Float64("当前最低价", vBase.Low),
			)

			if shouldUpdateHigh {
				vBase.High = newHigh
				logger.DataFlow("随机更新最高价", zap.Float64("新最高价", newHigh))
			} else if operate_func.IntGreater(newPrice, vBase.High) {
				vBase.High = newPrice
				logger.DataFlow("价格突破最高价", zap.Float64("新价格", newPrice))
			}

			if shouldUpdateLow {
				vBase.Low = newLow
				logger.DataFlow("随机更新最低价", zap.Float64("新最低价", newLow))
			} else if operate_func.IntLess(newPrice, vBase.Low) {
				vBase.Low = newPrice
				logger.DataFlow("价格跌破最低价", zap.Float64("新价格", newPrice))
			}
		}

		vBase.Close = newPrice
		currentKline[vBase.Interval][vBase.Time] = structs.KlineShadowData{
			Open:     vBase.Open,
			Close:    vBase.Close,
			High:     vBase.High,
			Low:      vBase.Low,
			Volume:   vBase.Volume,
			Turnover: vBase.Turnover,
		}
		/*
			判断是否需要使用开始或结束的值
			三种情况
			1 插针的开始和 插针的结束都在这个k线时间范围内 那么开盘和收盘价格都要使用真实
			2 插针开始的时候这个k线存在且插针没有在这条k内结束 那么只使用收盘价格
			3 插针结束在这种k线中且插针不在这条k线内开始 那么只使用开盘价格

		*/
		nextTime := function.NextKlineTimestamp(vBase.Time, timeZone, vBase.Interval)
		if tmp.StartTime >= vBase.Time && tmp.EndTime <= nextTime {
			// 这种记录就是都不用
			UseClose = false
			UseOpen = false
		} else {
			if tmp.ShadowStartTime == vBase.Time && tmp.EndTime > nextTime {
				// 使用收盘价格
				UseClose = true
			}
			if tmp.ShadowEndTime == vBase.Time && tmp.StartTime < vBase.Time {
				// 使用开盘价格
				UseOpen = true
			}
		}
		// 存储在缓存中
		// 这里才第一次使用 send ,实际内容是 vBase
		send = append(send, vBase)
		direction := "up"
		if spikeFactor, _ := strconv.ParseInt(tmp.SpikeFactor, 10, 64); spikeFactor < 0 {
			direction = "down"
		}
		shadowData := structs.KlineDataShadow{
			Time:      vBase.Time,
			Open:      vBase.Open,
			Close:     vBase.Close,
			High:      vBase.High,
			Low:       vBase.Low,
			Volume:    vBase.Volume,
			Turnover:  vBase.Turnover,
			UseOpen:   UseOpen,
			UseClose:  UseClose,
			Direction: direction,
		}

		// 价格保护：如果 Low <= 0，使用上次有效的价格
		operate_func.ProtectShadowPrice(cast.ToString(item.User.ID), item.Item.Symbol, &shadowData)

		b, err := json.Marshal(shadowData)
		if err == nil {
			redis.KlineShadowStoreData(cast.ToString(item.User.ID), item.Item.Platform, item.Item.Symbol, timeZone, vBase.Interval, item.Base[0].Time, string(b))
		} else {
			global.Lg.Error("K线插针数据JSON序列化失败", zap.Error(err))
			return send, newPrice, stage, err
		}

		// 记录插针日志到数据库
		data_ := structs.TickShadowLog{
			Symbol:             item.Item.Symbol,
			Platform:           item.Item.Platform,
			Resolutions:        vBase.Interval,
			SfStartTs:          tmp.StartTime * 1000,
			SfCurrentTs:        item.Item.Time,
			SfRiseTime:         tmp.RiseTime,
			SfPeakTime:         tmp.PeakTime,
			SfFallTime:         tmp.FallTime,
			SfFluc:             tmp.SpikeFactor,
			SfStartCandlestick: tmp.Candlestick,
			Stage:              stage,
			UserID:             item.User.ID,
			ShadowID:           tmp.ID,
			KlineID:            vBase.ID,
			KlineTime:          vBase.Time,
			Open:               cast.ToString(vBase.Open),
			Close:              cast.ToString(vBase.Close),
			High:               cast.ToString(vBase.High),
			Low:                cast.ToString(vBase.Low),
			Volume:             cast.ToString(vBase.Volume),
			Turnover:           cast.ToString(vBase.Turnover),
			UseOpen:            UseOpen,
			UseClose:           UseClose,
			IsOpen:             isOpen,
			OrigKlineTime:      item.Base[kBase].Time,
			OrigOpen:           cast.ToString(item.Base[kBase].Open),
			OrigClose:          cast.ToString(item.Base[kBase].Close),
			OrigHigh:           cast.ToString(item.Base[kBase].High),
			OrigLow:            cast.ToString(item.Base[kBase].Low),
			OrigVolume:         cast.ToString(item.Base[kBase].Volume),
			OrigTurnover:       cast.ToString(item.Base[kBase].Turnover),
			Direction:          direction,
		}

		logger.DataFlow("记录插针日志到数据库",
			zap.Uint("插针ID", data_.ShadowID),
			zap.String("阶段", getStageName(stage)),
			zap.String("时间间隔", data_.Resolutions),
			zap.Bool("使用开盘价", data_.UseOpen),
			zap.Bool("使用收盘价", data_.UseClose),
		)

		go mysql.M.Create(&data_)
	}

	logger.DataFlow("直观插针算法执行完成",
		zap.Uint("插针ID", tmp.ID),
		zap.String("阶段", getStageName(stage)),
		zap.Float64("最终价格", newPrice),
		zap.Int("处理K线数量", len(send)),
		zap.Float64("新最高价", newHigh),
		zap.Float64("新最低价", newLow),
	)

	return send, newPrice, stage, err
}

/*
爬坡期价格计算
*/
func riseNewPrice(candleOpen float64, tmp structs.UserShadow, item *operate_struct.TradeChSubData) (float64, error) {
	logger.DataFlow("开始爬坡期价格计算",
		zap.Float64("K线开盘价", candleOpen),
		zap.Uint("插针ID", tmp.ID),
		zap.Int64("当前时间", item.Item.Time),
	)

	var newPrice float64
	// 第一步: 判断是否是第一根k线(插针起始在一根不完整的k线上)
	var tmpCandlesInfo []structs.CandlesInfo
	err := json.Unmarshal(tmp.RiseCandlesInfo, &tmpCandlesInfo)
	if err != nil {
		global.Lg.Error("爬坡期K线信息解析失败", zap.Any("插针配置", tmp), zap.Error(err))
		return newPrice, err
	}

	logger.DataFlow("爬坡期K线信息",
		zap.Int("完整K线数量", len(tmpCandlesInfo)),
		zap.String("插针幅度", tmp.SpikeFactor),
		zap.String("起始价格", tmp.Candlestick),
	)

	if len(tmpCandlesInfo) < 1 {
		// 没有完整K线时，直接插值到峰值
		targetPrice := cast.ToFloat64(tmp.Candlestick) + cast.ToFloat64(tmp.SpikeFactor)
		newPrice = operate_func.LinearInterpolateOneMinute(
			item.Item.Time,
			candleOpen,
			targetPrice,
			tmp.StartTime*1000, 0,
		)

		logger.DataFlow("无完整K线，直接插值到峰值",
			zap.Float64("目标价格", targetPrice),
			zap.Float64("计算价格", newPrice),
		)
	} else {
		// 有完整K线时，按阶段计算
		for riseCandleK, riseCandleV := range tmpCandlesInfo {
			logger.DataFlow("处理爬坡期K线",
				zap.Int("K线索引", riseCandleK),
				zap.Int64("开始时间", riseCandleV.StartTime),
				zap.Int64("结束时间", riseCandleV.EndTime),
				zap.String("默认开盘价", riseCandleV.DefaultOpenPrice.String()),
				zap.String("默认收盘价", riseCandleV.DefaultClosePrice.String()),
			)

			if riseCandleK == 0 {
				// 第一根完整K线处理
				if item.Item.Time < riseCandleV.StartTime*1000 {
					// 在第一根完整K线之前
					newPrice = operate_func.LinearInterpolateOneMinute(
						item.Item.Time,
						candleOpen,
						riseCandleV.DefaultOpenPrice.InexactFloat64(),
						tmp.StartTime*1000, 0,
					)
					logger.DataFlow("在第一根完整K线之前",
						zap.Float64("计算价格", newPrice),
						zap.Float64("目标开盘价", riseCandleV.DefaultOpenPrice.InexactFloat64()),
					)
					break
				}
			}

			if riseCandleK == len(tmpCandlesInfo)-1 {
				// 最后一根完整K线处理
				if item.Item.Time >= riseCandleV.EndTime*1000 {
					// 在最后一根完整K线之后
					op, _ := strconv.ParseFloat(tmp.Candlestick, 64)
					s, _ := strconv.ParseFloat(tmp.SpikeFactor, 64)
					cp := op + s
					riseEndTime := tmp.StartTime + int64(tmp.RiseTime)
					newPrice = operate_func.LinearInterpolateOneMinute(
						item.Item.Time,
						candleOpen,
						cp, 0, riseEndTime*1000,
					)
					logger.DataFlow("在最后一根完整K线之后",
						zap.Float64("计算价格", newPrice),
						zap.Float64("峰值价格", cp),
						zap.Int64("爬坡结束时间", riseEndTime),
					)
					break
				}
			}

			// 在完整K线时间范围内
			if item.Item.Time >= riseCandleV.StartTime*1000 && item.Item.Time < riseCandleV.EndTime*1000 {
				newPrice = operate_func.LinearInterpolateOneMinute(
					item.Item.Time,
					riseCandleV.DefaultOpenPrice.InexactFloat64(),
					riseCandleV.DefaultClosePrice.InexactFloat64(),
					0, 0,
				)
				logger.DataFlow("在完整K线时间范围内",
					zap.Float64("计算价格", newPrice),
					zap.Float64("K线开盘价", riseCandleV.DefaultOpenPrice.InexactFloat64()),
					zap.Float64("K线收盘价", riseCandleV.DefaultClosePrice.InexactFloat64()),
				)
				break
			}
		}
	}

	// 反向拉价处理
	hit := operate_func.ShouldHitNormal(cast.ToFloat64(tmp.RiseHitMin), cast.ToFloat64(tmp.RiseHitMax))
	direction := "爬坡期"
	operate := "拉跌操作"

	logger.DataFlow("反向拉价判断",
		zap.Bool("是否命中", hit),
		zap.Float64("命中最小值", cast.ToFloat64(tmp.RiseHitMin)),
		zap.Float64("命中最大值", cast.ToFloat64(tmp.RiseHitMax)),
		zap.Float64("计算前价格", newPrice),
	)

	if hit {
		diff := function.ShouldHitValue(cast.ToFloat64(tmp.RiseValueMin), cast.ToFloat64(tmp.RiseValueMax))
		if cast.ToFloat64(tmp.SpikeFactor) > 0 {
			// 正向插针时拉跌
			newPrice = newPrice - diff
			operate = "拉跌操作"
		} else {
			// 负向插针时拉涨
			newPrice = newPrice + diff
			operate = "拉涨操作"
		}

		logger.DataFlow("命中反向拉价",
			zap.Uint("插针ID", tmp.ID),
			zap.Int64("1分钟时间", item.Base[0].Time),
			zap.Float64("差值", diff),
			zap.Float64("值范围最小", cast.ToFloat64(tmp.RiseValueMin)),
			zap.Float64("值范围最大", cast.ToFloat64(tmp.RiseValueMax)),
			zap.String("方向", direction),
			zap.Float64("K线开盘价", candleOpen),
			zap.Float64("最终价格", newPrice),
			zap.String("操作", operate),
		)
	} else {
		logger.DataFlow("未命中反向拉价",
			zap.Uint("插针ID", tmp.ID),
			zap.Int64("1分钟时间", item.Base[0].Time),
			zap.Float64("K线开盘价", candleOpen),
			zap.Float64("最终价格", newPrice),
		)
	}

	logger.DataFlow("爬坡期价格计算完成",
		zap.Float64("最终价格", newPrice),
		zap.Bool("使用反向拉价", hit),
	)

	return newPrice, nil
}

/*
峰值期价格计算
*/
func peakNewPrice(tmp structs.UserShadow) float64 {
	logger.DataFlow("开始峰值期价格计算",
		zap.Uint("插针ID", tmp.ID),
		zap.String("起始价格", tmp.Candlestick),
		zap.String("插针幅度", tmp.SpikeFactor),
		zap.String("峰值浮动", tmp.PeakFloat),
	)

	controlStartPrice := decimal.NewFromFloat(cast.ToFloat64(tmp.Candlestick))
	controlPeakPrice := controlStartPrice.Add(decimal.NewFromFloat(cast.ToFloat64(tmp.SpikeFactor)))
	controlPeakMaxPrice := controlPeakPrice.Add(decimal.NewFromFloat(cast.ToFloat64(tmp.PeakFloat)))
	controlPeakMinPrice := controlPeakPrice.Sub(decimal.NewFromFloat(cast.ToFloat64(tmp.PeakFloat)))

	logger.DataFlow("峰值期价格计算过程",
		zap.String("起始价格", controlStartPrice.String()),
		zap.String("峰值价格", controlPeakPrice.String()),
		zap.String("峰值最大价格", controlPeakMaxPrice.String()),
		zap.String("峰值最小价格", controlPeakMinPrice.String()),
	)

	finalPrice := function.ShouldHitValueDecimal(controlPeakMinPrice, controlPeakMaxPrice).Truncate(4).InexactFloat64()

	logger.DataFlow("峰值期价格计算完成",
		zap.Float64("最终价格", finalPrice),
		zap.String("价格范围", fmt.Sprintf("%.4f - %.4f", controlPeakMinPrice.InexactFloat64(), controlPeakMaxPrice.InexactFloat64())),
	)

	return finalPrice
}

/*
回落期价格计算
*/
func fallNewPrice(close, candleOpen float64, tmp structs.UserShadow, item *operate_struct.TradeChSubData, fallStartTime *int64, fallPeakValue *decimal.Decimal) (float64, error) {
	logger.DataFlow("开始回落期价格计算",
		zap.Float64("收盘价", close),
		zap.Float64("K线开盘价", candleOpen),
		zap.Uint("插针ID", tmp.ID),
		zap.Int64("当前时间", item.Item.Time),
		zap.Int64("回落开始时间", *fallStartTime),
		zap.String("回落峰值", fallPeakValue.String()),
	)

	// 计算插针峰值
	spikeFactor, _ := decimal.NewFromString(tmp.SpikeFactor)
	candleStick, _ := decimal.NewFromString(tmp.Candlestick)
	peakValue := candleStick.Add(spikeFactor)

	logger.DataFlow("插针峰值计算",
		zap.String("起始价格", candleStick.String()),
		zap.String("插针幅度", spikeFactor.String()),
		zap.String("峰值", peakValue.String()),
	)

	// 计算回落期开始时间
	if *fallStartTime == 0 {
		*fallStartTime = tmp.StartTime + int64(tmp.RiseTime) + int64(tmp.PeakTime)
		logger.DataFlow("初始化回落开始时间",
			zap.Int64("插针开始时间", tmp.StartTime),
			zap.Int("爬坡时间", tmp.RiseTime),
			zap.Int("峰值时间", tmp.PeakTime),
			zap.Int64("回落开始时间", *fallStartTime),
		)
	}

	// 临时代码：每次都重新计算回落开始时间
	*fallStartTime = tmp.StartTime + int64(tmp.RiseTime) + int64(tmp.PeakTime)

	// 计算回落期峰值
	if fallPeakValue.Equal(decimal.Zero) {
		*fallPeakValue = peakValue
		logger.DataFlow("初始化回落峰值", zap.String("回落峰值", fallPeakValue.String()))
	}

	// 计算回落期结束时间
	fallEndTime := tmp.StartTime + int64(tmp.RiseTime) + int64(tmp.PeakTime) + int64(tmp.FallTime)

	logger.DataFlow("回落期时间计算",
		zap.Int64("回落开始时间", *fallStartTime),
		zap.Int64("回落结束时间", fallEndTime),
		zap.Int64("当前时间", item.Item.Time),
	)

	// 回落期价格计算（追踪大盘真实值）
	newPrice, _ := function.CalFallValue(*fallStartTime, fallEndTime, item.Item.Time, decimal.NewFromFloat(item.Base[0].Close), peakValue)

	logger.DataFlow("基础回落价格计算",
		zap.Float64("基础回落价格", newPrice),
		zap.Float64("真实收盘价", item.Base[0].Close),
	)

	// 处理反向走势情况
	var fallCandlesInfo []structs.CandlesInfo
	err := json.Unmarshal(tmp.FallCandlesInfo, &fallCandlesInfo)
	if err != nil {
		global.Lg.Error("回落期K线信息解析失败", zap.Any("插针配置", tmp), zap.Error(err))
		return newPrice, err
	}

	logger.DataFlow("回落期K线信息",
		zap.Int("反向K线数量", len(fallCandlesInfo)),
	)

	for i, fallCandleV := range fallCandlesInfo {
		// 当前完整K线的收盘价格 = 上一根K线的收盘价格 + FallCandle里面保存的defaultClosePrice
		c := candleOpen + fallCandleV.DefaultClosePrice.InexactFloat64()

		logger.DataFlow("处理回落期反向K线",
			zap.Int("K线索引", i),
			zap.Int64("开始时间", fallCandleV.StartTime),
			zap.Int64("结束时间", fallCandleV.EndTime),
			zap.Bool("是否变化", fallCandleV.IsChange),
			zap.Float64("计算收盘价", c),
			zap.Int64("当前时间", item.Item.Time),
		)

		if item.Item.Time >= fallCandleV.StartTime*1000 && item.Item.Time < fallCandleV.EndTime*1000 && fallCandleV.IsChange {
			// 在反向K线时间范围内且需要变化
			newPrice = operate_func.LinearInterpolateOneMinute(item.Item.Time, candleOpen, c, 0, 0)
			// 重设追踪大盘的起始时间和峰值
			*fallStartTime = fallCandleV.EndTime
			*fallPeakValue = decimal.NewFromFloat(c)

			logger.DataFlow("应用反向走势",
				zap.Float64("反向价格", newPrice),
				zap.Int64("新回落开始时间", *fallStartTime),
				zap.String("新回落峰值", fallPeakValue.String()),
			)
			break
		}
	}

	if global.Yaml.Logging.ExecShadowLog && item.Base[0].Interval == "1m" {
		logger.DataFlow("回落期详细信息",
			zap.Float64("真实收盘价格", item.Base[0].Close),
			zap.Int64("回落开始时间", *fallStartTime),
			zap.Int64("回落结束时间", fallEndTime),
			zap.String("当前时间", time.Unix(item.Item.Time/1000, 0).Format("2006-01-02 15:04:05.000")),
			zap.String("峰值", peakValue.String()),
			zap.Float64("计算出的新价格", newPrice),
		)
	}

	// 反向拉价处理
	hit := operate_func.ShouldHitNormal(cast.ToFloat64(tmp.FallHitMin), cast.ToFloat64(tmp.FallHitMax))

	logger.DataFlow("回落期反向拉价判断",
		zap.Bool("是否命中", hit),
		zap.Float64("命中最小值", cast.ToFloat64(tmp.FallHitMin)),
		zap.Float64("命中最大值", cast.ToFloat64(tmp.FallHitMax)),
		zap.Float64("新价格", newPrice),
		zap.Float64("真实收盘价", close),
	)

	if hit {
		// 命中反向拉价
		diff := function.ShouldHitValue(cast.ToFloat64(tmp.FallValueMin), cast.ToFloat64(tmp.FallValueMax))
		var direction, operate string

		direction = "回落期"
		if newPrice < close {
			// 插针值比真实值小，说明大盘真实价格比插针价格高，命中后反向拉价应该是拉跌
			operate = "New Price < close 拉跌操作"
			newPrice = newPrice - diff
		} else {
			// 插针值比真实值大，说明大盘真实价格比插针价格低，命中后反向拉价应该是拉涨
			operate = "New Price > close 拉涨操作"
			newPrice = newPrice + diff
		}

		logger.DataFlow("命中回落期反向拉价",
			zap.Uint("插针ID", tmp.ID),
			zap.Int64("1分钟时间", item.Base[0].Time),
			zap.Float64("差值", diff),
			zap.Float64("值范围最小", cast.ToFloat64(tmp.FallValueMin)),
			zap.Float64("值范围最大", cast.ToFloat64(tmp.FallValueMax)),
			zap.String("方向", direction),
			zap.Float64("真实收盘价", close),
			zap.Float64("最终价格", newPrice),
			zap.String("操作", operate),
		)
	} else {
		logger.DataFlow("未命中回落期反向拉价",
			zap.Uint("插针ID", tmp.ID),
			zap.Int64("1分钟时间", item.Base[0].Time),
			zap.Float64("真实收盘价", close),
			zap.Float64("最终价格", newPrice),
		)
	}

	logger.DataFlow("回落期价格计算完成",
		zap.Float64("最终价格", newPrice),
		zap.Bool("使用反向拉价", hit),
		zap.Int64("回落开始时间", *fallStartTime),
		zap.String("回落峰值", fallPeakValue.String()),
	)

	return newPrice, nil
}

// 获取阶段名称
func getStageName(stage int) string {
	switch stage {
	case 1:
		return "爬坡期"
	case 2:
		return "峰值期"
	case 3:
		return "回落期"
	default:
		return "未知阶段"
	}
}
