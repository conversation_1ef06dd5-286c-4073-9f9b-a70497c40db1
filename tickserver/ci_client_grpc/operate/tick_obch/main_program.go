package tick_obch

import (
	"trading_tick_server/internal/data_transfer_station/model"
	dts_service "trading_tick_server/internal/data_transfer_station/service"
	"trading_tick_server/internal/data_transfer_station/types"
	"trading_tick_server/internal/user/user_cache"
	"trading_tick_server/lib/function"
	"trading_tick_server/lib/mysql"
	"trading_tick_server/lib/structs"

	ciclientgrpc "trading_tick_server/tickserver/ci_client_grpc"
	"trading_tick_server/tickserver/ci_client_grpc/operate_func"
	"trading_tick_server/tickserver/ci_client_grpc/operate_global"
	"trading_tick_server/tickserver/ci_client_grpc/operate_struct"
	pb "trading_tick_server/tickserver/data_source_grpc/data_source_proto"

	"github.com/sknun/cf/cast"
)

// 盘口数据 此数据是直接传输 不需要保存和计算 只是在传一条最新价格时检测价格是否变化，如果没变化则省略
func ProcessTickOBCh(item *pb.ReceiveDataOrderBook, laData *pb.OrderBookOnePriceMap) {
	operate_global.ShardCount.Inc(item.Platform + item.Symbol + "-TickOB-Count")
	// 判断是否有数据
	if len(item.Asks) < 1 || len(item.Bids) < 1 {
		// 因为没有数据 未下发
		operate_global.ShardCount.Inc(item.Platform + item.Symbol + "-TickOB-NoData")
		return
	}
	if item.TickTime > laData.Time {
		// 比较asks和bids是否发生变化
		if !operate_func.IntEqual(item.Asks[0].Price, laData.AsksPrice1) || !operate_func.IntEqual(item.Bids[0].Price, laData.BidsPrice1) {
			laData.Time = item.TickTime
			laData.AsksPrice1 = item.Asks[0].Price
			laData.BidsPrice1 = item.Bids[0].Price
		} else {
			// 因为价格没有变化只记录但不阻挡下发
			operate_global.ShardCount.Inc(item.Platform + item.Symbol + "-TickOB-NoChange")
		}
	} else {
		// 因为后到的数据时间小于之前的 未下发
		operate_global.ShardCount.Inc(item.Platform + item.Symbol + "-TickOB-TimeBefore")
		return
	}
	for _, v := range ciclientgrpc.CiClientGrpc.GetAllUserIDs() {
		user := user_cache.CacheUserGetOne(v)
		if user.ID < 1 {
			continue
		}

		var userSymbols []structs.UserSymbol
		err := mysql.M.Model(&structs.UserSymbol{}).Where("user_id = ?", user.ID).Find(&userSymbols).Error
		if err == nil {
			user.UserSymbols = userSymbols
		}
		// 为每个用户取自己的channel
		userKey := item.Platform + "_" + item.Symbol + "_" + cast.ToString(user.ID)
		ch, loaded := operate_global.TickOBSubChannels.LoadOrStore(userKey, make(chan *operate_struct.OBChSubData, 30))
		userChan := ch.(chan *operate_struct.OBChSubData)
		if !loaded {
			/*
				携程开启式就需要给定数据处理房间
			*/
			dataRoom, _ := dts_service.GetStationProductRoom[types.ExtraDataPkg](cast.ToString(user.ID), item.Symbol)
			go processTickOBCategorySub(userChan, dataRoom)
		}
		userChan <- &operate_struct.OBChSubData{
			User: user,
			Item: item,
		}
	}
}

func processTickOBCategorySub(channel chan *operate_struct.OBChSubData, dataRoom *model.DataRoom[types.ExtraDataPkg]) {
	for item := range channel {
		if item.User.KlineSymbol[function.GetPlatformSymbol(item.Item.Platform, item.Item.Symbol)] > 0 {
			tickOBChSub(item, dataRoom)
		}
	}
}
