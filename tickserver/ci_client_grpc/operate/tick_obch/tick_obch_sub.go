package tick_obch

import (
	"encoding/json"
	dts_model "trading_tick_server/internal/data_transfer_station/model"
	dts_types "trading_tick_server/internal/data_transfer_station/types"
	"trading_tick_server/lib/cacheimport"
	"trading_tick_server/lib/function"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/logger"
	ciclientgrpc "trading_tick_server/tickserver/ci_client_grpc"
	"trading_tick_server/tickserver/ci_client_grpc/internal_grpc_proto"
	"trading_tick_server/tickserver/ci_client_grpc/operate_global"
	"trading_tick_server/tickserver/ci_client_grpc/operate_struct"
	pb "trading_tick_server/tickserver/data_source_grpc/data_source_proto"
	"trading_tick_server/utils"

	"github.com/shopspring/decimal"

	"github.com/sknun/cf/cast"
	"go.uber.org/zap"
)

// 盘口数据
func tickOBChSub(item *operate_struct.OBChSubData, dataRoom *dts_model.DataRoom[dts_types.ExtraDataPkg]) {
	userID := cast.ToString(item.User.ID)
	symbol := item.Item.Symbol
	platform := item.Item.Platform
	var m internal_grpc_proto.InternalDataSourceRequest
	var b []byte
	var err error
	// 检查插针处理
	tmp := cacheimport.UserKlineShadowGet(userID, symbol)
	if tmp.ID > 0 { // 检测是否有插针要处理
		// 检测插针是否完成
		_, err := function.CalculateFloatValue(
			&tmp,
			tmp.StartTime*1000, // 插针开始时间
			item.Item.TickTime, // 收到这条插针数据的时间
			tmp.RiseTime,
			tmp.PeakTime,
			tmp.FallTime,
		)
		if err == nil {
			if tmp.Type == "intuitive" {
				// 这里修改不返回 放成交里面进行返回
				return
			}
		}
	}
	m.Type = 2

	// 使用用户配置的盘口精度，供所有地方使用
	userObDecimalPlaces := item.User.ObSymbol[function.GetPlatformSymbol(platform, symbol)] // 默认使用原来的盘口精度
	if item.User.UserSymbols != nil {
		for _, userSymbol := range item.User.UserSymbols {
			if userSymbol.Symbol == symbol && userSymbol.Type == "orderBook" {
				userObDecimalPlaces = int32(userSymbol.DecimalPlaces)
				break
			}
		}
	}

	var vData pb.OrderBookPrice
	vData.Symbol = symbol
	vData.Time = item.Item.TickTime
	if dataRoom == nil {
		/*
			这里有个bug,现在如果某个用户没有某个产品的访问权限,这里就会报找不到房间,
			但是正确的方式应该是如果这个用户没有这个产品的访问权限,那么就不应该下发数据给他
			这里先把输出注释掉,等后续有时间再来修复这个问题
		*/
		//fmt.Printf("❌未找到数据传输中心房间，用户ID: %d, 产品: %s\n", item.User.ID, item.Item.Symbol)
	} else if dataRoom.ActiveModifying.Load() { // 房间处于控制状态才需要走这部分逻辑
		// 随机生成一个卖价浮动价格
		asksPrice := utils.RandomDecimalInRange(dataRoom.AsksPriceMin, dataRoom.AsksPriceMax)

		// 生成每一档的 diff（从第1档开始，共 len-1 个）
		asksDiffs := make([]decimal.Decimal, len(item.Item.Asks)-1)
		for i := range asksDiffs {
			asksDiffs[i] = utils.RandomDecimalInRange(dataRoom.OrderBookDiffMin, dataRoom.OrderBookDiffMax)
		}

		// 初始价格 = 控盘价 + 卖价浮动价
		asksPrice = dataRoom.ControlPrice.Add(asksPrice)

		// 添加第0档 - 使用用户配置的盘口精度
		firstAskPrice := asksPrice.Truncate(userObDecimalPlaces).InexactFloat64()
		vData.Asks = append(vData.Asks, pb.OrderBookData{
			Price:  firstAskPrice,
			Volume: item.Item.Asks[0].Volume,
		})

		// 添加第1~N档，每一档递增 - 使用用户配置的盘口精度
		for i := 1; i < len(item.Item.Asks); i++ {
			asksPrice = asksPrice.Add(asksDiffs[i-1])
			vData.Asks = append(vData.Asks, pb.OrderBookData{
				Price:  asksPrice.Truncate(userObDecimalPlaces).InexactFloat64(),
				Volume: item.Item.Asks[i].Volume,
			})
		}

		/*
			计算买单单调递减: 第一个价格使用控盘价格减去价差，确保买1价 < 卖1价
		*/
		bidsDiffs := make([]decimal.Decimal, len(item.Item.Bids))
		for i := 1; i < len(bidsDiffs); i++ {
			bidsDiffs[i] = utils.RandomDecimalInRange(dataRoom.OrderBookDiffMin, dataRoom.OrderBookDiffMax)
		}

		// 买1价应该低于控盘价格，确保买卖价差正常
		firstBidDiff := utils.RandomDecimalInRange(dataRoom.OrderBookDiffMin, dataRoom.OrderBookDiffMax)
		bidsPrice := dataRoom.ControlPrice.Sub(firstBidDiff)
		firstBidPrice := float64(0)
		for i, v := range item.Item.Bids {
			if i > 0 {
				bidsPrice = bidsPrice.Sub(bidsDiffs[i]) // 从第1项开始递减
			}
			// 使用用户配置的盘口精度
			price := bidsPrice.Truncate(userObDecimalPlaces).InexactFloat64()
			if i == 0 {
				firstBidPrice = price
			}
			vData.Bids = append(vData.Bids, pb.OrderBookData{
				Price:  price,
				Volume: v.Volume,
			})
		}

		logger.DataFlow("盘口数据生成完成",
			zap.String("用户ID", userID),
			zap.String("交易对", symbol),
			zap.String("控盘价格", dataRoom.ControlPrice.String()),
			zap.String("卖价浮动价格", asksPrice.String()),
			zap.String("买1价差", firstBidDiff.String()),
			zap.Float64("买一价", firstBidPrice),
			zap.Float64("卖一价", firstAskPrice),
			zap.String("卖价浮动范围", dataRoom.AsksPriceMin.String()+"~"+dataRoom.AsksPriceMax.String()),
			zap.String("档位差价范围", dataRoom.OrderBookDiffMin.String()+"~"+dataRoom.OrderBookDiffMax.String()),
			zap.Float64("买卖价差", firstAskPrice-firstBidPrice),
			zap.Bool("价差正常", firstAskPrice > firstBidPrice),
			zap.Any("买盘", vData.Bids),
			zap.Any("卖盘", vData.Asks))
		// 如果盘口数据的买一价和卖一价都大于0,则下发数据
		if vData.Bids[0].Price > 0 && vData.Asks[0].Price > 0 {
			b, err = json.Marshal(vData)
			if err != nil {
				global.Lg.Error("控盘数据JSON序列化失败",
					zap.String("用户ID", userID),
					zap.String("交易对", symbol),
					zap.Any("vData", vData),
					zap.Error(err))
				return
			}
			m.Message = string(b)
			err = ciclientgrpc.CiClientGrpc.SendMessage(cast.ToString(item.User.ID), &m)
			if err != nil && err.Error() != "user has no active connection" {
				global.Lg.Error("一个价格盘口数据下发失败", zap.Error(err))
			} else {
				operate_global.ShardCount.Inc(item.Item.Platform + item.Item.Symbol + "-TickOB-" + cast.ToString(item.User.ID) + "-Count")
			}
		}
		return
	}

	// 无控盘时也使用用户配置的盘口精度
	for _, v := range item.Item.Asks {
		price := function.FloorPriceDecimal(v.Price, userObDecimalPlaces)
		vData.Asks = append(vData.Asks, pb.OrderBookData{
			Price:  price,
			Volume: v.Volume,
		})
	}

	for _, v := range item.Item.Bids {
		price := function.FloorPriceDecimal(v.Price, userObDecimalPlaces)
		vData.Bids = append(vData.Bids, pb.OrderBookData{
			Price:  price,
			Volume: v.Volume,
		})
	}
	// 如果盘口数据的买一价和卖一价都大于0,则下发数据
	if vData.Bids[0].Price > 0 && vData.Asks[0].Price > 0 {
		b, err = json.Marshal(vData)
		if err != nil {
			global.Lg.Error("原始数据JSON序列化失败",
				zap.String("用户ID", userID),
				zap.String("交易对", symbol),
				zap.Any("vData", vData),
				zap.Error(err))
			return
		}
		m.Message = string(b)
		err = ciclientgrpc.CiClientGrpc.SendMessage(cast.ToString(item.User.ID), &m)
		if err != nil && err.Error() != "user has no active connection" {
			global.Lg.Error("一个价格盘口数据下发失败", zap.Error(err))
		} else {
			operate_global.ShardCount.Inc(item.Item.Platform + item.Item.Symbol + "-TickOB-" + cast.ToString(item.User.ID) + "-Count")
		}
	}
}
