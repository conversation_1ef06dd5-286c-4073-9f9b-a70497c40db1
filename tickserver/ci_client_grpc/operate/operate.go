package operate

import (
	"github.com/sknun/cf/cast"
	"time"
	"trading_tick_server/internal/user/user_cache"
	"trading_tick_server/lib/mysql"
	"trading_tick_server/tickserver/ci_client_grpc/operate/tick_obch"
	"trading_tick_server/tickserver/ci_client_grpc/operate/tick_tradech"
	"trading_tick_server/tickserver/ci_client_grpc/operate_global"
	pb "trading_tick_server/tickserver/data_source_grpc/data_source_proto"

	"trading_tick_server/lib/cache"
	"trading_tick_server/lib/function"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/shard_count"
	"trading_tick_server/lib/structs"

	"go.uber.org/zap"
)

// 处理global通道内的数据并且发下
func ProcessChannelData() {
	// 统计
	operate_global.ShardCount = shard_count.NewShardedCounter(300)
	// 盘口数据 此数据是直接传输 不需要保存和计算 只是在传一条最新价格时检测价格是否变化，如果没变化则省略
	go func() {
		defer func() {
			if r := recover(); r != nil {
				global.Lg.Error("TickOB 协程发生 panic", zap.Any("panic", r), zap.Stack("stack"))
			}
		}()

		for v := range global.TickOBCh {
			// 获取或创建处理通道
			key := v.Platform + "_" + v.Symbol
			ch, loaded := operate_global.TickOBChannels.LoadOrStore(key, make(chan *pb.ReceiveDataOrderBook, 30))
			channel := ch.(chan *pb.ReceiveDataOrderBook)
			// 如果是首次创建该通道，启动独立协程
			if !loaded {
				go processTickOBCategory(channel) // 盘口数据
			}
			// 分发数据到专属通道
			channel <- v
		}
	}()
	// 成交报价 需要保存和计算 然后下发
	go func() {
		defer func() {
			if r := recover(); r != nil {
				global.Lg.Error("TickTrade 协程发生 panic", zap.Any("panic", r), zap.Stack("stack"))
			}
		}()

		for v := range global.TickTradeCh {
			/*
				按照平台和产品符号创建通道,多产品,多平台
				通道数量 = 平台数量 * 产品数量
			*/
			key := v.Platform + "_" + v.Symbol
			ch, loaded := operate_global.TickTradeChannels.LoadOrStore(key, make(chan *pb.ReceiveDataTrade, 30)) // 需要改用 cmap
			channel := ch.(chan *pb.ReceiveDataTrade)
			// 如果是首次创建该通道，启动独立协程
			if !loaded {
				// 取出当前产品的默认时区
				var timeZone string
				for _, vTick := range cache.CacheTickSliceGet() {
					if v.Platform == vTick.Platform && v.Symbol == vTick.Symbol {
						timeZone = vTick.TimeZone
						break
					}
				}
				if timeZone == "" {
					global.Lg.Error("❌当前产品的时区是空的，无是非法的", zap.Any("Platform", v.Platform), zap.Any("Symbol", v.Symbol))
				}
				go processTickTradeCategory(channel, v.Platform, v.Symbol, timeZone) // K 线
			}
			// 分发数据到专属通道
			channel <- v
			/*	if v.Symbol == "BTCUSDT" {
				fmt.Printf("2️⃣%v, 处理 Tick Trade 数据,平台 : %v, 产品 : %v, 价格 : %v ,通道长度: %v \n", time.Now().Format("2006-01-02 15:04:05.000"), v.Platform, v.Symbol, v.Price, len(channel))
			}*/
		}
	}()
	if global.Yaml.StatisticsInterval > 0 {
		// 启动统计协程
		go statistics()
	}
}

func statistics() {
	defer func() {
		if r := recover(); r != nil {
			global.Lg.Error("统计协程发生 panic", zap.Any("panic", r), zap.Stack("stack"))
		}
	}()
	ticker := time.NewTicker(time.Duration(global.Yaml.StatisticsInterval) * time.Minute)
	defer ticker.Stop()
	for range ticker.C {
		statisticsTime := time.Now().Unix()

		// 收集所有要写入的数据
		var tradeStats []structs.TradeReceiveStatistics
		var obStats []structs.OBReceiveStatistics
		var tradeSendStats []structs.TradeSendStatistics
		var obSendStats []structs.OBSendStatistics
		for _, vTick := range cache.CacheTickSliceGet() {
			// 统计成交报价接收
			tickCount := vTick.Platform + vTick.Symbol + "-TickTrade-Count"
			tickNoChange := vTick.Platform + vTick.Symbol + "-TickTrade-NoChange"
			tickTimeBefore := vTick.Platform + vTick.Symbol + "-TickTrade-TimeBefore"
			tickNoData := vTick.Platform + vTick.Symbol + "-TickTrade-NoData"
			tickData := operate_global.ShardCount.GetAll([]string{tickCount, tickNoChange, tickTimeBefore, tickNoData})
			tradeStats = append(tradeStats, structs.TradeReceiveStatistics{
				Platform:        vTick.Platform,
				Symbol:          vTick.Symbol,
				Interval:        global.Yaml.StatisticsInterval,
				StatisticsTime:  statisticsTime,
				Count:           tickData[tickCount],
				NoChangeCount:   tickData[tickNoChange],
				TimeBeforeCount: tickData[tickTimeBefore],
				NoDataCount:     tickData[tickNoData],
			})
			// 统计盘口报价接收
			tickCount = vTick.Platform + vTick.Symbol + "-TickOB-Count"
			tickNoChange = vTick.Platform + vTick.Symbol + "-TickOB-NoChange"
			tickTimeBefore = vTick.Platform + vTick.Symbol + "-TickOB-TimeBefore"
			tickData = operate_global.ShardCount.GetAll([]string{tickCount, tickNoChange, tickTimeBefore})
			obStats = append(obStats, structs.OBReceiveStatistics{
				Platform:        vTick.Platform,
				Symbol:          vTick.Symbol,
				Interval:        global.Yaml.StatisticsInterval,
				StatisticsTime:  statisticsTime,
				Count:           tickData[tickCount],
				NoChangeCount:   tickData[tickNoChange],
				TimeBeforeCount: tickData[tickTimeBefore],
			})
			for _, vUser := range user_cache.CacheUserSliceGet() {
				// 统计成交报价发送
				tickCount := vTick.Platform + vTick.Symbol + "-TickTrade-" + cast.ToString(vUser.ID) + "-Count"
				tickData := operate_global.ShardCount.Get(tickCount)
				if tickData > -1 {
					tradeSendStats = append(tradeSendStats, structs.TradeSendStatistics{
						UserID:         vUser.ID,
						Platform:       vTick.Platform,
						Symbol:         vTick.Symbol,
						Interval:       global.Yaml.StatisticsInterval,
						StatisticsTime: statisticsTime,
						Count:          tickData,
					})
				}
				// 统计盘口报价发送
				tickCount = vTick.Platform + vTick.Symbol + "-TickOB-" + cast.ToString(vUser.ID) + "-Count"
				tickData = operate_global.ShardCount.Get(tickCount)
				if tickData > -1 {
					obSendStats = append(obSendStats, structs.OBSendStatistics{
						UserID:         vUser.ID,
						Platform:       vTick.Platform,
						Symbol:         vTick.Symbol,
						Interval:       global.Yaml.StatisticsInterval,
						StatisticsTime: statisticsTime,
						Count:          tickData,
					})
				}
			}
		}
		// 批量写入数据库
		if len(tradeStats) > 0 {
			if err := mysql.M.CreateInBatches(&tradeStats, 100).Error; err != nil {
				global.Lg.Error("批量记录成交报价接收统计数据失败", zap.Error(err))
			}
		}
		if len(obStats) > 0 {
			if err := mysql.M.CreateInBatches(&obStats, 100).Error; err != nil {
				global.Lg.Error("批量记录盘口报价接收统计数据失败", zap.Error(err))
			}
		}
		if len(tradeSendStats) > 0 {
			if err := mysql.M.CreateInBatches(&tradeSendStats, 100).Error; err != nil {
				global.Lg.Error("批量记录成交报价发送统计数据失败", zap.Error(err))
			}
		}
		if len(obSendStats) > 0 {
			if err := mysql.M.CreateInBatches(&obSendStats, 100).Error; err != nil {
				global.Lg.Error("批量记录盘口报价发送统计数据失败", zap.Error(err))
			}
		}
	}
}

// 处理单个分类(盘口)
func processTickOBCategory(channel chan *pb.ReceiveDataOrderBook) {
	lastData := pb.OrderBookOnePriceMap{}
	for item := range channel {
		// 调用原有逻辑处理每个 item
		tick_obch.ProcessTickOBCh(item, &lastData)
	}
}

// 处理单个分类(成交报价)
func processTickTradeCategory(channel chan *pb.ReceiveDataTrade, platform, symbol, timeZone string) {
	var tickTradeCount, intervalLastStart int64
	/*
		这个变用于记录上一次 datafeed 来的数据,用于和当前数据对比
	*/
	lastData := pb.ReceiveDataTrade{}
	// 实时K线数据
	activeKLines := map[string]*structs.KlineRetData{}
	activeKLines[global.Resolutions.Minute1] = &structs.KlineRetData{}
	activeKLines[global.Resolutions.Minute5] = &structs.KlineRetData{}
	activeKLines[global.Resolutions.Minute15] = &structs.KlineRetData{}
	activeKLines[global.Resolutions.Minute30] = &structs.KlineRetData{}
	activeKLines[global.Resolutions.Hour1] = &structs.KlineRetData{}
	activeKLines[global.Resolutions.Hour4] = &structs.KlineRetData{}
	activeKLines[global.Resolutions.Day] = &structs.KlineRetData{}
	activeKLines[global.Resolutions.Week] = &structs.KlineRetData{}
	activeKLines[global.Resolutions.Month] = &structs.KlineRetData{}
	/*
		将平台和产品符号转换为唯一的键 htx_btcusdt 形式
	*/
	tickKey := function.GetPlatformSymbol(platform, symbol)
	for item := range channel {
		tick_tradech.ProcessTickTradeCh(item, activeKLines, tickKey, &lastData, &tickTradeCount, &intervalLastStart, timeZone)
	}
}
