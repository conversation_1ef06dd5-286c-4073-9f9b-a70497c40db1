package operate_func

import (
	"math"
	"math/rand"
	"sort"
	"sync"
	"time"
	"trading_tick_server/lib/function"
	"trading_tick_server/lib/structs"
)

const scale = 10000

// 价格保护缓存结构
type PriceCache struct {
	Open  float64
	High  float64
	Low   float64
	Close float64
}

// 全局价格缓存，用于价格保护
var (
	lastValidPrices = make(map[string]*PriceCache) // key: userID:symbol
	priceCacheMutex sync.RWMutex
)

// 等于
func IntEqual(a, b float64) bool {
	return int64(a*scale) == int64(b*scale)
}

// 大于
func IntGreater(a, b float64) bool {
	return int64(a*scale) > int64(b*scale)
}

// 小于
func IntLess(a, b float64) bool {
	return int64(a*scale) < int64(b*scale)
}

/*
k线插针 使用的相关功能函数
*/
// CalcCandles 返回三个切片：
// 1. 爬坡期内所有完整 1 分钟 K 线的起始时间戳
// 2. 峰值期内所有完整 1 分钟 K 线的起始时间戳
// 3. 回落期内所有完整 1 分钟 K 线的起始时间戳
//
// 参数单位：
//   - startTime 为 Unix 时间戳（秒）
//   - riseTime、peakTime、fallTime 单位均为秒
func CalcCandles(startTime int64, riseTime, peakTime, fallTime int) (riseCandles, peakCandles, fallCandles []int64) {
	// 1. 定义三个阶段的开始与结束
	riseStart := startTime
	riseEnd := startTime + int64(riseTime)

	peakStart := riseEnd
	peakEnd := riseEnd + int64(peakTime)

	fallStart := peakEnd
	fallEnd := peakEnd + int64(fallTime)

	// 2. 分别计算每个阶段内完整的 1 分钟 K 线起始时间
	riseCandles = gatherOneMinCandles(riseStart, riseEnd)
	peakCandles = gatherOneMinCandles(peakStart, peakEnd)
	fallCandles = gatherOneMinCandles(fallStart, fallEnd)

	return
}

// gatherOneMinCandles 在给定的区间 [start, end) 中，
// 找到所有能完整容纳 [T, T+60) 的整分钟开盘 T。
func gatherOneMinCandles(start, end int64) []int64 {
	var result []int64

	// 如果连一根完整 1 分钟都放不下，则直接返回空
	if end-start < 60 {
		return result
	}

	// 1. 先对齐到区间内的"下一个整分"或"当前整分"
	alignedStart := alignToNextMinute(start)

	// 2. 依次迭代，每次+60s，直到不超出 end - 60
	for t := alignedStart; t <= end-60; t += 60 {
		result = append(result, t)
	}
	return result
}

// alignToNextMinute 将输入秒级时间戳对齐到"下一个"或"当前"整分
// 若本身已经是整分，则不动，否则往后推到下一整分。
func alignToNextMinute(ts int64) int64 {
	remainder := ts % 60
	if remainder == 0 {
		return ts
	}
	return ts + (60 - remainder)
}

// 计算有哪些蜡烛图需要改变
func MarkBars(bars []int64) []bool {
	rand.Seed(time.Now().UnixNano())

	n := len(bars)
	changed := make([]bool, n)
	if n == 0 {
		return changed
	}

	// 1) 若 n<=2：首尾之外无可改动位置，直接返回全 false
	if n <= 2 {
		return changed
	}

	// 2) 若 n=3：60% 概率中间那根为 true，否则 false
	if n == 3 {
		if rand.Float64() < 0.6 {
			changed[1] = true
		}
		return changed
	}

	// ---- 下面处理 n >= 4 的场景 ----

	// 首尾不可改 => 可改动下标范围 [1 .. n-2]
	candidateCount := n - 2

	// 分段确定需要选多少 true 的范围 [minNeeded..maxNeeded]
	minNeeded, maxNeeded := getNeededRange(n, candidateCount)

	// 若可选位都比 minNeeded 还小，就无法满足 => 返回全 false
	if candidateCount < minNeeded {
		return changed
	}

	// 3) “洗牌 + 拒绝采样”直到选出无三连的组合
	//    在 [minNeeded..maxNeeded] 内随机选 k，洗牌 [0..candidateCount-1]
	//    取前 k 个，若出现三连就重试
	for {
		k := rand.Intn(maxNeeded-minNeeded+1) + minNeeded

		perm := rand.Perm(candidateCount)
		chosen := perm[:k]
		sort.Ints(chosen) // 排序以检测三连

		if !hasTripleConsecutive(chosen) {
			// 映射到实际 bars (要 +1)，置 true
			for _, idx := range chosen {
				changed[idx+1] = true
			}
			return changed
		}
		// 出现三连 => 重试
	}
}

// getNeededRange 根据需求分段，给出“本次必须/至少选多少个 true”的上下限：
//   - 4 <= n < 5   => [1..1] (恰好 1 根)
//   - 5 <= n < 8   => [2..2] (恰好 2 根)
//   - 8 <= n < 12  => [3..3] (恰好 3 根)
//   - n >= 12      => [4..candidateCount] (最少 4 根, 不允许超过可选数量)
func getNeededRange(n, candidateCount int) (int, int) {
	switch {
	case n < 4:
		// 不会进这里，因为 n>=4 才会调用
		return 0, 0

	case n < 5:
		// n=4 => 恰好 1 根
		return 1, 1

	case n < 8:
		// n=5,6,7 => 恰好 2 根
		return 2, 2

	case n < 12:
		// n=8,9,10,11 => 恰好 3 根
		return 3, 3

	default:
		// n >= 12 => 至少 4 根
		minNeeded := 4
		maxNeeded := candidateCount
		if minNeeded > maxNeeded {
			// 若候选位不足 4，也只能取 candidateCount
			minNeeded = maxNeeded
		}
		return minNeeded, maxNeeded
	}
}

// hasTripleConsecutive 判断已排序下标切片里是否出现三连 (如 [2,3,4])
func hasTripleConsecutive(sortedIdx []int) bool {
	for i := 2; i < len(sortedIdx); i++ {
		if sortedIdx[i] == sortedIdx[i-1]+1 &&
			sortedIdx[i-1] == sortedIdx[i-2]+1 {
			return true
		}
	}
	return false
}

/*
LinearInterpolateOneMinute
  - currentMs:   当前时间 (毫秒)
  - startPrice:  该分钟开始时的价格
  - endPrice:    该分钟结束时的价格

返回: 在 currentMs 时刻，对应的线性“收盘价”。

原理:
 1. 用 currentMs 推算这一分钟的起止毫秒 (startMs, endMs)。
 2. 计算 fraction = (currentMs - startMs) / (endMs - startMs) 并 clamp 到 [0,1]。
 3. 按线性插值公式得到价格。
*/
func LinearInterpolateOneMinute(
	currentMs int64,
	startPrice, endPrice float64,
	targetStartTimeMs, targetEndTimeMs int64,
) float64 {
	// 1. 算出“本分钟”的起始毫秒(startMs) 和结束毫秒(endMs)

	var startMs, endMs int64
	if targetStartTimeMs == 0 {
		startMs = (currentMs / 60000) * 60000 // 向下取整到整分钟
	} else {
		startMs = targetStartTimeMs
	}

	if targetEndTimeMs == 0 {
		endMs = startMs + 60000 // 1 分钟后
	} else {
		endMs = targetEndTimeMs
	}

	// 为避免极端: 如果恰好 startMs == endMs(不太可能，除非 currentMs 是负数?), 直接返回 endPrice
	if endMs == startMs {
		return endPrice
	}

	// 2. 计算在该分钟内的进度 fraction ∈ [0,1]
	fraction := float64(currentMs-startMs) / float64(endMs-startMs)
	fraction = math.Max(0.0, math.Min(1.0, fraction))

	// 3. 线性插值
	interpolated := startPrice + fraction*(endPrice-startPrice)
	return interpolated
}

/*
ShouldHitNormal: 在 [probMin, probMax] 区间内生成一个正态分布倾向的 p，
然后进行一次 Bernoulli 检验 (r < p)。
*/
func ShouldHitNormal(probMin, probMax float64) bool {
	// 1. 边界处理，防止越界
	if probMin < 0 {
		probMin = 0
	}
	if probMax > 1 {
		probMax = 1
	}
	if probMin > probMax {
		probMin, probMax = probMax, probMin
	}

	// 2. 标准正态取样（均值0，标准差1）
	z := rand.NormFloat64()

	// 3. 将标准正态随机数 z 映射为 [0,1]
	//    Φ(z) = 0.5 * (1 + erf(z / sqrt(2)))
	cdfVal := 0.5 * (1 + math.Erf(z/math.Sqrt2))

	// 4. 映射到 [probMin, probMax]
	p := probMin + (probMax-probMin)*cdfVal

	// 5. Bernoulli 检验
	return rand.Float64() < p
}

/*
处理k线的小数位数
*/
func ChangeKlineDecimalPlaces(s []structs.KlineRetData, dp int32) {
	for k := range s {
		s[k].Open = function.FloorPriceDecimal(s[k].Open, dp)
		s[k].Close = function.FloorPriceDecimal(s[k].Close, dp)
		s[k].High = function.FloorPriceDecimal(s[k].High, dp)
		s[k].Low = function.FloorPriceDecimal(s[k].Low, dp)
	}
}

/*
价格保护：检查并修正K线数据的价格
*/
func ProtectKlinePrice(s []structs.KlineRetData, userID, symbol string) {
	cacheKey := userID + ":" + symbol

	for k := range s {
		// 价格保护：如果 Low <= 0，使用上次有效的所有价格
		if s[k].Low <= 0 {
			priceCacheMutex.RLock()
			lastPrice, exists := lastValidPrices[cacheKey]
			priceCacheMutex.RUnlock()

			if exists {
				s[k].Open = lastPrice.Open
				s[k].High = lastPrice.High
				s[k].Low = lastPrice.Low
				s[k].Close = lastPrice.Close
			}
		} else {
			// 更新价格缓存
			priceCacheMutex.Lock()
			lastValidPrices[cacheKey] = &PriceCache{
				Open:  s[k].Open,
				High:  s[k].High,
				Low:   s[k].Low,
				Close: s[k].Close,
			}
			priceCacheMutex.Unlock()
		}
	}
}

/*
价格保护：检查并修正插针数据的价格
*/
func ProtectShadowPrice(userID, symbol string, shadowData *structs.KlineDataShadow) {
	cacheKey := userID + ":" + symbol

	// 价格保护：如果 Low <= 0，使用上次有效的所有价格
	if shadowData.Low <= 0 {
		priceCacheMutex.RLock()
		lastPrice, exists := lastValidPrices[cacheKey]
		priceCacheMutex.RUnlock()

		if exists {
			shadowData.Open = lastPrice.Open
			shadowData.High = lastPrice.High
			shadowData.Low = lastPrice.Low
			shadowData.Close = lastPrice.Close
		}
	} else {
		// 更新价格缓存
		priceCacheMutex.Lock()
		lastValidPrices[cacheKey] = &PriceCache{
			Open:  shadowData.Open,
			High:  shadowData.High,
			Low:   shadowData.Low,
			Close: shadowData.Close,
		}
		priceCacheMutex.Unlock()
	}
}
