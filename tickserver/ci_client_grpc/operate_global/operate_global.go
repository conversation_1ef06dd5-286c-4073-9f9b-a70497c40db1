package operate_global

import (
	"sync"
	"trading_tick_server/lib/shard_count"
)

// 用于存储每个 Platform-Symbol 的处理通道
var TickOBSubChannels sync.Map
var TickTradeSubChannels sync.Map

// 用于存储每个 Platform-Symbol 的处理通道
var TickOBChannels sync.Map
var TickTradeChannels sync.Map

// 日志类型
type MonitorType string

const (
	MonitorTypeOB    MonitorType = "ob"    // 记录盘口日志
	MonitorTypeTrade MonitorType = "trade" // 记录成交日志
)

var ShardCount *shard_count.ShardedCounter
