package extfunction

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"time"
	"trading_tick_server/lib/function"
	"trading_tick_server/lib/global"
	pb "trading_tick_server/tickserver/data_source_grpc/data_source_proto"

	"github.com/sknun/cf/cast"
	"go.uber.org/zap"
)

/*
从客户端取k线数据
uri 客户端地址
token 客户端生成后提交到服务端的
codetype 类型
code 代码
resolutions k线时间颗粒
endtime 结束时间戳
num 数量
*/
func QueryKlineCurl(uri, token, codetype, code, resolutions string, endtime int64, num int) ([]pb.ReceiveDataKline, error) {
	var m pb.RetDataKline
	url_ := fmt.Sprintf(
		"%v/historykline?token=%v&codetype=%v&code=%v&resolutions=%v&endtime=%v&num=%v",
		uri,
		token,
		codetype,
		code,
		resolutions,
		endtime,
		num,
	)
	var err error
	var b2 []byte
	for i := 0; i < 3; i++ {
		b2, err = function.HttpCurlGet(url_)
		if err != nil {
			time.Sleep(time.Second * 2)
			continue
		}
		err = json.Unmarshal(b2, &m)
		if err != nil {
			time.Sleep(time.Second * 2)
			continue
		}
		if m.Code != 200 {
			time.Sleep(time.Second * 2)
		} else {
			return m.Data, err
		}
	}
	return m.Data, err
}

/*
从客户端取k线数据
uri 客户端地址
token 客户端生成后提交到服务端的
codetype 类型
code 代码
resolutions k线时间颗粒
endtime 结束时间戳
*/
func QueryBatchKlineCurl(uri, token string, resolutions []string, endtime int64, symbols []pb.NotifyClientData) ([]pb.ReceiveDataKline, error) {
	var kline pb.RetDataKline
	var err error
	var b1, b2, bRet []byte
	b1, err = json.Marshal(symbols)
	if err != nil {
		return kline.Data, err
	}
	b2, err = json.Marshal(resolutions)
	if err != nil {
		return kline.Data, err
	}
	url_ := fmt.Sprintf(
		"%v/historybatchkline?token=%v&resolutions=%v&endtime=%v&query=%v",
		uri,
		token,
		url.QueryEscape(string(b2)),
		endtime,
		url.QueryEscape(string(b1)),
	)
	for i := 0; i < 10; i++ {
		bRet, err = function.HttpCurlGet(url_)
		if err != nil {
			time.Sleep(time.Second * 2)
			continue
		}
		err = json.Unmarshal(bRet, &kline)
		if err != nil {
			time.Sleep(time.Second * 2)
			continue
		}
		if kline.Code != 200 {
			time.Sleep(time.Second * 2)
		} else {
			return kline.Data, err
		}
	}
	return kline.Data, err
}

/*
通知客户端上报k线
*/
func NotifyClientKline(uri, token string, code []pb.NotifyClientData) error {
	b, err := json.Marshal(code)
	if err != nil {
		return err
	}
	url_ := fmt.Sprintf(
		"%v/notifykline?token=%v&query=%v",
		uri,
		token,
		url.QueryEscape(string(b)),
	)
	b2, err := function.HttpCurlGet(url_)
	if err != nil {
		return err
	}
	var m map[string]interface{}
	err = json.Unmarshal(b2, &m)
	if err != nil {
		return err
	}
	if cast.ToInt(m["code"]) != 200 {
		global.Lg.Error(
			"通知客户端上报k线失败",
			zap.String("url_", url_),
			zap.String("b2", string(b2)),
			zap.Any("m", m),
			zap.String("component", "notifyClientKline"),
			zap.Error(err),
		)
		return errors.New("请求失败")
	}
	return nil
}

/*
通知客户端上报盘口数据
*/
func NotifyClientOrderBook(uri, token string, code []pb.NotifyClientData) error {
	b, err := json.Marshal(code)
	if err != nil {
		return err
	}
	url_ := fmt.Sprintf(
		"%v/notifyorderbook?token=%v&query=%v",
		uri,
		token,
		url.QueryEscape(string(b)),
	)
	b2, err := function.HttpCurlGet(url_)
	if err != nil {
		return err
	}
	var m map[string]interface{}
	err = json.Unmarshal(b2, &m)
	if err != nil {
		return err
	}
	if cast.ToInt(m["code"]) != 200 {
		global.Lg.Error(
			"通知客户端上报盘口数据失败",
			zap.String("url_", url_),
			zap.String("b2", string(b2)),
			zap.Any("m", m),
			zap.String("component", "notifyClientOrderBook"),
			zap.Error(err),
		)
		return errors.New("请求失败")
	}
	return nil
}
