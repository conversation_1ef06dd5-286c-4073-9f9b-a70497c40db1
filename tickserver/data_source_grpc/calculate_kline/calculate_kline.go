package calculatekline

import (
	"time"
	"trading_tick_server/lib/cache"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/influxdb"
	pb "trading_tick_server/tickserver/data_source_grpc/data_source_proto"
	"trading_tick_server/tickserver/data_source_grpc/extfunction"

	"go.uber.org/zap"
)

// 计算K线
func CalculateKlineOnce(clientInfo pb.ReceiveClientInfo, symbols []pb.NotifyClientData) {
	// 启动取最新k线程序
	if oldVal, ok := global.ClientMap.Load(clientInfo.Platform + "CalculateKline"); ok {
		if stopChan, ok2 := oldVal.(chan struct{}); ok2 {
			close(stopChan)
			global.ClientMap.Delete(clientInfo.Platform + "CalculateKline")
		}
	}
	stopChan := make(chan struct{})
	global.ClientMap.Store(clientInfo.Platform+"CalculateKline", stopChan)
	var timeZone string
	for _, v := range cache.CacheTickSliceGet() {
		if v.Platform == clientInfo.Platform {
			// 默认情况下每个平台都有自己唯一的默认时区
			timeZone = v.TimeZone
			break
		}
	}
	go runCalculateKline(stopChan, clientInfo, symbols, timeZone)
}

/*
获取最新k线
*/
func runCalculateKline(stopChan chan struct{}, clientInfo pb.ReceiveClientInfo, symbols []pb.NotifyClientData, timeZone string) {
	intervalSec := 10
	ticker := time.NewTicker(time.Duration(intervalSec) * time.Second)
	defer ticker.Stop()
	for {
		select {
		case <-stopChan:
			return
		case <-ticker.C:
			go calculateKlineSub(clientInfo, symbols, timeZone)
		}
	}
}

func calculateKlineSub(clientInfo pb.ReceiveClientInfo, symbols []pb.NotifyClientData, timeZone string) {
	data, err := extfunction.QueryBatchKlineCurl(clientInfo.Url, clientInfo.Token, global.ArrResolutions, 0, symbols)
	if err != nil {
		global.Lg.Error("获取最新k线失败", zap.Any("clientInfo", clientInfo), zap.Error(err))
	}
	if len(data) > 0 {
		err = influxdb.WriteKlineData(data, timeZone)
		if err != nil {
			global.Lg.Error("存储最新k线失败", zap.Any("data", data), zap.Any("clientInfo", clientInfo), zap.Error(err))
		}
	}
}
