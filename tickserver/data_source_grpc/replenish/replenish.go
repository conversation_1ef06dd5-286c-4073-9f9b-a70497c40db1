package replenish

import (
	"errors"
	"sync"
	"trading_tick_server/lib/function"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/influxdb"
	"trading_tick_server/lib/structs"
	"trading_tick_server/tickserver/data_source_grpc/extfunction"

	"github.com/sknun/cf/cast"
	"go.uber.org/zap"
)

/*
补齐1分钟k线
取数据源允许的最大数条k线来计算是否需要补充 也就是数据源客户端最多可以断线 数据源允许的最大数条/60 小时，超出这个时间将无法再补充
后期需要单独使用一个程序来读取1分钟K线，3小时读取一次，如果失败发出telegram通知
*/
func replenishDataMinute1(info symbolInfo, results chan<- error, wg *sync.WaitGroup) {
	defer wg.Done()
	table := function.GetInfluxDBTable(info.Platform, info.Symbol, info.InitTimeZone, global.Resolutions.Minute1)
	// 取N条数据进行补充
	data, err := extfunction.QueryKlineCurl(info.Url, info.Token, info.Type, info.Symbol, global.Resolutions.Minute1, info.InitTime, int(info.Oncecount))
	if err != nil {
		global.Lg.Error(
			"补充数据时向数据源取k线数据失败",
			zap.String("table", info.Url),
			zap.String("Token", info.Token),
			zap.String("Type", info.Type),
			zap.String("Symbol", info.Symbol),
			zap.Uint32("Oncecount", info.Oncecount),
			zap.String("table", info.Url),
			zap.String("component", "replenishDataMinute1"),
			zap.Error(err),
		)
		results <- err
		return
	}
	n := len(data)
	if n < 1 {
		global.Lg.Error(
			"补充向数据源取k线数据失败--数据条数对不上并数据为空",
			zap.String("table", info.Url),
			zap.String("Token", info.Token),
			zap.String("Type", info.Type),
			zap.String("Symbol", info.Symbol),
			zap.Uint32("Oncecount", info.Oncecount),
			zap.String("table", info.Url),
			zap.String("component", "replenishDataMinute1"),
		)
		results <- errors.New("补充向数据源取k线数据失败--数据条数对不上并数据为空")
		return
	}
	var dataNew []structs.AllDataKline
	for _, v := range data {
		dataNew = append(dataNew, structs.AllDataKline{
			Time:     v.Time,
			Open:     v.Open,
			Close:    v.Close,
			High:     v.High,
			Low:      v.Low,
			Volume:   v.Volume,
			Turnover: v.Turnover,
		})
	}
	// 进行补充数据
	err = influxdb.BatchWriteKlineData(table, dataNew, "replenishDataMinute1")
	if err != nil {
		if global.Yaml.Logging.InfluxDB {
			global.Lg.Error(
				"补充写入k线数据失败",
				zap.String("table", info.Url),
				zap.String("Token", info.Token),
				zap.String("Type", info.Type),
				zap.String("Symbol", info.Symbol),
				zap.Uint32("Oncecount", info.Oncecount),
				zap.String("table", info.Url),
				zap.Error(err),
			)
		}
		results <- err
		return
	}
	if len(dataNew) > 0 {
		info.InitTime = dataNew[len(dataNew)-1].Time
	}
	global.Lg.Info(
		info.Platform+"--"+info.Symbol+"补充'1m'数据完成，补充结束时间戳："+cast.ToString(info.InitTime)+",取数据量"+cast.ToString(len(dataNew)),
		zap.String("table", table),
	)
	results <- nil
}

func replenishDataOther(info symbolInfo) {
	replenishDataOtherSubNew(info, global.Resolutions.Minute5)
	replenishDataOtherSubNew(info, global.Resolutions.Minute15)
	replenishDataOtherSubNew(info, global.Resolutions.Minute30)
	replenishDataOtherSubNew(info, global.Resolutions.Hour1)
	replenishDataOtherSubNew(info, global.Resolutions.Hour4)
	replenishDataOtherSubNew(info, global.Resolutions.Day)
	replenishDataOtherSubNew(info, global.Resolutions.Week)
	replenishDataOtherSubNew(info, global.Resolutions.Month)
}

func replenishDataOtherSubNew(info symbolInfo, resolution string) {
	table := function.GetInfluxDBTable(info.Platform, info.Symbol, info.InitTimeZone, resolution)
	data, err := extfunction.QueryKlineCurl(info.Url, info.Token, info.Type, info.Symbol, resolution, 0, int(info.Oncecount))
	if err == nil {
		var dataNew []structs.AllDataKline
		for _, v := range data {
			dataNew = append(dataNew, structs.AllDataKline{
				Platform: v.Platform,
				Symbol:   v.Symbol,
				Time:     v.Time,
				Open:     v.Open,
				Close:    v.Close,
				High:     v.High,
				Low:      v.Low,
				Volume:   v.Volume,
				Turnover: v.Turnover,
			})
		}
		if len(dataNew) > 0 {
			influxdb.BatchWriteKlineData(table, dataNew, resolution)
			needTIme := dataNew[len(dataNew)-1].Time
			global.Lg.Info(
				info.Platform+"--"+info.Symbol+"补充'"+resolution+"'数据完成，补充结束时间戳："+cast.ToString(needTIme)+",取数据量"+cast.ToString(len(dataNew)),
				zap.String("table", table),
			)
		} else {
			global.Lg.Info(
				info.Platform+"--"+info.Symbol+"补充'"+resolution+"'时取数据为空",
				zap.String("table", table),
			)
		}
	} else {
		global.Lg.Error(
			info.Platform+"--"+info.Symbol+":"+resolution+"补齐数据时出错",
			zap.Error(err),
		)
	}
}
