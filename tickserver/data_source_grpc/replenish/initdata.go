package replenish

import (
	"errors"
	"fmt"
	"sync"
	"time"
	"trading_tick_server/data-provider/databento"
	"trading_tick_server/lib/function"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/influxdb"
	"trading_tick_server/lib/mysql"
	"trading_tick_server/lib/structs"
	calculatekline "trading_tick_server/tickserver/data_source_grpc/calculate_kline"
	pb "trading_tick_server/tickserver/data_source_grpc/data_source_proto"
	"trading_tick_server/tickserver/data_source_grpc/extfunction"

	"go.uber.org/zap"
)

/*
initDataMinute1
initDataMinute5
initDataMinute15
initDataMinute30
initDataHour1
initDataHour4
initDataDay
initDataWeek
initDataMonth
*/
type symbolInfo struct {
	symbolInfoCommon
	InitTime   int64  // 初始化的时间
	mark       string // 标记
	Resolution string // 当前时间颗粒
}

type symbolInfoCommon struct {
	Platform       string // 平台
	Url            string // 客户端请求地址
	Token          string // 客户端请求token
	Symbol         string // 产品标识
	ReplaceSymbol  string // 替换产品标识
	Type           string // 类型 stock股票 forex外汇 crypto代币 commodity商品
	Maximum        uint32 // 最大更新数据量上限
	Oncecount      uint32 // 单次数量
	DesignatedTime int64  // 指定取到历史数据的时间
	InitTimeZone   string // 初始需要生成数据的时区 对30分钟以上生效
}

/*
检测是否需要初始化
初始化规则：完成时InitDone值设置为1，值不为1时开始初始化
*/
func InitData(param pb.ReceiveClientInfo) {
	var symbol []structs.Tick
	mysql.M.Model(&structs.Tick{}).Where("status = 1").Find(&symbol)
	if len(symbol) < 1 {
		global.Lg.Error("❌没有任何需要处理的产品", zap.Any("param", param), zap.Any("symbol", symbol))
		return
	}

	var notifyData []pb.NotifyClientData
	var err error
	for _, v := range symbol {
		notifyData = append(notifyData, pb.NotifyClientData{
			Symbol:   v.Symbol,
			Type:     v.Type,
			Platform: v.Platform, // 平台
		})
	}
	// 先通知上报盘口数据
	err = extfunction.NotifyClientOrderBook(param.Url, param.Token, notifyData)
	if err != nil {
		time.Sleep(time.Second * 2)
		err = extfunction.NotifyClientOrderBook(param.Url, param.Token, notifyData)
		if err != nil {
			global.Lg.Error("❌先通知上报盘口数据失败", zap.Any("param", param), zap.Any("notifyData", notifyData), zap.Error(err))
			return
		}
	} else {
		global.Lg.Info("✅通知上报盘口数据完成", zap.Any("param", param), zap.Any("notifyData", notifyData))
	}
	// 检测是否需要初始化
	for _, v := range symbol {
		if v.InitDone != 1 {
			fmt.Printf("🍖开始初始化数据,ID:%v,  产品: %v\n", v.ID, v.Symbol)
			// 检测初始化时间是否设定 如果未设置 取最新一分钟
			if v.InitTime == 0 {
				v.InitTime = time.Now().Truncate(time.Minute).Unix()
				if err := mysql.M.Model(&structs.Tick{}).Where("id=?", v.ID).Update("init_time", v.InitTime).Error; err != nil {
					global.Lg.Error("❌更新init_time时间失败", zap.Any("param", param), zap.Any("v", v), zap.Error(err))
					return
				}
			}

			if v.Platform == databento.DATABENTO {
				go func(v structs.Tick) {
					_, err := databento.ReplenishKlineHistory(param.Url, param.Token, v.Symbol, v.DesignatedTime, v.InitTime, v.TimeZone)
					if err != nil {
						global.Lg.Error("❌Databento补齐数据失败", zap.Any("param", param), zap.Any("v", v), zap.Error(err))
						return
					}
					if err := mysql.M.Model(&structs.Tick{}).Where("id=?", v.ID).Update("init_done", 1).Error; err != nil {
						global.Lg.Error("❌更新init_done状态失败", zap.Any("param", param), zap.Any("v", v), zap.Error(err))
						return
					}
				}(v)
				continue
			}

			// 所有时间颗粒一起更新
			// 15分钟的呢?
			minute30 := function.TruncateTIme(v.InitTime, v.TimeZone, global.Resolutions.Minute30)
			if minute30 < 1 {
				global.Lg.Error("❌Minute30取时间戳失败", zap.Any("p", param), zap.Any("i", v.InitTime), zap.Any("t", minute30), zap.Error(err))
				return
			}
			hour1 := function.TruncateTIme(v.InitTime, v.TimeZone, global.Resolutions.Hour1)
			if hour1 < 1 {
				global.Lg.Error("❌Hour1取时间戳失败", zap.Any("p", param), zap.Any("i", v.InitTime), zap.Any("t", hour1), zap.Error(err))
				return
			}
			hour4 := function.TruncateTIme(v.InitTime, v.TimeZone, global.Resolutions.Hour4)
			if hour4 < 1 {
				global.Lg.Error("❌Hour4取时间戳失败", zap.Any("p", param), zap.Any("i", v.InitTime), zap.Any("t", hour4), zap.Error(err))
				return
			}
			day := function.TruncateTIme(v.InitTime, v.TimeZone, global.Resolutions.Day)
			if day < 1 {
				global.Lg.Error("❌Day取时间戳失败", zap.Any("p", param), zap.Any("i", v.InitTime), zap.Any("t", day), zap.Error(err))
				return
			}
			week := function.TruncateTIme(v.InitTime, v.TimeZone, global.Resolutions.Week)
			if week < 1 {
				global.Lg.Error("❌Week取时间戳失败", zap.Any("p", param), zap.Any("i", v.InitTime), zap.Any("t", week), zap.Error(err))
				return
			}
			month := function.TruncateTIme(v.InitTime, v.TimeZone, global.Resolutions.Month)
			if month < 1 {
				global.Lg.Error("❌Month取时间戳失败", zap.Any("p", param), zap.Any("i", v.InitTime), zap.Any("t", month), zap.Error(err))
				return
			}
			results := make(chan error, 9)
			var wg sync.WaitGroup
			wg.Add(9)
			symbolinfocommon := symbolInfoCommon{
				Platform:       v.Platform,
				Url:            param.Url,
				Token:          param.Token,
				Symbol:         v.Symbol,
				ReplaceSymbol:  v.ReplaceSymbol,
				Type:           v.Type,
				Maximum:        v.Maximum,
				Oncecount:      v.Oncecount,
				DesignatedTime: v.DesignatedTime,
				InitTimeZone:   v.TimeZone,
			}
			go initDataMinute1(symbolInfo{
				symbolInfoCommon: symbolinfocommon,
				InitTime:         v.InitTime - 60,
				mark:             "initDataMinute1",
				Resolution:       global.Resolutions.Minute1,
			}, results, &wg)
			go initDataOther(symbolInfo{
				symbolInfoCommon: symbolinfocommon,
				InitTime:         function.TruncateTIme(v.InitTime, v.TimeZone, global.Resolutions.Minute5),
				mark:             "initDataMinute5",
				Resolution:       global.Resolutions.Minute5,
			}, results, &wg)

			go initDataOther(symbolInfo{
				symbolInfoCommon: symbolinfocommon,
				InitTime:         function.TruncateTIme(v.InitTime, v.TimeZone, global.Resolutions.Minute15),
				mark:             "initDataMinute15",
				Resolution:       global.Resolutions.Minute15,
			}, results, &wg)

			go initDataOther(symbolInfo{
				symbolInfoCommon: symbolinfocommon,
				InitTime:         minute30,
				mark:             "initDataMinute30",
				Resolution:       global.Resolutions.Minute30,
			}, results, &wg)

			go initDataOther(symbolInfo{
				symbolInfoCommon: symbolinfocommon,
				InitTime:         hour1,
				mark:             "initDataHour1",
				Resolution:       global.Resolutions.Hour1,
			}, results, &wg)

			go initDataOther(symbolInfo{
				symbolInfoCommon: symbolinfocommon,
				InitTime:         hour4,
				mark:             "initDataHour4",
				Resolution:       global.Resolutions.Hour4,
			}, results, &wg)

			go initDataOther(symbolInfo{
				symbolInfoCommon: symbolinfocommon,
				InitTime:         day,
				mark:             "initDataDay",
				Resolution:       global.Resolutions.Day,
			}, results, &wg)

			go initDataOther(symbolInfo{
				symbolInfoCommon: symbolinfocommon,
				InitTime:         week,
				mark:             "initDataWeek",
				Resolution:       global.Resolutions.Week,
			}, results, &wg)

			go initDataOther(symbolInfo{
				symbolInfoCommon: symbolinfocommon,
				InitTime:         month,
				mark:             "initDataMonth",
				Resolution:       global.Resolutions.Month,
			}, results, &wg)
			// 启动一个 goroutine 来关闭 channel，当所有任务完成时
			go func() {
				wg.Wait()
				close(results)
			}()
			// 读取 results 结果
			for err := range results {
				if err != nil {
					global.Lg.Error("❌初始化数据时出错", zap.Any("results", results), zap.Error(err))
					return
				}
			}
			// 全部处理完修改状态
			if err := mysql.M.Model(&structs.Tick{}).Where("id=?", v.ID).Update("init_done", 1).Error; err != nil {
				global.Lg.Error("❌更新init_done状态失败", zap.Any("param", param), zap.Any("v", v), zap.Error(err))
				return
			}
		}
	}
	// 先开启取最新k线和备份协程
	calculatekline.CalculateKlineOnce(param, notifyData)
	// 初始化完成后检查补齐数据
	// 进入补齐程序 补齐只针对1分钟k线，其它k线不在这里进行
	results := make(chan error, 9)
	var wg sync.WaitGroup
	for _, v := range symbol {
		wg.Add(1)
		symbolinfocommon := symbolInfoCommon{
			Platform:       v.Platform,
			Url:            param.Url,
			Token:          param.Token,
			Symbol:         v.Symbol,
			ReplaceSymbol:  v.ReplaceSymbol,
			Type:           v.Type,
			Maximum:        v.Maximum,
			Oncecount:      v.Oncecount,
			DesignatedTime: v.DesignatedTime,
			InitTimeZone:   v.TimeZone,
		}
		go replenishDataMinute1(symbolInfo{
			symbolInfoCommon: symbolinfocommon,
			InitTime:         0,
		}, results, &wg)
	}
	// 启动一个 goroutine 来关闭 channel，当所有任务完成时
	go func() {
		wg.Wait()
		close(results)
	}()
	// 读取 results 结果
	for err := range results {
		if err != nil {
			global.Lg.Error("❌补齐数据时出错", zap.Any("results", results), zap.Error(err))
			// 出错时也要执行取k线
			break
		}
	}
	// 如果结果没有错误 补齐其它时间颗粒K线并开始执行取实时k线
	for _, v := range symbol {
		symbolinfocommon := symbolInfoCommon{
			Platform:       v.Platform,
			Url:            param.Url,
			Token:          param.Token,
			Symbol:         v.Symbol,
			ReplaceSymbol:  v.ReplaceSymbol,
			Type:           v.Type,
			Maximum:        v.Maximum,
			Oncecount:      v.Oncecount,
			DesignatedTime: v.DesignatedTime,
			InitTimeZone:   v.TimeZone,
		}
		go replenishDataOther(symbolInfo{
			symbolInfoCommon: symbolinfocommon,
			InitTime:         0,
		})
	}
	// 向数据源客户端发送取实时k线通知
	err = extfunction.NotifyClientKline(param.Url, param.Token, notifyData)
	if err != nil {
		time.Sleep(time.Second * 2)
		err = extfunction.NotifyClientKline(param.Url, param.Token, notifyData)
		if err != nil {
			global.Lg.Error("❌通知上报k线数据失败", zap.Any("param", param), zap.Any("notifyData", notifyData), zap.Error(err))
		}
	} else {
		global.Lg.Info("✅通知上报k线数据完成", zap.Any("param", param), zap.Any("notifyData", notifyData))
	}

	// 补齐最近24小时的K线数据
	go ReplenishLast24HoursOnStartup(param)
}

// 初始化1分钟k线 时间戳为整分
func initDataMinute1(info symbolInfo, results chan<- error, wg *sync.WaitGroup) {
	defer wg.Done()
	table := function.GetInfluxDBTable(info.Platform, info.Symbol, info.InitTimeZone, global.Resolutions.Minute1)
	var count uint32
	var end bool
	var lastTIme int64
	var err error
	for {
		if lastTIme == 0 {
			lastTIme, err = influxdb.GetMinTimestamp(table, "real")
			if err != nil {
				global.Lg.Error("取最小时间戳失败", zap.Any("table", table), zap.Error(err))
				results <- err
				return
			}
			if lastTIme == 0 {
				// 查询数据为空的时候 置为数据查询截止时间
				lastTIme = info.InitTime
			}
		}
		if lastTIme <= info.DesignatedTime {
			// 说明初始化结束
			break
		}
		// 以lastTIme的时间向前取数据
		var data []pb.ReceiveDataKline
		if info.Platform == databento.DATABENTO {
			data, err = databento.QueryKlineHistory(info.Url, info.Symbol, global.Resolutions.Minute1, lastTIme, info.DesignatedTime)
		} else {
			data, err = extfunction.QueryKlineCurl(info.Url, info.Token, info.Type, info.Symbol, global.Resolutions.Minute1, lastTIme, int(info.Oncecount))
		}
		if err != nil {
			global.Lg.Error("❌初始化向数据源取k线数据失败", zap.Any("info", info), zap.Int64("lastTIme", lastTIme), zap.Int("len_data", len(data)), zap.Error(err))
			results <- err
			return
		}
		if len(data) != int(info.Oncecount) {
			if len(data) < 1 {
				global.Lg.Error(
					"初始化向数据源取k线数据失败--数据条数对不上并数据为空，原因一：数据源不存在",
					zap.Any("info", info),
					zap.Int64("lastTIme", lastTIme),
					zap.Int("len_data", len(data)),
				)
				results <- errors.New("初始化向数据源取k线数据失败--数据条数对不上并数据为空")
				return
			} else {
				global.Lg.Info(
					"初始化向数据源取k线数据--数据条数对不上，认为数据已经取完，初始化结束",
					zap.Any("info", info),
					zap.Int64("lastTIme", lastTIme),
					zap.Int("len_data", len(data)),
				)
				end = true
			}
		}
		// 开始写入数据
		var dataNew []structs.AllDataKline
		for _, v := range data {
			dataNew = append(dataNew, structs.AllDataKline{
				Platform: v.Platform,
				Symbol:   v.Symbol,
				Time:     v.Time,
				Open:     v.Open,
				Close:    v.Close,
				High:     v.High,
				Low:      v.Low,
				Volume:   v.Volume,
				Turnover: v.Turnover,
			})
		}
		count += info.Oncecount
		if info.Maximum > 0 && count >= info.Maximum {
			end = true
		} else {
			lastTIme = data[0].Time
			if lastTIme <= info.DesignatedTime || end {
				// 已经取到指定时间最早的数据 需要结束
				end = true
			}
		}
		err = influxdb.BatchWriteKlineData(table, dataNew, "initDataMinute1")
		if err != nil {
			if global.Yaml.Logging.InfluxDB {
				global.Lg.Error(
					"❌初始化写入k线数据失败",
					zap.Any("info", info),
					zap.Int64("lastTIme", lastTIme),
					zap.Int("len_data", len(data)),
					zap.Any("data", data),
					zap.Any("dataNew", dataNew),
					zap.Error(err),
				)
			}
			results <- err
			return
		}
		if end {
			break
		}
	}
	results <- nil
}

// 初始化其它K线
func initDataOther(info symbolInfo, results chan<- error, wg *sync.WaitGroup) {
	defer wg.Done()
	table := function.GetInfluxDBTable(info.Platform, info.Symbol, info.InitTimeZone, info.Resolution)
	var count uint32
	var end bool
	for {
		lastTIme, err := influxdb.GetMinTimestamp(table, "real")
		if err != nil {
			global.Lg.Error("取最小时间戳失败", zap.Any("table", table), zap.String("component", info.mark), zap.Error(err))
			results <- err
			return
		}
		if lastTIme == 0 {
			// 查询数据为空的时候 置为数据查询截止时间
			lastTIme = info.InitTime
		}
		if lastTIme <= info.DesignatedTime {
			// 说明初始化结束
			break
		}
		// 以lastTIme的时间向前取数据
		data, err := extfunction.QueryKlineCurl(info.Url, info.Token, info.Type, info.Symbol, info.Resolution, lastTIme, int(info.Oncecount))
		fmt.Printf("🍖QueryKilneCurl, %v, %v, %v, %v, %v, %v, %v \n", info.Url, info.Token, info.Type, info.Symbol, info.Resolution, lastTIme, int(info.Oncecount))
		fmt.Printf("🍖QueryKilneCurl, data : %v, err : %v \n", data, err)
		if err != nil {
			global.Lg.Error(
				"初始化向数据源取k线数据失败",
				zap.Any("info", info),
				zap.Int64("lastTIme", lastTIme),
				zap.Int("len_data", len(data)),
				zap.String("component", info.mark),
				zap.Error(err),
			)
			results <- err
			return
		}
		if len(data) != int(info.Oncecount) {
			if len(data) < 1 {
				global.Lg.Error(
					"初始化向数据源取k线数据失败--数据条数对不上并数据为空，原因一：数据源不存在",
					zap.Any("info", info),
					zap.Int64("lastTIme", lastTIme),
					zap.Int("len_data", len(data)),
					zap.String("component", info.mark),
				)
				break
			} else {
				global.Lg.Info(
					"初始化向数据源取k线数据失败--数据条数对不上，认为数据已经取完，初始化结束",
					zap.Any("info", info),
					zap.Int64("lastTIme", lastTIme),
					zap.Int("len_data", len(data)),
					zap.String("component", info.mark),
				)
				end = true
			}
		}
		var dataNew []structs.AllDataKline
		for _, v := range data {
			dataNew = append(dataNew, structs.AllDataKline{
				Time:     v.Time,
				Open:     v.Open,
				Close:    v.Close,
				High:     v.High,
				Low:      v.Low,
				Volume:   v.Volume,
				Turnover: v.Turnover,
			})
		}
		// 开始写入数据
		count += info.Oncecount
		if info.Maximum > 0 && count >= info.Maximum {
			// 达到取数据上限时把第一条数据写入数据库之中并且结束
			end = true
		} else {
			lastTIme = data[0].Time
			if lastTIme <= info.DesignatedTime || end {
				// 已经取到指定时间最早的数据 需要结束
				end = true
			}
		}
		err = influxdb.BatchWriteKlineData(table, dataNew, "initDataOther-"+table)
		if err != nil {
			if global.Yaml.Logging.InfluxDB {
				global.Lg.Error(
					"初始化写入k线数据失败",
					zap.Any("info", info),
					zap.Int64("lastTIme", lastTIme),
					zap.Int("len_data", len(data)),
					zap.Any("data", data),
					zap.String("component", info.mark),
					zap.Error(err),
				)
			}
			results <- err
			return
		}
		if end {
			break
		}
	}
	results <- nil
}
