package replenish

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"sync"
	"time"

	"trading_tick_server/data-provider/databento"
	"trading_tick_server/lib/function"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/influxdb"
	"trading_tick_server/lib/mysql"
	"trading_tick_server/lib/structs"
	pb "trading_tick_server/tickserver/data_source_grpc/data_source_proto"

	"go.uber.org/zap"
)

// ReplenishLast24Hours 补齐所有产品最近24小时的K线数据
func ReplenishLast24Hours(param pb.ReceiveClientInfo) error {
	log.Printf("🔄 开始补齐最近24小时的K线数据...")

	// 获取所有需要处理的产品
	var symbols []structs.Tick
	mysql.M.Model(&structs.Tick{}).Where("status = 1").Find(&symbols)
	if len(symbols) < 1 {
		return fmt.Errorf("没有任何需要处理的产品")
	}

	// 使用 WaitGroup 并发处理所有产品
	var wg sync.WaitGroup
	errChan := make(chan error, len(symbols))

	for _, symbol := range symbols {
		// 只处理 Databento 平台的产品
		if symbol.Platform != databento.DATABENTO {
			continue
		}

		wg.Add(1)
		go func(s structs.Tick) {
			defer wg.Done()

			log.Printf("📊 开始补齐产品 %s 的最近24小时数据", s.Symbol)

			// 调用 trading_getdata_alltick 的接口获取最近24小时数据
			err := replenishSymbolLast24Hours(param.Url, param.Token, s)
			if err != nil {
				errChan <- fmt.Errorf("补齐 %s 失败: %w", s.Symbol, err)
				return
			}

			log.Printf("✅ 产品 %s 的最近24小时数据补齐完成", s.Symbol)
		}(symbol)
	}

	// 等待所有协程完成
	wg.Wait()
	close(errChan)

	// 检查错误
	var errors []error
	for err := range errChan {
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		log.Printf("⚠️  补齐过程中出现 %d 个错误", len(errors))
		for _, err := range errors {
			log.Printf("  - %v", err)
		}
		return fmt.Errorf("补齐过程中出现错误")
	}

	log.Printf("✅ 所有产品的最近24小时数据补齐完成")
	return nil
}

// replenishSymbolLast24Hours 补齐单个产品的最近24小时数据
func replenishSymbolLast24Hours(uri, token string, symbol structs.Tick) error {
	// 调用 trading_getdata_alltick 的新接口获取最近24小时数据
	url := fmt.Sprintf("%s/databentoLast24Hours?token=%s&symbol=%s", uri, token, symbol.Symbol)

	resp, err := http.Get(url)
	if err != nil {
		return fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("API返回错误状态码: %d", resp.StatusCode)
	}

	// 解析响应
	var result struct {
		Code int                   `json:"code"`
		Msg  string                `json:"msg"`
		Data []pb.ReceiveDataKline `json:"data"`
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %w", err)
	}

	if err := json.Unmarshal(body, &result); err != nil {
		return fmt.Errorf("解析响应失败: %w", err)
	}

	if result.Code != http.StatusOK {
		return fmt.Errorf("API返回错误: %s", result.Msg)
	}

	data := result.Data

	if len(data) == 0 {
		log.Printf("⚠️  产品 %s 没有获取到任何数据", symbol.Symbol)
		return nil
	}

	// 1. 写入1分钟K线数据
	table1m := function.GetInfluxDBTable(symbol.Platform, symbol.Symbol, symbol.TimeZone, global.Resolutions.Minute1)
	var dataNew []structs.AllDataKline

	for _, v := range data {
		dataNew = append(dataNew, structs.AllDataKline{
			Platform: v.Platform,
			Symbol:   v.Symbol,
			Time:     v.Time,
			Open:     v.Open,
			Close:    v.Close,
			High:     v.High,
			Low:      v.Low,
			Volume:   v.Volume,
			Turnover: v.Turnover,
		})
	}

	// 批量写入1分钟数据
	err = influxdb.BatchWriteKlineData(table1m, dataNew, "replenishLast24Hours")
	if err != nil {
		return fmt.Errorf("写入1分钟数据失败: %w", err)
	}

	log.Printf("📊 产品 %s 写入了 %d 条1分钟K线数据", symbol.Symbol, len(dataNew))

	// 2. 基于1分钟数据生成其他周期的K线
	log.Printf("📊 开始基于1分钟数据生成产品 %s 的其他时间颗粒K线...", symbol.Symbol)

	// 定义需要生成的K线周期
	resolutions := []string{
		global.Resolutions.Minute5,
		global.Resolutions.Minute15,
		global.Resolutions.Minute30,
		global.Resolutions.Hour1,
		global.Resolutions.Hour4,
		global.Resolutions.Day,
		global.Resolutions.Week,
		global.Resolutions.Month,
	}

	// 为每个周期生成并写入K线数据
	for _, resolution := range resolutions {
		err := generateAndWriteKlineFromMinute1(symbol, dataNew, resolution)
		if err != nil {
			global.Lg.Error("生成K线失败",
				zap.String("symbol", symbol.Symbol),
				zap.String("resolution", resolution),
				zap.Error(err))
			// 继续处理其他周期
			continue
		}
	}

	log.Printf("✅ 产品 %s 的所有时间颗粒K线生成完成", symbol.Symbol)

	// 3. 更新最后补齐时间
	/*	currentTime := time.Now().Unix()
		if err := mysql.M.Model(&structs.Tick{}).Where("id=?", symbol.ID).Update("last_replenish_time", currentTime).Error; err != nil {
			global.Lg.Error("更新last_replenish_time失败", zap.String("symbol", symbol.Symbol), zap.Error(err))
		}*/

	return nil
}

// generateAndWriteKlineFromMinute1 基于1分钟数据生成指定周期的K线并写入
func generateAndWriteKlineFromMinute1(symbol structs.Tick, minute1Data []structs.AllDataKline, resolution string) error {
	if len(minute1Data) == 0 {
		return nil
	}

	// 获取目标表名
	table := function.GetInfluxDBTable(symbol.Platform, symbol.Symbol, symbol.TimeZone, resolution)

	// 根据不同的周期，计算聚合的K线数据
	var aggregatedData []structs.AllDataKline

	switch resolution {
	case global.Resolutions.Minute5:
		aggregatedData = aggregateKlineData(minute1Data, 5*60)
	case global.Resolutions.Minute15:
		aggregatedData = aggregateKlineData(minute1Data, 15*60)
	case global.Resolutions.Minute30:
		aggregatedData = aggregateKlineData(minute1Data, 30*60)
	case global.Resolutions.Hour1:
		aggregatedData = aggregateKlineData(minute1Data, 60*60)
	case global.Resolutions.Hour4:
		aggregatedData = aggregateKlineData(minute1Data, 4*60*60)
	case global.Resolutions.Day:
		aggregatedData = aggregateKlineData(minute1Data, 24*60*60)
	case global.Resolutions.Week:
		aggregatedData = aggregateKlineData(minute1Data, 7*24*60*60)
	case global.Resolutions.Month:
		// 月K线需要特殊处理，这里简化为30天
		aggregatedData = aggregateKlineData(minute1Data, 30*24*60*60)
	default:
		return fmt.Errorf("不支持的K线周期: %s", resolution)
	}

	if len(aggregatedData) == 0 {
		return nil
	}

	// 批量写入数据
	err := influxdb.BatchWriteKlineData(table, aggregatedData, "replenishLast24Hours")
	if err != nil {
		return fmt.Errorf("写入%s数据失败: %w", resolution, err)
	}

	log.Printf("📊 产品 %s 写入了 %d 条%s K线数据", symbol.Symbol, len(aggregatedData), resolution)
	return nil
}

// aggregateKlineData 将1分钟K线数据聚合成指定周期的K线数据
func aggregateKlineData(minute1Data []structs.AllDataKline, periodSeconds int64) []structs.AllDataKline {
	if len(minute1Data) == 0 {
		return nil
	}

	// 使用map来存储聚合的数据，key是对齐后的时间戳
	aggregatedMap := make(map[int64]*structs.AllDataKline)

	for _, kline := range minute1Data {
		// 将时间戳对齐到周期
		alignedTime := (kline.Time / periodSeconds) * periodSeconds

		if existingKline, exists := aggregatedMap[alignedTime]; exists {
			// 更新现有K线
			existingKline.High = max(existingKline.High, kline.High)
			existingKline.Low = min(existingKline.Low, kline.Low)
			existingKline.Close = kline.Close // 使用最新的收盘价
			existingKline.Volume += kline.Volume
			existingKline.Turnover += kline.Turnover
		} else {
			// 创建新的K线
			aggregatedMap[alignedTime] = &structs.AllDataKline{
				Platform: kline.Platform,
				Symbol:   kline.Symbol,
				Time:     alignedTime,
				Open:     kline.Open,
				High:     kline.High,
				Low:      kline.Low,
				Close:    kline.Close,
				Volume:   kline.Volume,
				Turnover: kline.Turnover,
			}
		}
	}

	// 将map转换为slice并按时间排序
	var result []structs.AllDataKline
	for _, kline := range aggregatedMap {
		result = append(result, *kline)
	}

	// 按时间排序
	for i := 0; i < len(result)-1; i++ {
		for j := i + 1; j < len(result); j++ {
			if result[i].Time > result[j].Time {
				result[i], result[j] = result[j], result[i]
			}
		}
	}

	return result
}

// min 返回两个float64中的较小值
func min(a, b float64) float64 {
	if a < b {
		return a
	}
	return b
}

// max 返回两个float64中的较大值
func max(a, b float64) float64 {
	if a > b {
		return a
	}
	return b
}

// ReplenishLast24HoursOnStartup 在程序启动时补齐最近24小时的数据
// 这个函数应该在 InitData 完成后调用
func ReplenishLast24HoursOnStartup(param pb.ReceiveClientInfo) {
	log.Printf("🚀 程序启动，开始补齐最近24小时的K线数据...")

	// 等待一段时间，确保系统初始化完成
	time.Sleep(10 * time.Second)

	// 执行补齐
	err := ReplenishLast24Hours(param)
	if err != nil {
		global.Lg.Error("补齐最近24小时数据失败", zap.Error(err))
		log.Printf("❌ 补齐最近24小时数据失败: %v", err)
	}
}
