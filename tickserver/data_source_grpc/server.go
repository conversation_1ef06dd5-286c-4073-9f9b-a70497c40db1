package externalgrpc

import (
	"context"
	"encoding/json"
	"fmt"
	"net"
	"trading_tick_server/lib/logger"

	"trading_tick_server/lib/global"
	ciclientgrpc "trading_tick_server/tickserver/ci_client_grpc"
	"trading_tick_server/tickserver/ci_client_grpc/internal_grpc_proto"
	pb "trading_tick_server/tickserver/data_source_grpc/data_source_proto"
	"trading_tick_server/tickserver/data_source_grpc/replenish"

	"go.uber.org/zap"
	"google.golang.org/grpc"
)

type server struct {
	pb.UnimplementedTickServiceServer
}

func (s *server) SayTick(ctx context.Context, req *pb.TickRequest) (*pb.EmptyResponse, error) {
	switch req.Type {
	case 1:
		fmt.Printf("SayTick 收到 data resrouce 连接请求: %s\n", req.Message)
		// 客户端连接
		var param pb.ReceiveClientInfo
		if err := json.Unmarshal([]byte(req.Message), &param); err != nil {
			global.Lg.Error("SayTick 解析客户端消息失败", zap.String("Message", req.Message), zap.String("component", "externalgrpc"), zap.Error(err))
		} else {
			global.ClientMap.Store(param.Platform, param)
			// 检测是否需要初始化数据 和传递需要取的数据过去
			go func() {
				defer func() {
					if r := recover(); r != nil {
						global.Lg.Error("InitData goroutine panic", zap.Any("error", r))
					}
				}()
				replenish.InitData(param)
			}()
		}
	case 2:
		// 上条k线 此接口弃用
		// var param []pb.ReceiveDataKline
		// if err := json.Unmarshal([]byte(req.Message), &param); err != nil {
		// 	global.Lg.Error(
		// 		"SayTick 解析客户端上条k线失败",
		// 		zap.String("Message", req.Message),
		// 		zap.String("component", "externalgrpc"),
		// 		zap.Error(err),
		// 	)
		// } else {
		// 	global.TickKlineCh <- &param
		// }
	case 3:
		// 盘口数据
		var param pb.ReceiveDataOrderBook
		if err := json.Unmarshal([]byte(req.Message), &param); err != nil {
			global.Lg.Error(
				"SayTick 解析客户端盘口数据失败",
				zap.String("Message", req.Message),
				zap.String("component", "externalgrpc"),
				zap.Error(err),
			)
		} else {
			//global.TickOBCh <- &param
			logger.DataFlow("SayTick 收到盘口数据", zap.String("Symbol", param.Symbol), zap.Any("Bids", param.Bids), zap.Any("Asks", param.Asks))
			select {
			case global.TickOBCh <- &param:
			default:
				//global.Lg.Warn("TickOBCh 满了，丢弃数据", zap.String("symbol", param.Symbol))
			}
		}
	case 4:
		// 成交报价数据
		var param pb.ReceiveDataTrade
		if err := json.Unmarshal([]byte(req.Message), &param); err != nil {
			global.Lg.Error(
				"SayTick 解析客户端成交报价数据失败",
				zap.String("Message", req.Message),
				zap.String("component", "externalgrpc"),
				zap.Error(err),
			)
		} else {
			//global.TickTradeCh <- &param
			/*if param.Symbol == "BTCUSDT" {
				fmt.Printf("1️⃣📥SayTick 收到 Data Source 数据 存入 Tick Trade Channel, Symbol : %v, Price : %v, Channel 缓存数量 : %v\n", param.Symbol, param.Price, len(global.TickTradeCh))
			}*/
			logger.DataFlow("SayTick 收到成交报价数据", zap.String("Symbol", param.Symbol), zap.Float64("Price", param.Price))
			select {
			case global.TickTradeCh <- &param:
			default:
				global.Lg.Warn("TickTradeCh 满了，丢弃数据", zap.String("symbol", param.Symbol))
			}
		}
	}

	// 实际的业务逻辑
	return &pb.EmptyResponse{}, nil
}

// 拦截器：日志记录 & 超时控制
func unaryInterceptor(
	ctx context.Context,
	req interface{},
	info *grpc.UnaryServerInfo,
	handler grpc.UnaryHandler,
) (interface{}, error) {
	// 记录日志
	// log.Printf("Handling request for %s", info.FullMethod)

	// 调用实际的处理器
	return handler(ctx, req)
}

func Run() {
	lis, err := net.Listen("tcp", ":"+global.Yaml.Externalgrpc.Port)
	if err != nil {
		panic(fmt.Errorf("unary RPC Failed to listen, error: %w", err))
	}

	// 配置拦截器
	opts := []grpc.ServerOption{
		grpc.UnaryInterceptor(unaryInterceptor),
	}

	// 创建gRPC服务器
	grpcServer := grpc.NewServer(opts...)

	// 注册外部数据源服务
	pb.RegisterTickServiceServer(grpcServer, &server{})

	// 注册双向流服务
	bidirectionalServer := ciclientgrpc.NewBidirectionalServer()
	internal_grpc_proto.RegisterInternalDataSourceServiceServer(grpcServer, bidirectionalServer)

	// 启动服务
	if err := grpcServer.Serve(lis); err != nil {
		panic(fmt.Errorf("unary RPC Failed to serve, error: %w", err))
	}
}
