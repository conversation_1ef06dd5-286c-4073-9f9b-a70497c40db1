// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.2
// source: data_source_grpc.proto

package data_source_grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	TickService_SayTick_FullMethodName = "/TickService.TickService/SayTick"
)

// TickServiceClient is the client API for TickService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 定义服务
type TickServiceClient interface {
	SayTick(ctx context.Context, in *TickRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
}

type tickServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTickServiceClient(cc grpc.ClientConnInterface) TickServiceClient {
	return &tickServiceClient{cc}
}

func (c *tickServiceClient) SayTick(ctx context.Context, in *TickRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, TickService_SayTick_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TickServiceServer is the server API for TickService service.
// All implementations must embed UnimplementedTickServiceServer
// for forward compatibility.
//
// 定义服务
type TickServiceServer interface {
	SayTick(context.Context, *TickRequest) (*EmptyResponse, error)
	mustEmbedUnimplementedTickServiceServer()
}

// UnimplementedTickServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTickServiceServer struct{}

func (UnimplementedTickServiceServer) SayTick(context.Context, *TickRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SayTick not implemented")
}
func (UnimplementedTickServiceServer) mustEmbedUnimplementedTickServiceServer() {}
func (UnimplementedTickServiceServer) testEmbeddedByValue()                     {}

// UnsafeTickServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TickServiceServer will
// result in compilation errors.
type UnsafeTickServiceServer interface {
	mustEmbedUnimplementedTickServiceServer()
}

func RegisterTickServiceServer(s grpc.ServiceRegistrar, srv TickServiceServer) {
	// If the following call pancis, it indicates UnimplementedTickServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&TickService_ServiceDesc, srv)
}

func _TickService_SayTick_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TickRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TickServiceServer).SayTick(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TickService_SayTick_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TickServiceServer).SayTick(ctx, req.(*TickRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TickService_ServiceDesc is the grpc.ServiceDesc for TickService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TickService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "TickService.TickService",
	HandlerType: (*TickServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SayTick",
			Handler:    _TickService_SayTick_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "data_source_grpc.proto",
}
