syntax = "proto3";

package TickService;

option go_package = "./;data_source_grpc"; 

// 定义服务
service TickService {
    rpc SayTick (TickRequest) returns (EmptyResponse);
}

/*
请求消息
类型 1 首次连接发送本身消息给服务器 2 实时K线 3 盘口数据
内容
*/
message TickRequest {
    int32 type = 1;
    string message = 2;
}

// 响应消息
message TickReply {
    string message = 1;
}

// 空的响应消息
message EmptyResponse {}

// protoc --go_out=. --go-grpc_out=. data_source_grpc.proto