// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: data_source_grpc.proto

package data_source_grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 请求消息
// 类型 1 首次连接发送本身消息给服务器 2 实时K线 3 盘口数据
// 内容
type TickRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          int32                  `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TickRequest) Reset() {
	*x = TickRequest{}
	mi := &file_data_source_grpc_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TickRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TickRequest) ProtoMessage() {}

func (x *TickRequest) ProtoReflect() protoreflect.Message {
	mi := &file_data_source_grpc_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TickRequest.ProtoReflect.Descriptor instead.
func (*TickRequest) Descriptor() ([]byte, []int) {
	return file_data_source_grpc_proto_rawDescGZIP(), []int{0}
}

func (x *TickRequest) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *TickRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 响应消息
type TickReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       string                 `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TickReply) Reset() {
	*x = TickReply{}
	mi := &file_data_source_grpc_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TickReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TickReply) ProtoMessage() {}

func (x *TickReply) ProtoReflect() protoreflect.Message {
	mi := &file_data_source_grpc_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TickReply.ProtoReflect.Descriptor instead.
func (*TickReply) Descriptor() ([]byte, []int) {
	return file_data_source_grpc_proto_rawDescGZIP(), []int{1}
}

func (x *TickReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 空的响应消息
type EmptyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmptyResponse) Reset() {
	*x = EmptyResponse{}
	mi := &file_data_source_grpc_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmptyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyResponse) ProtoMessage() {}

func (x *EmptyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_data_source_grpc_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyResponse.ProtoReflect.Descriptor instead.
func (*EmptyResponse) Descriptor() ([]byte, []int) {
	return file_data_source_grpc_proto_rawDescGZIP(), []int{2}
}

var File_data_source_grpc_proto protoreflect.FileDescriptor

var file_data_source_grpc_proto_rawDesc = []byte{
	0x0a, 0x16, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72,
	0x70, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x54, 0x69, 0x63, 0x6b, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x22, 0x3b, 0x0a, 0x0b, 0x54, 0x69, 0x63, 0x6b, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x22, 0x25, 0x0a, 0x09, 0x54, 0x69, 0x63, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x0f, 0x0a, 0x0d, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0x4e, 0x0a, 0x0b, 0x54, 0x69,
	0x63, 0x6b, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3f, 0x0a, 0x07, 0x53, 0x61, 0x79,
	0x54, 0x69, 0x63, 0x6b, 0x12, 0x18, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a,
	0x2e, 0x54, 0x69, 0x63, 0x6b, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x15, 0x5a, 0x13, 0x2e, 0x2f,
	0x3b, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x70,
	0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_data_source_grpc_proto_rawDescOnce sync.Once
	file_data_source_grpc_proto_rawDescData = file_data_source_grpc_proto_rawDesc
)

func file_data_source_grpc_proto_rawDescGZIP() []byte {
	file_data_source_grpc_proto_rawDescOnce.Do(func() {
		file_data_source_grpc_proto_rawDescData = protoimpl.X.CompressGZIP(file_data_source_grpc_proto_rawDescData)
	})
	return file_data_source_grpc_proto_rawDescData
}

var file_data_source_grpc_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_data_source_grpc_proto_goTypes = []any{
	(*TickRequest)(nil),   // 0: TickService.TickRequest
	(*TickReply)(nil),     // 1: TickService.TickReply
	(*EmptyResponse)(nil), // 2: TickService.EmptyResponse
}
var file_data_source_grpc_proto_depIdxs = []int32{
	0, // 0: TickService.TickService.SayTick:input_type -> TickService.TickRequest
	2, // 1: TickService.TickService.SayTick:output_type -> TickService.EmptyResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_data_source_grpc_proto_init() }
func file_data_source_grpc_proto_init() {
	if File_data_source_grpc_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_data_source_grpc_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_data_source_grpc_proto_goTypes,
		DependencyIndexes: file_data_source_grpc_proto_depIdxs,
		MessageInfos:      file_data_source_grpc_proto_msgTypes,
	}.Build()
	File_data_source_grpc_proto = out.File
	file_data_source_grpc_proto_rawDesc = nil
	file_data_source_grpc_proto_goTypes = nil
	file_data_source_grpc_proto_depIdxs = nil
}
