package data_source_grpc

/*
客户端信息
*/
type ReceiveClientInfo struct {
	Platform string `json:"platform"` // 平台
	Token    string `json:"token"`    // 客户端请求token
	Url      string `json:"url"`      // 客户端请求地址
}

/*
接收到的盘口数据
*/
type ReceiveDataOrderBook struct {
	Platform string          `json:"platform"`  // 平台
	Symbol   string          `json:"symbol"`    // 标签
	Seq      string          `json:"seq"`       // 交易序列号
	TickTime int64           `json:"tick_time"` // 时间戳 接收时使用毫秒单位
	Bids     []OrderBookData `json:"bids"`      // 买入价格
	Asks     []OrderBookData `json:"asks"`      // 卖出价格
}

/*
只传输第一位价格的结构
*/
type OrderBookData struct {
	Price  float64 `json:"price"`
	Volume float64 `json:"volume"`
}

/*
接收到的成交数据
*/
type ReceiveDataTrade struct {
	Platform string  `json:"platform"` // 平台
	Symbol   string  `json:"symbol"`   // 标签
	Time     int64   `json:"time"`     // 时间戳 接收时使用毫秒单位
	Price    float64 `json:"price"`
	Volume   float64 `json:"volume"`
	Turnover float64 `json:"turnover"`
}

/*
接收到的k线数据
*/
type ReceiveDataKline struct {
	Platform    string  `json:"platform"`    // 平台
	Symbol      string  `json:"symbol"`      // 标签
	Resolutions string  `json:"resolutions"` // 时间分辨率
	Time        int64   `json:"time"`        // 时间戳 接收时使用秒单位
	Open        float64 `json:"open"`        // 开盘价
	Close       float64 `json:"close"`       // 收盘价
	High        float64 `json:"high"`        // 最高价
	Low         float64 `json:"low"`         // 最低价
	Volume      float64 `json:"volume"`      // 成交量
	Turnover    float64 `json:"turnover"`    // 成交额
}

/*
历史k线返回结果解析结构体
*/
type RetDataKline struct {
	Code int                `json:"code"`
	Msg  string             `json:"msg"`
	Data []ReceiveDataKline `json:"data"`
}

/*
只传输第一位价格的结构
*/
type OrderBookPrice struct {
	Symbol string          `json:"symbol"`
	Time   int64           `json:"time"`
	Bids   []OrderBookData `json:"bids"` // 买入价格
	Asks   []OrderBookData `json:"asks"` // 卖出价格
}

/*
只传输第一位价格的安全map结构
*/
type OrderBookOnePriceMap struct {
	Time       int64   `json:"time"`
	BidsPrice1 float64 `json:"bids"`
	AsksPrice1 float64 `json:"asks"`
}

/*
接收到的k线数据
*/
type NotifyClientData struct {
	Symbol   string `json:"symbol"`   // 标签
	Type     string `json:"type"`     // 类型
	Platform string `json:"platform"` // 平台,目前可选 : alltick, htx
}
