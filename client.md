
历史k线 /api/kline_history [GET]
参数
// 用户ID
UserID uint `form:"user_id,omitempty"`
// token
Token string `form:"token,omitempty"`
// 截止时间
KlineTimestampEnd int64 `form:"kline_timestamp_end,omitempty"`
// 产品标识 Symbol
Code string `form:"code,omitempty"`
// 数量
Count int `form:"count,omitempty"`
// 时间分辨率
Resolution string `form:"resolution,omitempty"`

批量获取历史k线 /api/kline_history [GET]
参数
// 用户ID
UserID uint `form:"user_id,omitempty"`
// token
Token string `form:"token,omitempty"`
// 截止时间
KlineTimestampEnd int64 `form:"kline_timestamp_end,omitempty"`
// 产品标识 Symbol
Codes string `form:"codes,omitempty"`
// 数量
Count int `form:"count,omitempty"`
// 时间分辨率
Resolution string `form:"resolution,omitempty"`

k线插针列表 /api/kline_shadow [GET]
参数
// 用户ID
UserID uint `form:"user_id,omitempty"`
// token
Token string `form:"token,omitempty"`
// 页码 可空 默认为1
Page int `form:"page,omitempty"`
// 每页显示数量 可空 默认值由项目启动参数决定
PageSize int `form:"page_size,omitempty"`

添加k线插针 /api/kline_shadow [POST]
参数
// 用户ID
UserID uint `json:"user_id" binding:"required"`
// token
Token string `json:"token" binding:"required"`
// 名称
Symbol string `json:"symbol" binding:"required"`
// 类型 intuitive fluctuation
Type string `json:"type" binding:"required"`
// 插针开始时间
StartTime int64 `json:"start_time" binding:"required"`
// 爬坡时间(秒)
RiseTime int `json:"rise_time" binding:"required"`
// 峰值时间(秒)
PeakTime int `json:"peak_time" binding:"required"`
// 回落时间(秒)
FallTime int `json:"fall_time" binding:"required"`
// 插针幅度 正数为上插 负数为下插 0为非法
SpikeFactor string `json:"spike_factor" binding:"required"`
// 实时k线的close值 intuitive类型下才需要传递
Candlestick string `json:"candlestick,omitempty"`
// 买跌浮动值 intuitive类型下才需要传递
AsksPriceMin string `json:"asks_price_min,omitempty"`
// 买跌浮动值 intuitive类型下才需要传递
AsksPriceMax string `json:"asks_price_max,omitempty"`
// 爬坡期的命中最小值 范围0.01-1之间 对应百分比1%到100%
RiseHitMin string `json:"rise_hit_min,omitempty"`
// 爬坡期的命中最大值 范围0.01-1之间 对应百分比1%到100%
RiseHitMax string `json:"rise_hit_max,omitempty"`
// 峰值期的命中最小值 范围0.01-1之间 对应百分比1%到100%
PeakHitMin string `json:"peak_hit_min,omitempty"`
// 峰值期的命中最大值 范围0.01-1之间 对应百分比1%到100%
PeakHitMax string `json:"peak_hit_max,omitempty"`
// 回落期的命中最小值 范围0.01-1之间 对应百分比1%到100%
FallHitMin string `json:"fall_hit_min,omitempty"`
// 回落期的命中最大值 范围0.01-1之间 对应百分比1%到100%
FallHitMax string `json:"fall_hit_max,omitempty"`
// 爬坡期的命中后取值范围最小值 最小0.01 无上限
RiseValueMin string `json:"rise_value_min,omitempty"`
// 爬坡期的命中后取值范围最大值 最小0.01 无上限
RiseValueMax string `json:"rise_value_max,omitempty"`
// 峰值期的命中后取值范围最小值 最小0.01 无上限
PeakValueMin string `json:"peak_value_min,omitempty"`
// 峰值期的命中后取值范围最大值 最小0.01 无上限
PeakValueMax string `json:"peak_value_max,omitempty"`
// 回落期的命中后取值范围最小值 最小0.01 无上限
FallValueMin string `json:"fall_value_min,omitempty"`
// 回落期的命中后取值范围最大值 最小0.01 无上限
FallValueMax string `json:"fall_value_max,omitempty"`
// 上影线最大值
UpperShadowMax string `json:"upper_shadow_max,omitempty"`
// 下影线最大值
LowerShadowMax string `json:"lower_shadow_max,omitempty"`

删除k线插针 /api/kline_shadow [DELETE]
参数
// 用户ID
UserID uint `json:"user_id" binding:"required"`
// token
Token string `json:"token" binding:"required"`
// ID
ID uint `json:"id" binding:"required,min=1"`
// 是否同时删除插针的k线数据
DelData uint8 `json:"del_data,omitempty"`

删除k线插针清空 /api/kline_shadow/fix [DELETE]
参数
// 用户ID
UserID uint `json:"user_id" binding:"required"`
// token
Token string `json:"token" binding:"required"`
// 要修复的产品 多个用,分开
Symbols string `json:"symbols" binding:"required"`

请求grpc连接 /api/connection_delivery [GET]
参数
// 用户ID
UserID uint `form:"user_id,omitempty"`
// token
Token string `form:"token,omitempty"`
// 你的服务器地址
YourSever string `form:"your_sever,omitempty"`
// 你的验证token
YourToken string `form:"your_token,omitempty"`
会返回连接成功或失败的提示
