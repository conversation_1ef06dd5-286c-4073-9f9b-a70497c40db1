# 第一阶段：构建 Go 项目
FROM golang:1.23-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装必要的依赖，包括 swag 和 gin-swagger
#RUN go install github.com/swaggo/swag/cmd/swag@latest

# 将项目的 go.mod 和 go.sum 文件复制到容器中
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 将整个项目复制到容器中
COPY . .

# 生成 Swagger 文档
#RUN swag init

# 构建 Go 应用（编译为二进制文件）
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o main .

# 第二阶段：构建运行环境的轻量级镜像
FROM alpine:latest

# 设置工作目录
WORKDIR /app

# 拷贝第一阶段构建的二进制文件
COPY --from=builder /app/main .

# 拷贝 Swag 生成的文档（如果你的 docs 文件夹是生成 Swagger 文档的路径）
#COPY --from=builder /app/docs ./docs

# 暴露服务运行的端口（如果你的应用监听 8080 端口）
EXPOSE 8080

# 运行应用
CMD ["./main"]
