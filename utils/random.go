package utils

import (
	"github.com/shopspring/decimal"
	"math/rand"
	"time"
)

// RandomDecimalInRange 返回[min, max]范围内一个随机的decimal值，保留最高小数位精度
func RandomDecimalInRange(min, max decimal.Decimal) decimal.Decimal {
	if min.<PERSON><PERSON>han(max) {
		min, max = max, min
	}

	// 获取最高小数位数精度
	scale := max.Exponent() * -1
	if s := min.Exponent() * -1; s > scale {
		scale = s
	}

	multiplier := decimal.NewFromInt(1).Shift(int32(scale))
	minInt := min.Mul(multiplier).IntPart()
	maxInt := max.Mul(multiplier).IntPart()

	if minInt == maxInt {
		return min.Round(int32(scale))
	}

	// 使用推荐方式创建随机源
	rng := rand.New(rand.NewSource(time.Now().UnixNano()))
	randInt := rng.Int63n(maxInt-minInt+1) + minInt

	return decimal.NewFromInt(randInt).Div(multiplier).Round(int32(scale))
}
