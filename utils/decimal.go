package utils

import (
	"math"
	"strconv"
	"strings"
)

/*
DecimalPlaces 返回一个浮点数的小数位数
*/
func DecimalPlaces(num float64) int {
	// 用 strconv 转换成字符串
	s := strconv.FormatFloat(num, 'f', -1, 64)

	// 看有没有小数点
	if !strings.Contains(s, ".") {
		return 0
	}

	// 截取小数点后的部分
	parts := strings.Split(s, ".")
	decimalPart := parts[1]

	// 返回小数位数
	return len(decimalPart)
}

/*
TruncateDecimal 截断浮点数到指定的小数位数
*/
func TruncateDecimal(num float64, decimalPlaces int) float64 {
	// 计算缩放倍数
	pow := math.Pow(10, float64(decimalPlaces))

	// 先放大，取整，再缩小
	truncated := math.Trunc(num*pow) / pow

	return truncated
}
