package utils

import (
	"fmt"
	"time"
)

func SplitByWeeks(startUnix, endUnix int64) []WeekPeriod {
	if startUnix >= endUnix {
		return nil
	}

	var periods []WeekPeriod

	current := time.Unix(startUnix, 0).UTC()
	endTime := time.Unix(endUnix, 0).UTC()

	for current.Before(endTime) {
		// 计算一周后的时间
		weekLater := current.AddDate(0, 0, 7)

		// 确定实际的结束时间
		actualEnd := weekLater
		if weekLater.After(endTime) {
			actualEnd = endTime
		}

		periods = append(periods, WeekPeriod{
			Start: current.Unix(),
			End:   actualEnd.Unix(),
		})

		current = weekLater
	}

	return periods
}

// WeekPeriod 表示一周的时间段
type WeekPeriod struct {
	Start int64 `json:"start"` // 周开始时间戳（秒）
	End   int64 `json:"end"`   // 周结束时间戳（秒）
}

// 格式化显示函数
func (wp WeekPeriod) String() string {
	start := time.Unix(wp.Start, 0).UTC()
	end := time.Unix(wp.End, 0).UTC()
	duration := end.Sub(start)

	return fmt.Sprintf("开始: %s, 结束: %s, 时长: %.1f天",
		start.Format("2006-01-02 15:04:05"),
		end.Format("2006-01-02 15:04:05"),
		duration.Hours()/24)
}

// 辅助函数：打印周期数组
func PrintWeekPeriods(periods []WeekPeriod, title string) {
	fmt.Printf("\n=== %s ===\n", title)
	for i, period := range periods {
		fmt.Printf("第%d周: %s\n", i+1, period.String())
	}
	fmt.Printf("总共 %d 个周期\n", len(periods))
}
