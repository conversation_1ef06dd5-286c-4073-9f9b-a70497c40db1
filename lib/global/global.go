package global

import (
	"go.uber.org/zap"
	"sync"
	"trading_tick_server/lib/structs"
	pb "trading_tick_server/tickserver/data_source_grpc/data_source_proto"
)

var Path string
var Yaml structs.YamlConf

// 接收到数据源的盘口数据
var TickOBCh = make(chan *pb.ReceiveDataOrderBook, 30)

// 接收到数据源的实时k线数据 实时只有1分钟k线
var TickKlineCh = make(chan *[]pb.ReceiveDataKline, 30)

// 接收到数据源的成交报价数据
var TickTradeCh = make(chan *pb.ReceiveDataTrade, 30)
var PathUploads string
var CmdIDArr = []int{22001, 22003}
var Lg *zap.Logger
var Salt = "somesaltvalue"

// 给k线查询一个默认值
var KlineSelectLimit = 200

// 客户端列表
var ClientMap sync.Map
var SystemCommonAuth = []string{
	"logout:get", "reset_password:post", "build_google_totp:get", "build_google_totp:post",
	"verify_google_totp:post",
}

var SysMenuTypes = map[string]string{
	"menu":   "菜单",
	"action": "操作",
}

// 定义一个结构体类型
type ResolutionsStruct struct {
	Minute1  string
	Minute5  string
	Minute15 string
	Minute30 string
	Hour1    string
	Hour4    string
	Day      string
	Week     string
	Month    string
}

// 通过不可导出的变量`status`提供常量值
var Resolutions = ResolutionsStruct{
	Minute1:  "1m",
	Minute5:  "5m",
	Minute15: "15m",
	Minute30: "30m",
	Hour1:    "1h",
	Hour4:    "4h",
	Day:      "D",
	Week:     "W",
	Month:    "M",
}

var ArrResolutions = []string{"1m", "5m", "15m", "30m", "1h", "4h", "D", "W", "M"}
