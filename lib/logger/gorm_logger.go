package logger

import (
	"context"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"time"
	"trading_tick_server/lib/global"

	"go.uber.org/zap"
	"gorm.io/gorm/logger"
)

type GormLogger struct {
	ZapLogger                 *zap.Logger
	SlowThreshold             time.Duration
	LogLevel                  logger.LogLevel
	IgnoreRecordNotFoundError bool
}

func NewGormLogger(logLevel logger.LogLevel) *GormLogger {
	return &GormLogger{
		ZapLogger:                 global.Lg.Named("gorm").WithOptions(zap.AddCallerSkip(4)),
		SlowThreshold:             time.Millisecond * 500, // SQL 慢查询阈值 500ms
		LogLevel:                  logLevel,
		IgnoreRecordNotFoundError: true,
	}
}

func (l *GormLogger) LogMode(level logger.LogLevel) logger.Interface {
	l.LogLevel = level
	return l
}

func (l *GormLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= logger.Info {
		l.ZapLogger.Info(fmt.Sprintf(msg, data...), extractTraceFields(ctx)...)
	}
}

func (l *GormLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= logger.Warn {
		l.ZapLogger.Warn(fmt.Sprintf(msg, data...), extractTraceFields(ctx)...)
	}
}

func (l *GormLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= logger.Error {
		l.ZapLogger.Error(fmt.Sprintf(msg, data...), extractTraceFields(ctx)...)
	}
}

func (l *GormLogger) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	elapsed := time.Since(begin)
	sql, rows := fc()

	fields := []zap.Field{
		zap.String("sql", sql),
		zap.Int64("rows", rows),
		zap.Duration("elapsed", elapsed),
	}
	fields = append(fields, extractTraceFields(ctx)...)

	switch {
	case err != nil:
		if l.IgnoreRecordNotFoundError && errors.Is(err, gorm.ErrRecordNotFound) {
			break
		}
		if l.LogLevel >= logger.Error {
			l.ZapLogger.Error("SQL Error", append(fields, zap.Error(err))...)
		}
	case elapsed > l.SlowThreshold:
		if l.LogLevel >= logger.Warn {
			l.ZapLogger.Warn("Slow Query", fields...)
		}
	default:
		if l.LogLevel >= logger.Info {
			l.ZapLogger.Info("SQL Query", fields...)
		}
	}
}
