package logger

import (
	"archive/zip"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	rotatelogs "github.com/lestrrat-go/file-rotatelogs"
	"go.uber.org/zap"
)

// RotateConfig 日志轮转配置
type RotateConfig struct {
	LogDir       string        // 日志目录
	Filename     string        // 文件名
	MaxAge       time.Duration // 文件最大保存时间
	RotationTime time.Duration // 日志切割时间间隔
	MaxBackups   int           // 最大备份数量（可选）
	Compress     bool          // 是否压缩
	LinkName     bool          // 是否创建软链接（Windows下无效）
}

// NewRotateLogs 创建日志轮转器（兼容旧版本）
func NewRotateLogs(logDir, filename string, maxAge, rotationTime time.Duration) (*rotatelogs.RotateLogs, error) {
	config := &RotateConfig{
		LogDir:       logDir,
		Filename:     filename,
		MaxAge:       maxAge,
		RotationTime: rotationTime,
		Compress:     true,
		LinkName:     true,
	}
	return NewRotateLogsWithConfig(config)
}

// NewRotateLogsWithConfig 使用配置创建日志轮转器
func NewRotateLogsWithConfig(config *RotateConfig) (*rotatelogs.RotateLogs, error) {
	// 确保日志目录存在
	if err := ensureLogDir(config.LogDir); err != nil {
		return nil, fmt.Errorf("创建日志目录失败: %w", err)
	}

	logPath := filepath.Join(config.LogDir, config.Filename)

	// 构建轮转文件名模式
	ext := filepath.Ext(config.Filename)
	nameWithoutExt := strings.TrimSuffix(config.Filename, ext)
	rotatePattern := filepath.Join(config.LogDir, fmt.Sprintf("%s.%%Y%%m%%d%s", nameWithoutExt, ext))

	var options []rotatelogs.Option

	// 设置软链接（仅非 Windows 系统）
	if config.LinkName && runtime.GOOS != "windows" {
		options = append(options, rotatelogs.WithLinkName(logPath))
	}

	// 设置最大保存时间
	if config.MaxAge > 0 {
		options = append(options, rotatelogs.WithMaxAge(config.MaxAge))
	}

	// 设置轮转时间间隔
	if config.RotationTime > 0 {
		options = append(options, rotatelogs.WithRotationTime(config.RotationTime))
	}

	// 设置最大备份数量
	if config.MaxBackups > 0 {
		options = append(options, rotatelogs.WithRotationCount(uint(config.MaxBackups)))
	}

	// 设置压缩处理器
	if config.Compress {
		options = append(options, rotatelogs.WithHandler(rotatelogs.HandlerFunc(createCompressHandler())))
	}

	return rotatelogs.New(rotatePattern, options...)
}

// ensureLogDir 确保日志目录存在
func ensureLogDir(logDir string) error {
	if _, err := os.Stat(logDir); os.IsNotExist(err) {
		if err := os.MkdirAll(logDir, 0755); err != nil {
			return fmt.Errorf("创建日志目录失败 %s: %w", logDir, err)
		}
	}
	return nil
}

// createCompressHandler 创建压缩处理器
func createCompressHandler() func(rotatelogs.Event) {
	return func(e rotatelogs.Event) {
		switch event := e.(type) {
		case *rotatelogs.FileRotatedEvent:
			go compressLogFile(event.PreviousFile())
		}
	}
}

// compressLogFile 压缩日志文件
func compressLogFile(srcFile string) {
	if srcFile == "" {
		return
	}

	// 检查源文件是否存在
	if _, err := os.Stat(srcFile); os.IsNotExist(err) {
		return
	}

	zipFile := srcFile + ".zip"

	// 执行压缩操作
	if err := performCompression(srcFile, zipFile); err != nil {
		logCompressError("压缩过程失败", srcFile, err)
		// 清理可能创建的不完整zip文件
		os.Remove(zipFile)
		return
	}

	// 删除原始文件
	if err := os.Remove(srcFile); err != nil {
		logCompressError("删除原始日志文件失败", srcFile, err)
	} else {
		logCompressSuccess(srcFile, zipFile)
	}
}

// performCompression 执行实际的压缩操作
func performCompression(srcFile, zipFile string) error {
	// 创建 zip 文件
	zipf, err := os.Create(zipFile)
	if err != nil {
		return fmt.Errorf("创建zip文件失败: %w", err)
	}
	defer func() {
		if closeErr := zipf.Close(); closeErr != nil &&
			!strings.Contains(closeErr.Error(), "file already closed") {
			logCompressError("关闭zip文件失败", zipFile, closeErr)
		}
	}()

	// 创建 zip writer
	zipWriter := zip.NewWriter(zipf)
	defer func() {
		if closeErr := zipWriter.Close(); closeErr != nil &&
			!strings.Contains(closeErr.Error(), "file already closed") &&
			closeErr.Error() != "zip: writer closed twice" {
			logCompressError("关闭zip writer失败", zipFile, closeErr)
		}
	}()

	// 添加文件到 zip
	writer, err := zipWriter.Create(filepath.Base(srcFile))
	if err != nil {
		return fmt.Errorf("添加文件到zip失败: %w", err)
	}

	// 打开源文件
	srcf, err := os.Open(srcFile)
	if err != nil {
		return fmt.Errorf("打开源日志文件失败: %w", err)
	}
	defer func() {
		if closeErr := srcf.Close(); closeErr != nil {
			logCompressError("关闭源文件失败", srcFile, closeErr)
		}
	}()

	// 复制文件内容
	if _, err := io.Copy(writer, srcf); err != nil {
		return fmt.Errorf("写入zip失败: %w", err)
	}

	return nil
}

// logCompressError 记录压缩错误
func logCompressError(msg, file string, err error) {
	if MainLogger != nil {
		MainLogger.Error("日志压缩错误",
			zap.String("message", msg),
			zap.String("file", file),
			zap.Error(err),
			zap.String("component", "log_rotator"))
	} else {
		// 如果主日志器不可用，输出到标准错误
		fmt.Fprintf(os.Stderr, "[LOG_ROTATOR_ERROR] %s: %s, error: %v\n", msg, file, err)
	}
}

// logCompressSuccess 记录压缩成功
func logCompressSuccess(srcFile, zipFile string) {
	if MainLogger != nil {
		MainLogger.Info("日志文件压缩完成",
			zap.String("source", srcFile),
			zap.String("compressed", zipFile),
			zap.String("component", "log_rotator"))
	}
}

// CleanupOldLogs 清理旧日志文件
func CleanupOldLogs(logDir string, maxAge time.Duration) error {
	cutoff := time.Now().Add(-maxAge)

	return filepath.Walk(logDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 跳过目录和当前日志文件
		if info.IsDir() || isCurrentLogFile(path) {
			return nil
		}

		// 检查文件是否过期
		if info.ModTime().Before(cutoff) {
			if err := os.Remove(path); err != nil {
				logCompressError("删除过期日志文件失败", path, err)
			} else if MainLogger != nil {
				MainLogger.Info("删除过期日志文件",
					zap.String("file", path),
					zap.Time("mod_time", info.ModTime()),
					zap.String("component", "log_cleanup"))
			}
		}

		return nil
	})
}

// isCurrentLogFile 检查是否是当前正在使用的日志文件
func isCurrentLogFile(path string) bool {
	// 检查是否是软链接文件或当天的日志文件
	filename := filepath.Base(path)

	// 如果文件名不包含日期模式，可能是当前文件
	if !strings.Contains(filename, time.Now().Format("20060102")) {
		return true
	}

	// 检查是否是今天的日志文件
	today := time.Now().Format("20060102")
	return strings.Contains(filename, today)
}

// GetLogFileSize 获取日志文件大小
func GetLogFileSize(logPath string) (int64, error) {
	info, err := os.Stat(logPath)
	if err != nil {
		return 0, err
	}
	return info.Size(), nil
}

// GetLogFileInfo 获取日志文件信息
type LogFileInfo struct {
	Path     string    `json:"path"`
	Size     int64     `json:"size"`
	ModTime  time.Time `json:"mod_time"`
	IsActive bool      `json:"is_active"`
}

// ListLogFiles 列出日志目录中的所有日志文件
func ListLogFiles(logDir string) ([]LogFileInfo, error) {
	var files []LogFileInfo

	err := filepath.Walk(logDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.IsDir() {
			return nil
		}

		// 只处理日志文件
		if isLogFile(path) {
			files = append(files, LogFileInfo{
				Path:     path,
				Size:     info.Size(),
				ModTime:  info.ModTime(),
				IsActive: isCurrentLogFile(path),
			})
		}

		return nil
	})

	return files, err
}

// isLogFile 检查是否是日志文件
func isLogFile(path string) bool {
	ext := strings.ToLower(filepath.Ext(path))
	return ext == ".log" || ext == ".zip"
}

// RotateLogNow 立即轮转指定的日志文件
func RotateLogNow(rotator *rotatelogs.RotateLogs) error {
	// 这个功能需要 rotatelogs 库支持，目前的版本可能不支持手动轮转
	// 可以通过写入一个特殊标记来触发轮转
	return fmt.Errorf("手动轮转功能暂不支持")
}

// LogRotatorStats 日志轮转统计信息
type LogRotatorStats struct {
	TotalFiles      int       `json:"total_files"`
	CompressedFiles int       `json:"compressed_files"`
	TotalSize       int64     `json:"total_size"`
	OldestFile      time.Time `json:"oldest_file"`
	NewestFile      time.Time `json:"newest_file"`
}

// GetRotatorStats 获取日志轮转统计信息
func GetRotatorStats(logDir string) (*LogRotatorStats, error) {
	files, err := ListLogFiles(logDir)
	if err != nil {
		return nil, err
	}

	stats := &LogRotatorStats{}

	for _, file := range files {
		stats.TotalFiles++
		stats.TotalSize += file.Size

		if strings.HasSuffix(file.Path, ".zip") {
			stats.CompressedFiles++
		}

		if stats.OldestFile.IsZero() || file.ModTime.Before(stats.OldestFile) {
			stats.OldestFile = file.ModTime
		}

		if stats.NewestFile.IsZero() || file.ModTime.After(stats.NewestFile) {
			stats.NewestFile = file.ModTime
		}
	}

	return stats, nil
}
