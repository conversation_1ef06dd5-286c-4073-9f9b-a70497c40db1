package logger

import (
	"context"
	"fmt"
	"os"
	"time"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/structs"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// 全局日志实例
var (
	MainLogger     *zap.Logger // 主日志
	InfoLogger     *zap.Logger // 信息日志
	DataFlowLogger *zap.Logger // 数据流日志（高频数据）
	ErrorLogger    *zap.Logger // 错误日志
	atomicLevel    zap.AtomicLevel
)

// InitNew 新版本初始化方法（使用 structs.Logging 配置）
func InitNew(cfg *structs.Logging) error {
	// 初始化原子级别
	atomicLevel = zap.NewAtomicLevel()
	atomicLevel.SetLevel(parseLevel(cfg.LogLevel))

	// 确保日志目录存在
	if err := ensureLogDir(cfg.LogDir); err != nil {
		return fmt.Errorf("创建日志目录失败: %w", err)
	}

	// 获取编码器
	encoder := getEncoder(cfg.IsDev)

	// 创建日志写入器
	if cfg.MaxAge == 0 {
		cfg.MaxAge = 7 * 24 * time.Hour
	}
	if cfg.RotationTime == 0 {
		cfg.RotationTime = 24 * time.Hour
	}
	mainWriter, err := NewRotateLogs(cfg.LogDir, "info.log", cfg.MaxAge, cfg.RotationTime)
	if err != nil {
		return fmt.Errorf("创建main日志轮转器失败: %w", err)
	}

	infoWriter, err := NewRotateLogs(cfg.LogDir, "view.log", cfg.MaxAge, cfg.RotationTime)
	if err != nil {
		return fmt.Errorf("创建info日志轮转器失败: %w", err)
	}

	errorWriter, err := NewRotateLogs(cfg.LogDir, "error.log", cfg.MaxAge, cfg.RotationTime)
	if err != nil {
		return fmt.Errorf("创建error日志轮转器失败: %w", err)
	}

	dataflowWriter, err := NewRotateLogs(cfg.LogDir, "dataflow.log", 24*time.Hour, cfg.RotationTime)
	if err != nil {
		return fmt.Errorf("创建dataflow日志轮转器失败: %w", err)
	}

	// 创建核心
	mainCore := zapcore.NewCore(encoder, zapcore.AddSync(mainWriter), atomicLevel)
	infoCore := zapcore.NewCore(encoder, zapcore.AddSync(infoWriter), atomicLevel)
	errorCore := zapcore.NewCore(encoder, zapcore.AddSync(errorWriter), zapcore.ErrorLevel)
	dataflowCore := zapcore.NewCore(encoder, zapcore.AddSync(dataflowWriter), zapcore.DebugLevel)

	// 开发环境添加控制台输出
	if cfg.IsDev {
		consoleEncoder := zapcore.NewConsoleEncoder(zap.NewDevelopmentEncoderConfig())
		consoleCore := zapcore.NewCore(consoleEncoder, zapcore.Lock(os.Stdout), atomicLevel)
		mainCore = zapcore.NewTee(mainCore, consoleCore)
	}

	// 创建主日志器（包含 main + info + error）
	allCore := zapcore.NewTee(mainCore, errorCore)
	MainLogger = zap.New(allCore, zap.AddCaller(), zap.AddCallerSkip(1))

	InfoLogger = zap.New(infoCore, zap.AddCaller())

	// 创建专用日志器
	DataFlowLogger = zap.New(dataflowCore, zap.AddCaller())

	ErrorLogger = zap.New(errorCore, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))

	// 设置全局日志器
	zap.ReplaceGlobals(MainLogger)
	if global.Lg == nil {
		global.Lg = MainLogger
	}

	// 记录初始化成功
	MainLogger.Info("日志系统初始化完成",
		zap.String("level", cfg.LogLevel),
		zap.String("log_dir", cfg.LogDir),
		zap.Bool("dev_mode", cfg.IsDev))
	return nil
}

// getEncoder 获取编码器
func getEncoder(isDev bool) zapcore.Encoder {
	encoderCfg := zap.NewProductionEncoderConfig()
	encoderCfg.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderCfg.TimeKey = "time"
	encoderCfg.EncodeCaller = zapcore.ShortCallerEncoder
	encoderCfg.EncodeLevel = zapcore.LowercaseLevelEncoder
	if isDev {
		return zapcore.NewConsoleEncoder(encoderCfg)
	}
	return zapcore.NewJSONEncoder(encoderCfg)
}

// parseLevel 解析日志级别
func parseLevel(level string) zapcore.Level {
	switch level {
	case "debug":
		return zapcore.DebugLevel
	case "info":
		return zapcore.InfoLevel
	case "warn":
		return zapcore.WarnLevel
	case "error":
		return zapcore.ErrorLevel
	default:
		return zapcore.InfoLevel
	}
}

// ========== 基础日志方法 ==========

// Info 记录信息日志
func Info(msg string, fields ...zap.Field) {
	if MainLogger != nil {
		MainLogger.Info(msg, fields...)
	}
}

// Warn 记录警告日志
func Warn(msg string, fields ...zap.Field) {
	if MainLogger != nil {
		MainLogger.Warn(msg, fields...)
	}
}

// Error 记录错误日志
func Error(msg string, fields ...zap.Field) {
	if MainLogger != nil {
		MainLogger.Error(msg, fields...)
	}
	if ErrorLogger != nil {
		ErrorLogger.Error(msg, fields...)
	}
}

// Debug 记录调试日志
func Debug(msg string, fields ...zap.Field) {
	if MainLogger != nil {
		MainLogger.Debug(msg, fields...)
	}
}

// Fatal 记录致命错误日志
func Fatal(msg string, fields ...zap.Field) {
	if MainLogger != nil {
		MainLogger.Fatal(msg, fields...)
	}
	if ErrorLogger != nil {
		ErrorLogger.Fatal(msg, fields...)
	}
}

// ========== 带 Context 的日志方法 ==========

// InfoCtx 记录带上下文的信息日志
func InfoCtx(ctx context.Context, msg string, fields ...zap.Field) {
	logger := getLoggerWithTrace(ctx)
	logger.Info(msg, fields...)
}

// WarnCtx 记录带上下文的警告日志
func WarnCtx(ctx context.Context, msg string, fields ...zap.Field) {
	logger := getLoggerWithTrace(ctx)
	logger.Warn(msg, fields...)
}

// ErrorCtx 记录带上下文的错误日志
func ErrorCtx(ctx context.Context, msg string, fields ...zap.Field) {
	logger := getLoggerWithTrace(ctx)
	logger.Error(msg, fields...)

	// 同时写入专用错误日志（带上下文）
	if ErrorLogger != nil {
		errorLogger := ErrorLogger
		if traceID := ExtractTraceID(ctx); traceID != "" {
			errorLogger = errorLogger.With(zap.String(TraceIDKey, traceID))
		}
		errorLogger.Error(msg, fields...)
	}
}

// DebugCtx 记录带上下文的调试日志
func DebugCtx(ctx context.Context, msg string, fields ...zap.Field) {
	logger := getLoggerWithTrace(ctx)
	logger.Debug(msg, fields...)
}

// ========== 专用日志方法 ==========

// View 只记录到信息日志文件
func View(msg string, fields ...zap.Field) {
	if InfoLogger != nil {
		InfoLogger.Info(msg, fields...)
	}
}

// ViewCtx 只记录到信息日志文件（带上下文）
func ViewCtx(ctx context.Context, msg string, fields ...zap.Field) {
	if InfoLogger != nil {
		logger := InfoLogger
		if traceID := ExtractTraceID(ctx); traceID != "" {
			logger = logger.With(zap.String(TraceIDKey, traceID))
		}
		logger.Info(msg, fields...)
	}
}

// DataFlow 记录数据流日志（高频数据）
func DataFlow(msg string, fields ...zap.Field) {
	if DataFlowLogger != nil && shouldLogDataFlow() {
		DataFlowLogger.Debug(msg, fields...)
	}
}

// DataFlowCtx 记录带上下文的数据流日志
func DataFlowCtx(ctx context.Context, msg string, fields ...zap.Field) {
	if DataFlowLogger != nil && shouldLogDataFlow() {
		logger := DataFlowLogger
		if traceID := ExtractTraceID(ctx); traceID != "" {
			logger = logger.With(zap.String(TraceIDKey, traceID))
		}
		logger.Debug(msg, fields...)
	}
}

// ========== 工具方法 ==========

// getLoggerWithTrace 获取带 trace 信息的 logger
func getLoggerWithTrace(ctx context.Context) *zap.Logger {
	if MainLogger == nil {
		return zap.NewNop()
	}

	traceID := ExtractTraceID(ctx)

	var fields []zap.Field
	if traceID != "" {
		fields = append(fields, zap.String(TraceIDKey, traceID))
	}

	if len(fields) > 0 {
		return MainLogger.With(fields...)
	}
	return MainLogger
}

// shouldLogDataFlow 检查是否应该记录数据流日志
func shouldLogDataFlow() bool {
	return global.Yaml.Logging.DataFlow
}
