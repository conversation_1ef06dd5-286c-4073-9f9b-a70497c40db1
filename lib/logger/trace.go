package logger

import (
	"context"
	"github.com/google/uuid"
	"go.uber.org/zap"
)

// context key 类型，避免冲突
type ctxKeyTraceID struct{}

// 日志字段 key
const TraceIDKey = "traceId"

// GenerateTraceID 生成一个新的 traceId
func GenerateTraceID() string {
	return uuid.New().String()
}

// InjectTraceID 注入 traceId 到 context
func InjectTraceID(ctx context.Context, traceId string) context.Context {
	return context.WithValue(ctx, ctxKeyTraceID{}, traceId)
}

// ExtractTraceID 从 context 提取 traceId
func ExtractTraceID(ctx context.Context) string {
	if v, ok := ctx.Value(ctxKeyTraceID{}).(string); ok {
		return v
	}
	return ""
}

func extractTraceFields(ctx context.Context) []zap.Field {
	//traceID, ok := ctx.Value(TraceIDKey).(string)
	if traceID, ok := ctx.Value(ctxKeyTraceID{}).(string); ok {
		return []zap.Field{zap.String(TraceIDKey, traceID)}
	}
	return nil
}
