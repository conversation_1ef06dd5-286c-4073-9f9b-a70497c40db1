package decouplingfunction

import (
	"fmt"
	"strconv"
	"strings"
	"time"
	"trading_tick_server/lib/global"
)

// 取全部客户的插针redis键名
func GetAllRedisTable(userID, platform, symbol, timeZone string) []string {
	s := []string{
		GetRedisTable(userID, platform, symbol, timeZone, global.Resolutions.Minute1),
		GetRedisTable(userID, platform, symbol, timeZone, global.Resolutions.Minute5),
		GetRedisTable(userID, platform, symbol, timeZone, global.Resolutions.Minute15),
		GetRedisTable(userID, platform, symbol, timeZone, global.Resolutions.Minute30),
		GetRedisTable(userID, platform, symbol, timeZone, global.Resolutions.Hour1),
		GetRedisTable(userID, platform, symbol, timeZone, global.Resolutions.Hour4),
		GetRedisTable(userID, platform, symbol, timeZone, global.Resolutions.Day),
		GetRedisTable(userID, platform, symbol, timeZone, global.Resolutions.Week),
		GetRedisTable(userID, platform, symbol, timeZone, global.Resolutions.Month),
	}
	return s
}

// 全部时间颗粒
func GetAllResolutions() []string {
	return []string{
		global.Resolutions.Minute1,
		global.Resolutions.Minute5,
		global.Resolutions.Minute15,
		global.Resolutions.Minute30,
		global.Resolutions.Hour1,
		global.Resolutions.Hour4,
		global.Resolutions.Day,
		global.Resolutions.Week,
		global.Resolutions.Month,
	}
}

// 取客户的插针redis键名
func GetRedisTable(userID, platform, symbol, timeZone, resolutions string) string {
	if resolutions == global.Resolutions.Minute1 || resolutions == global.Resolutions.Minute5 || resolutions == global.Resolutions.Minute15 {
		return "kshadow_" + userID + "_" + conversionSymbol(platform) + "_" + conversionSymbol(symbol) + "_" + conversionSymbol(resolutions)
	}
	return "kshadow_" + userID + "_" + conversionSymbol(platform) + "_" + conversionSymbol(symbol) + "_" + conversionSymbol(timeZone) + "_" + conversionSymbol(resolutions)
}

// 统一处理产品名的格式转换
func conversionSymbol(s string) string {
	s = strings.Replace(s, "+", "-", -1)
	s = strings.Replace(s, ":", "-", -1)
	s = strings.Replace(s, ".", "_", -1)
	return strings.ToLower(s)
}

/*
取当前时间的k线起始时间戳
*/
func CurrentKlineTimestamp(timestamp int64, timezone string, interval string) int64 {
	t := time.Unix(timestamp, 0)
	if timezone == "" {
		timezone = "UTC"
	}
	offsetSeconds, err := parseTimezoneOffset(timezone)
	if err != nil {
		return 0
	}
	// 创建对应的时区对象
	loc := time.FixedZone(timezone, offsetSeconds)

	// 将 t 转为指定时区
	t2 := t.In(loc)

	switch interval {
	case global.Resolutions.Minute1:
		return t2.Truncate(1 * time.Minute).Unix()
	case global.Resolutions.Minute5:
		return t2.Truncate(5 * time.Minute).Unix()
	case global.Resolutions.Minute15:
		return t2.Truncate(15 * time.Minute).Unix()
	case global.Resolutions.Minute30:
		return t2.Truncate(30 * time.Minute).Unix()
	case global.Resolutions.Hour1:
		return t2.Truncate(time.Hour).Unix()
	case global.Resolutions.Hour4:
		return t2.Truncate(4 * time.Hour).Unix()
	case global.Resolutions.Day:
		return t2.Truncate(24 * time.Hour).Unix()
	case global.Resolutions.Week:
		weekday := int(t2.Weekday())
		if weekday == 0 {
			weekday = 7
		}
		truncatedWeek := t2.Truncate(24*time.Hour).AddDate(0, 0, -(weekday - 1))
		return truncatedWeek.Unix()
	case global.Resolutions.Month:
		return time.Date(t2.Year(), t2.Month(), 1, 0, 0, 0, 0, loc).Unix()
	default:
		return 0
	}
}

// 解析时区偏移量
func parseTimezoneOffset(timezone string) (int, error) {
	if strings.HasPrefix(timezone, "UTC") {
		timezone = timezone[3:]
	} else {
		return 0, fmt.Errorf("时区格式不正确，应以 'UTC' 开头")
	}
	// 如果去除 "UTC" 后为空字符串，表示 UTC+0:00
	if timezone == "" {
		return 0, nil // 返回 0 表示没有偏移
	}

	// 处理 "+" 或 "-" 符号
	sign := 1
	if strings.HasPrefix(timezone, "+") {
		timezone = timezone[1:]
	} else if strings.HasPrefix(timezone, "-") {
		sign = -1
		timezone = timezone[1:]
	}

	// 将小时和分钟分开（如果有分钟）
	parts := strings.Split(timezone, ":")
	hours, err := strconv.Atoi(parts[0])
	if err != nil {
		return 0, fmt.Errorf("无法解析时区中的小时部分")
	}

	minutes := 0
	if len(parts) > 1 {
		minutes, err = strconv.Atoi(parts[1])
		if err != nil {
			return 0, fmt.Errorf("无法解析时区中的分钟部分")
		}
	}

	// 计算总的偏移秒数
	offsetSeconds := sign * ((hours * 3600) + (minutes * 60))

	return offsetSeconds, nil
}
