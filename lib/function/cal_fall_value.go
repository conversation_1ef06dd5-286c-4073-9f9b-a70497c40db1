package function

import (
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"math/rand"
	"trading_tick_server/lib/logger"
)

// 我现在有三个时间值, 开始时间,结束时间,和当前之间, 当前时间一定是在开始时间和结束时间之间的
// 有两个值, 开始值和结束值
// 现在我的需求是,计算出当前时间在开始时间和结束时间之间的经过的比例 比如开始时间是1,结束时间是10,当前时间是4,那么比例就是0.4
// 然后根据这个百分比,计算出当前时间对应的值,这个值是开始值和结束值之间的一个值
// 请帮我写出go 语言代码

// 计算回落期追大盘真实数据的插针值
// 参数: 回落期结束时间,当前大盘真实值,之前一次插针值,
func CalFallValue(fallStartTime, fallEndTime, currentTime int64, realCloseValue decimal.Decimal, peakValue decimal.Decimal) (close float64, exact bool) {

	fallEndTime = fallEndTime * 1000
	fallStartTime = fallStartTime * 1000
	// 计算时间区间的持续时间
	totalDuration := fallEndTime - fallStartTime   // time.Duration
	currentDuration := currentTime - fallStartTime // time.Duration

	// 转换为浮点数进行比例计算
	ratio := float64(currentDuration) / float64(totalDuration)

	logger.DataFlow("回落期插针值计算",
		zap.Int64("回落开始时间", fallStartTime),
		zap.Int64("回落结束时间", fallEndTime),
		zap.Int64("当前时间", currentTime),
		zap.Int64("总持续时间", totalDuration),
		zap.Int64("已过时间", currentDuration),
		zap.Float64("时间比例", ratio),
		zap.String("峰值价格", peakValue.String()),
		zap.String("真实收盘价", realCloseValue.String()))

	// 根据比例进行插值 (大盘真实值 - 峰值)
	currentValue := peakValue.Add(decimal.NewFromFloat(ratio).Mul(realCloseValue.Sub(peakValue))) // 这里为什么要减一个 peakValue

	logger.DataFlow("回落期插针值计算结果",
		zap.String("价格差值", realCloseValue.Sub(peakValue).String()),
		zap.String("比例乘以差值", decimal.NewFromFloat(ratio).Mul(realCloseValue.Sub(peakValue)).String()),
		zap.String("计算结果", currentValue.String()))

	return currentValue.Float64()
}

// 两数间取随机
func ShouldHitValueDecimal(probMin, probMax decimal.Decimal) decimal.Decimal {
	if probMin.GreaterThan(probMax) {
		probMin, probMax = probMax, probMin
	}
	rf := decimal.NewFromFloat(rand.Float64())
	p := probMin.Add(rf.Mul(probMax.Sub(probMin)))
	return p
}
