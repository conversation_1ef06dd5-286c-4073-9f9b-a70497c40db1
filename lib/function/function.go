package function

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"math/big"
	"math/rand"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"
	"trading_tick_server/lib/cacheimport"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/logger"
	"trading_tick_server/lib/mysql"
	"trading_tick_server/lib/structs"

	"go.uber.org/zap"

	"github.com/shopspring/decimal"
	"github.com/sknun/cf/cast"
	"golang.org/x/crypto/argon2"
	"gopkg.in/yaml.v3"
	"gorm.io/gorm"
)

/*
初始化路径
*/
func InitPath() {
	global.Path, _ = os.Getwd()
}

// 加载配置
func InitYaml() {
	// 读取 YAML 文件
	data, err := os.ReadFile(global.Path + "/storage/conf/conf.yaml")
	if err != nil {
		panic(fmt.Errorf("reading yaml config file failed: %w", err))
	}

	// 将 YAML 数据解析到结构体中
	err = yaml.Unmarshal(data, &global.Yaml)
	if err != nil {
		panic(fmt.Errorf("yaml.Unmarshal failed: %w", err))
	}
}

// 生成MD5值
func MD5(text string) string {
	// 创建MD5哈希
	hash := md5.New()
	// 将字符串转换为字节并写入哈希
	hash.Write([]byte(text))
	// 计算哈希值
	md5Hash := hash.Sum(nil)
	// 将哈希值转换为十六进制字符串
	return hex.EncodeToString(md5Hash)
}

// 检查字符串是否存在
func StrContains(s string, sub []string) bool {
	for _, v := range sub {
		if !strings.Contains(s, v) {
			return false
		}
	}
	return true
}

// json请求
func HttpCurlJson(uri string, jsonByte []byte) ([]byte, error) {
	client := &http.Client{}
	req, err := http.NewRequest("POST", uri, bytes.NewBuffer(jsonByte))

	if err != nil {
		return []byte(""), err
	}
	req.Header.Set("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		return []byte(""), err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return []byte(""), err
	}
	return body, nil
}

// get请求
func HttpCurlGet(uri string) ([]byte, error) {
	client := &http.Client{}
	req, err := http.NewRequest("GET", uri, nil)
	if err != nil {
		return []byte(""), err
	}
	resp, err := client.Do(req)
	if err != nil {
		return []byte(""), err
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return []byte(""), err
	}
	return body, nil
}

// 判断ID是否在切片中
func CmdIDNotIN(cmdid int) bool {
	for _, v := range global.CmdIDArr {
		if cmdid == v {
			return false
		}
	}
	return true
}

// 判断一个时间戳是否为整分
func IsExactMinute(timestamp int64) bool {
	t := time.Unix(timestamp, 0) // 将时间戳转换为 time.Time
	return t.Second() == 0       // 判断秒数是否为 0
}

// 秒转纳秒
func SecondsToNanoseconds(seconds int64) int64 {
	return seconds * 1_000_000_000
}

// 翻转切片
func ReverseSlice(slice []structs.KlineDataRet) {
	for i, j := 0, len(slice)-1; i < j; i, j = i+1, j-1 {
		slice[i], slice[j] = slice[j], slice[i]
	}
}

func DecimalSubAbs(a, b float64) float64 {
	dA := decimal.NewFromFloat(a)
	dB := decimal.NewFromFloat(b)
	diff := dA.Sub(dB).Abs()
	val, _ := diff.Float64()
	return val
}

/*
给一个时间戳 返回这个时间戳在哪个k线范围内
*/
func GetKLineStartTimes(minuteTimestamp int64, timezone string) (map[string]int64, error) {
	// 将时间戳转换为 time.Time 对象（UTC 时间）
	t := time.Unix(minuteTimestamp, 0)

	// 解析时区字符串，例如 "UTC+2" 或 "UTC-3"
	if timezone == "" {
		timezone = "UTC"
	}
	offsetSeconds, err := parseTimezoneOffset(timezone)
	if err != nil {
		return nil, err
	}
	// 创建对应的时区对象
	loc := time.FixedZone(timezone, offsetSeconds)
	// 转换为指定时区的时
	t = t.In(loc)

	wd := int(t.Weekday())
	if wd == 0 {
		wd = 7
	}
	weekStart := time.Date(t.Year(), t.Month(), t.Day()-(wd-1), 0, 0, 0, 0, loc).Unix()

	// 计算各周期起始时间
	return map[string]int64{
		global.Resolutions.Minute5:  t.Truncate(5 * time.Minute).Unix(),
		global.Resolutions.Minute15: t.Truncate(15 * time.Minute).Unix(),
		global.Resolutions.Minute30: t.Truncate(30 * time.Minute).Unix(),
		global.Resolutions.Hour1:    t.Truncate(1 * time.Hour).Unix(),
		global.Resolutions.Hour4:    time.Date(t.Year(), t.Month(), t.Day(), t.Hour()/4*4, 0, 0, 0, loc).Unix(),
		global.Resolutions.Day:      time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, loc).Unix(),
		global.Resolutions.Week:     weekStart,
		global.Resolutions.Month:    time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, loc).Unix(),
	}, nil
}

/*
取当前时间的k线起始时间戳
*/
func CurrentKlineTimestamp(timestamp int64, timezone string, interval string) int64 {
	// 将时间戳转换为 time.Time 对象（UTC 时间）
	t := time.Unix(timestamp, 0)

	// 如果 timezone 为空，默认是 "UTC"
	if timezone == "" {
		timezone = "UTC"
	}
	offsetSeconds, err := parseTimezoneOffset(timezone)
	if err != nil {
		return 0
	}
	// 创建对应的时区对象
	loc := time.FixedZone(timezone, offsetSeconds)

	// 将 t 转为指定时区
	t2 := t.In(loc)

	switch interval {
	case global.Resolutions.Minute1:
		return t2.Truncate(1 * time.Minute).Unix()
	case global.Resolutions.Minute5:
		return t2.Truncate(5 * time.Minute).Unix()
	case global.Resolutions.Minute15:
		return t2.Truncate(15 * time.Minute).Unix()
	case global.Resolutions.Minute30:
		return t2.Truncate(30 * time.Minute).Unix()
	case global.Resolutions.Hour1:
		return t2.Truncate(time.Hour).Unix()
	case global.Resolutions.Hour4:
		return t2.Truncate(4 * time.Hour).Unix()
	case global.Resolutions.Day:
		return t2.Truncate(24 * time.Hour).Unix()
	case global.Resolutions.Week:
		weekday := int(t2.Weekday())
		if weekday == 0 {
			weekday = 7
		}
		truncatedWeek := t2.Truncate(24*time.Hour).AddDate(0, 0, -(weekday - 1))
		return truncatedWeek.Unix()
	case global.Resolutions.Month:
		return time.Date(t2.Year(), t2.Month(), 1, 0, 0, 0, 0, loc).Unix()
	default:
		return 0
	}
}

/*
根据给出的时间戳和时间颗粒 时区 来取上一个k线的时间
*/
func TruncateTIme(timestamp int64, timezone string, interval string) int64 {
	// 将时间戳转换为 time.Time 对象（UTC 时间）
	t := time.Unix(timestamp, 0)

	// 解析时区字符串，例如 "UTC+2" 或 "UTC-3"
	if timezone == "" {
		timezone = "UTC"
	}
	offsetSeconds, err := parseTimezoneOffset(timezone)
	if err != nil {
		return 0
	}
	// 创建对应的时区对象
	loc := time.FixedZone(timezone, offsetSeconds)
	// 转换为指定时区的时
	t2 := t.In(loc)

	// 判断指定的周期条件
	switch interval {
	case global.Resolutions.Minute1:
		return t2.Truncate(1 * time.Minute).Add(-1 * time.Minute).Unix()
	case global.Resolutions.Minute5:
		return t2.Truncate(5 * time.Minute).Add(-5 * time.Minute).Unix()
	case global.Resolutions.Minute15:
		return t2.Truncate(15 * time.Minute).Add(-15 * time.Minute).Unix()
	case global.Resolutions.Minute30:
		previousTime := t2.Truncate(30 * time.Minute).Add(-30 * time.Minute)
		return previousTime.Unix()
	case global.Resolutions.Hour1:
		previousTime := t2.Truncate(time.Hour).Add(-time.Hour)
		return previousTime.Unix()
	case global.Resolutions.Hour4:
		previousTime := t2.Truncate(4 * time.Hour).Add(-4 * time.Hour)
		return previousTime.Unix()
	case global.Resolutions.Day:
		previousTime := t2.Truncate(24 * time.Hour).Add(-24 * time.Hour)
		return previousTime.Unix()
	case global.Resolutions.Week:
		weekday := int(t2.Weekday())
		if weekday == 0 {
			weekday = 7
		}
		thisWeekMonday := t2.Truncate(24*time.Hour).AddDate(0, 0, -(weekday - 1))
		lastWeekMonday := thisWeekMonday.AddDate(0, 0, -7)
		return lastWeekMonday.Unix()
	case global.Resolutions.Month:
		truncatedMonth := time.Date(t2.Year(), t2.Month(), 1, 0, 0, 0, 0, loc).AddDate(0, -1, 0)
		return truncatedMonth.Unix()
	default:
		return 0
	}
}

// 计算下一根 K 线的起始时间戳
func NextKlineTimestamp(currentStart int64, timezone string, interval string) int64 {
	if timezone == "" {
		timezone = "UTC"
	}
	offsetSeconds, err := parseTimezoneOffset(timezone)
	if err != nil {
		return 0
	}
	loc := time.FixedZone(timezone, offsetSeconds)

	currentTime := time.Unix(currentStart, 0).In(loc)

	switch interval {
	case global.Resolutions.Minute1:
		return currentTime.Add(1 * time.Minute).Unix()
	case global.Resolutions.Minute5:
		return currentTime.Add(5 * time.Minute).Unix()
	case global.Resolutions.Minute15:
		return currentTime.Add(15 * time.Minute).Unix()
	case global.Resolutions.Minute30:
		return currentTime.Add(30 * time.Minute).Unix()
	case global.Resolutions.Hour1:
		return currentTime.Add(1 * time.Hour).Unix()
	case global.Resolutions.Hour4:
		return currentTime.Add(4 * time.Hour).Unix()
	case global.Resolutions.Day:
		return currentTime.Add(24 * time.Hour).Unix()
	case global.Resolutions.Week:
		return currentTime.AddDate(0, 0, 7).Unix()
	case global.Resolutions.Month:
		nextMonth := currentTime.AddDate(0, 1, 0)
		return time.Date(nextMonth.Year(), nextMonth.Month(), 1, 0, 0, 0, 0, loc).Unix()
	default:
		return 0
	}
}

// Argon2 加密密码
func HashPasswordArgon2(password string) string {
	// 生成密码哈希
	hash := argon2.IDKey([]byte(password), []byte(global.Salt), 1, 64*1024, 4, 32)
	return hex.EncodeToString(hash)
}

// Argon2 加密密码
func HashUserPasswordArgon2(password string, id uint) string {
	// 生成密码哈希
	hash := argon2.IDKey([]byte(password), []byte(global.Salt+cast.ToString(id)), 1, 64*1024, 4, 32)
	return hex.EncodeToString(hash)
}

// 根据用户ID返回权限列表的接口
func GetSysUserPermissions(id uint) (string, error) {
	fmt.Println("id", id)
	var user structs.SysUser

	// 获取用户及其角色
	if err := mysql.M.Preload("SysUserRole.SysRolePermission", func(db *gorm.DB) *gorm.DB {
		return db.Order("sort_order asc") // 按 sort_order 升序排序
	}).First(&user, id).Error; err != nil {
		return "", err
	}

	// 收集用户所有的权限
	permissionMap := make(map[uint]structs.SysPermission)
	for _, role := range user.SysUserRole {
		for _, permission := range role.SysRolePermission {
			permissionMap[permission.ID] = permission
		}
	}

	// 转换 map 到 slice
	var permissions []structs.SysPermission
	for _, permission := range permissionMap {
		permissions = append(permissions, permission)
	}

	// 构建权限树
	tree := buildPermissionTree(permissions, 0)
	b, err := json.Marshal(&tree)
	if err != nil {
		return "", err
	}
	return string(b), nil
}

// 根据权限列表构建权限树
func buildPermissionTree(permissions []structs.SysPermission, parentID uint) []structs.SysPermissionTree {
	var tree []structs.SysPermissionTree

	for _, permission := range permissions {
		if permission.ParentID == parentID {
			// 构建子权限
			children := buildPermissionTree(permissions, permission.ID)

			// 构建当前权限节点
			node := structs.SysPermissionTree{
				ID:       permission.ID,
				Name:     permission.Name,
				Key:      permission.Resource,
				Type:     permission.Type,
				Children: children,
			}
			tree = append(tree, node)
		}
	}
	return tree
}

// 检查允许的文件扩展名
func IsAllowedExtension(ext string) bool {
	// 允许的扩展名
	allowedExtensions := []string{".jpg", ".jpeg", ".png", ".pdf"}

	for _, allowed := range allowedExtensions {
		if ext == allowed {
			return true
		}
	}

	return false
}

// 用时间生成目录
func GenerateDirForData() string {
	return time.Now().Format("20060102")
}

// 给时间戳 返回整分钟时间
func ZeroSecondOfTimestamp(ts int64) int64 {
	return (ts / 60) * 60
}

// 取influxdb的表名
func GetInfluxDBTable(platform, symbol, timeZone, resolutions string) string {
	if resolutions == global.Resolutions.Minute1 || resolutions == global.Resolutions.Minute5 || resolutions == global.Resolutions.Minute15 {
		return "kline_" + platform + "_" + conversionSymbol(symbol) + "_" + resolutions
	}
	return "kline_" + platform + "_" + conversionSymbol(symbol) + "_" + conversionSymbol(timeZone) + "_" + resolutions
}

// 取备份mysql的表名
func GetBackupMysqlTable(platform, symbol string) string {
	return "backup_kline_" + platform + "_" + conversionSymbol(symbol)
}

// 取平台+产品的组合名称
func GetPlatformSymbol(platform, symbol string) string {
	return platform + "_" + conversionSymbol(symbol)
}

// 取平台+产品的组合名称
func GetPlatformSymbolUserID(platform, symbol, userID string, t int64) string {
	if userID == "" || userID == "0" {
		return platform + "_" + conversionSymbol(symbol) + "_" + cast.ToString(t)
	}
	return platform + "_" + conversionSymbol(symbol) + "_" + userID + "_" + cast.ToString(t)
}

// 取平台+产品+时间颗粒的组合名称
func GetPlatformSymbolResolutions(platform, symbol, resolutions string) string {
	return platform + "_" + conversionSymbol(symbol) + "_" + conversionSymbol(resolutions)
}

// 解析产品数量统计的平台和产品名
func AnalyzePlatformSymbol(s string) (platform, symbol string, userID uint) {
	parts := strings.Split(s, "_")
	if len(parts) == 2 {
		platform = parts[0]
		symbol = parts[1]
		userID = 0
		return
	}
	if len(parts) == 3 {
		platform = parts[0]
		symbol = parts[1]
		userID = cast.ToUint(parts[2])
		return
	}
	platform = s
	return
}

// 取全部客户的插针redis键名
func GetAllRedisTable(userID, platform, symbol, timeZone string) []string {
	s := []string{
		GetRedisTable(userID, platform, symbol, timeZone, global.Resolutions.Minute1),
		GetRedisTable(userID, platform, symbol, timeZone, global.Resolutions.Minute5),
		GetRedisTable(userID, platform, symbol, timeZone, global.Resolutions.Minute15),
		GetRedisTable(userID, platform, symbol, timeZone, global.Resolutions.Minute30),
		GetRedisTable(userID, platform, symbol, timeZone, global.Resolutions.Hour1),
		GetRedisTable(userID, platform, symbol, timeZone, global.Resolutions.Hour4),
		GetRedisTable(userID, platform, symbol, timeZone, global.Resolutions.Day),
		GetRedisTable(userID, platform, symbol, timeZone, global.Resolutions.Week),
		GetRedisTable(userID, platform, symbol, timeZone, global.Resolutions.Month),
	}
	return s
}

// 取客户的插针redis键名
func GetRedisTable(userID, platform, symbol, timeZone, resolutions string) string {
	if resolutions == global.Resolutions.Minute1 || resolutions == global.Resolutions.Minute5 || resolutions == global.Resolutions.Minute15 {
		return "kshadow_" + userID + "_" + conversionSymbol(platform) + "_" + conversionSymbol(symbol) + "_" + conversionSymbol(resolutions)
	}
	return "kshadow_" + userID + "_" + conversionSymbol(platform) + "_" + conversionSymbol(symbol) + "_" + conversionSymbol(timeZone) + "_" + conversionSymbol(resolutions)
}

// 取influxdb的表名
func GetInfluxDBShadowTable(userID, platform, symbol, timeZone, resolutions string) string {
	if resolutions == global.Resolutions.Minute1 || resolutions == global.Resolutions.Minute5 || resolutions == global.Resolutions.Minute15 {
		return "kshadow_" + userID + "_" + conversionSymbol(platform) + "_" + conversionSymbol(symbol) + "_" + resolutions
	}
	return "kshadow_" + userID + "_" + conversionSymbol(platform) + "_" + conversionSymbol(symbol) + "_" + conversionSymbol(timeZone) + "_" + resolutions
}

// 客户插针待执行redis操作
func KlineShadowOperate(param structs.UserShadow) error {
	return cacheimport.UserKlineShadowSet(param)
}

// 删除客户插针待执行redis操作
func KlineShadowOperateDel(param structs.UserShadow) {
	cacheimport.UserKlineShadowDelStruct(param)
}

// 统一处理产品名的格式转换
func conversionSymbol(s string) string {
	s = strings.Replace(s, "+", "-", -1)
	s = strings.Replace(s, ":", "-", -1)
	s = strings.Replace(s, ".", "_", -1)
	return strings.ToLower(s)
}

// BoolToUint8 converts a boolean value to uint8 (true -> 1, false -> 0).
func BoolToUint8(b bool) uint8 {
	if b {
		return 1
	}
	return 0
}

// Uint8ToBool converts a uint8 value to boolean (0 -> false, any other value -> true).
func Uint8ToBool(u uint8) bool {
	return u != 0
}

/*
取用户产品
kline 获取k线 orderbook5 盘口五条 orderbook1 盘口1条
*/
func GetUserSymbol(s []structs.UserSymbol, t string) map[string]int32 {
	m := map[string]int32{}
	for _, v := range s {
		if v.Type == t {
			m[GetPlatformSymbol(v.Platform, v.Symbol)] = cast.ToInt32(v.DecimalPlaces)
		}
	}
	return m
}

// 取产品需要生成的时区
func GetTickTimeZone(s string) string {
	if s == "" {
		return "UTC"
	}
	return s
}

/*
判断是否需要生成特定周期的K线
如果需要的话 返回上一个k线的时间戳
*/
func ShouldGenerateKLine(timestamp int64, timezone string, interval string) int64 {
	// 将时间戳转为 time.Time (UTC)
	t := time.Unix(timestamp, 0)

	if timezone == "" {
		timezone = "UTC"
	}
	offsetSeconds, err := parseTimezoneOffset(timezone)
	if err != nil {
		return 0
	}
	// 创建指定时区
	loc := time.FixedZone(timezone, offsetSeconds)

	// 转为指定时区
	tLoc := t.In(loc)

	// 统一用 tLoc 的时分秒等
	tSecond := tLoc.Second()
	tMinute := tLoc.Minute()
	tHour := tLoc.Hour()
	tDay := tLoc.Day()
	tWeekday := tLoc.Weekday()

	switch interval {

	case global.Resolutions.Minute5:
		if tMinute%5 == 0 && tSecond == 0 {
			return timestamp - 300
		}
	case global.Resolutions.Minute15:
		if tMinute%15 == 0 && tSecond == 0 {
			return timestamp - 900
		}
	case global.Resolutions.Minute30:
		if tMinute%30 == 0 && tSecond == 0 {
			return timestamp - 1800
		}
	case global.Resolutions.Hour1:
		if tMinute == 0 && tSecond == 0 {
			return timestamp - 3600
		}
	case global.Resolutions.Hour4:
		if tHour%4 == 0 && tMinute == 0 && tSecond == 0 {
			return timestamp - 14400
		}
	case global.Resolutions.Day:
		if tHour == 0 && tMinute == 0 && tSecond == 0 {
			return timestamp - 86400
		}
	case global.Resolutions.Week:
		if tWeekday == time.Monday && tHour == 0 && tMinute == 0 && tSecond == 0 {
			return timestamp - 604800
		}
	case global.Resolutions.Month:
		// 判断是否是本月 1 号 00:00:00
		if tDay == 1 && tHour == 0 && tMinute == 0 && tSecond == 0 {
			// 获取上个月的开始时间
			prev := tLoc.AddDate(0, -1, -(tDay - 1))
			prevStart := time.Date(prev.Year(), prev.Month(), 1, 0, 0, 0, 0, loc)
			return prevStart.Unix()
		}
	}
	return 0
}

// 判断是否是下个月的开始时间，并返回上个月的开始时间戳
func GetPreviousMonthStartIfNextMonth(timestamp int64, timezone string) (int64, bool, error) {
	// 将时间戳转换为 time.Time 对象（UTC 时间）
	t := time.Unix(timestamp, 0)

	// 解析时区字符串，例如 "UTC+2" 或 "UTC-3"
	offsetSeconds, err := parseTimezoneOffset(timezone)
	if err != nil {
		return 0, false, err
	}

	// 创建对应的时区对象
	loc := time.FixedZone(timezone, offsetSeconds)

	// 转换为指定时区的时间
	t = t.In(loc)

	// 检查是否是下个月的开始时间（1号 00:00:00）
	if t.Day() == 1 && t.Hour() == 0 && t.Minute() == 0 && t.Second() == 0 {
		// 获取上个月的开始时间
		previousMonth := t.AddDate(0, -1, -(t.Day() - 1))
		previousMonthStart := time.Date(previousMonth.Year(), previousMonth.Month(), 1, 0, 0, 0, 0, loc)
		return previousMonthStart.Unix(), true, nil
	}

	return 0, false, nil
}

// 解析时区偏移量
func parseTimezoneOffset(timezone string) (int, error) {
	// 去除前面的 "UTC"
	if strings.HasPrefix(timezone, "UTC") {
		timezone = timezone[3:]
	} else {
		return 0, fmt.Errorf("时区格式不正确，应以 'UTC' 开头")
	}

	// 如果去除 "UTC" 后为空字符串，表示 UTC+0:00
	if timezone == "" {
		return 0, nil // 返回 0 表示没有偏移
	}

	// 处理 "+" 或 "-" 符号
	sign := 1
	if strings.HasPrefix(timezone, "+") {
		timezone = timezone[1:]
	} else if strings.HasPrefix(timezone, "-") {
		sign = -1
		timezone = timezone[1:]
	}

	// 将小时和分钟分开（如果有分钟）
	parts := strings.Split(timezone, ":")
	hours, err := strconv.Atoi(parts[0])
	if err != nil {
		return 0, fmt.Errorf("无法解析时区中的小时部分")
	}

	minutes := 0
	if len(parts) > 1 {
		minutes, err = strconv.Atoi(parts[1])
		if err != nil {
			return 0, fmt.Errorf("无法解析时区中的分钟部分")
		}
	}

	// 计算总的偏移秒数
	offsetSeconds := sign * ((hours * 3600) + (minutes * 60))

	return offsetSeconds, nil
}

// 判断是否有差异
func KlineDifferenceValue(old, new structs.KlineDataRet) bool {
	return old.Open == new.Open && old.Close == new.Close && old.High == new.High && old.Low == new.Low
}

/*
判断时间分辨率是否合法
*/
func ResolutionIsLegal(s string) bool {
	switch s {
	case global.Resolutions.Minute1:
		return true
	case global.Resolutions.Minute5:
		return true
	case global.Resolutions.Minute15:
		return true
	case global.Resolutions.Minute30:
		return true
	case global.Resolutions.Hour1:
		return true
	case global.Resolutions.Hour4:
		return true
	case global.Resolutions.Day:
		return true
	case global.Resolutions.Week:
		return true
	case global.Resolutions.Month:
		return true
	}
	return false
}

/*
对比1分钟时间戳 如果超过当前时间则更改为当前整分-60秒
*/
func CompareMinute1Resolution(t int64) int64 {
	t2 := time.Now().Truncate(time.Minute).Unix()
	if t > t2-60 {
		return t2 - 60
	}
	return t
}

/*
对比5分钟时间戳
找出所有5分钟的时间戳并返回 包括开始和结束的时间戳在内
*/
func CompareMinute5Resolution(start, end int64) []int64 {
	var result []int64

	// 找到第一个整五分钟的时间戳
	if start%300 != 0 {
		start += 300 - (start % 300)
	}
	// 从第一个整五分钟的时间戳开始，每次增加300秒（即5分钟）
	for t := start; t <= end; t += 300 {
		result = append(result, t)
	}
	return result
}

/*
对比15分钟时间戳
找出所有15分钟的时间戳并返回 包括开始和结束的时间戳在内
*/
func CompareMinute15Resolution(start, end int64) []int64 {
	var result []int64

	// 找到第一个整五分钟的时间戳
	if start%900 != 0 {
		start += 900 - (start % 900)
	}
	// 从第一个整五分钟的时间戳开始，每次增加900秒（即15分钟）
	for t := start; t <= end; t += 900 {
		result = append(result, t)
	}
	return result
}

/*
对比30分钟时间戳 带时间
找出所有30分钟的时间戳并返回 包括开始和结束的时间戳在内
*/
func CompareMinute30ResolutionTimeZone(start, end int64, timezone string) ([]int64, error) {
	var result []int64

	// 解析时区并创建 time.Location
	offset, err := parseTimezoneOffset(timezone)
	if err != nil {
		return nil, err
	}
	location := time.FixedZone(timezone, offset)

	// 将起始和结束时间转换为指定时区的时间
	startTime := time.Unix(start, 0).In(location)
	endTime := time.Unix(end, 0).In(location)

	// 找到第一个整30分钟的时间点
	offsetMinutes := startTime.Minute() % 30
	if offsetMinutes != 0 || startTime.Second() != 0 {
		startTime = startTime.Add(time.Duration(30-offsetMinutes) * time.Minute).Truncate(time.Minute)
	}

	// 从第一个整30分钟的时间点开始，每次增加30分钟，直到超过结束时间
	for current := startTime; !current.After(endTime); current = current.Add(30 * time.Minute) {
		result = append(result, current.Unix())
	}

	return result, nil
}

/*
对比1小时时间戳 带时间
找出所有1小时的时间戳并返回 包括开始和结束的时间戳在内
*/
func CompareHour1ResolutionTimeZone(start, end int64, timezone string) ([]int64, error) {
	var result []int64

	// 解析时区并创建 time.Location
	offset, err := parseTimezoneOffset(timezone)
	if err != nil {
		return nil, err
	}
	location := time.FixedZone(timezone, offset)

	// 将起始和结束时间转换为指定时区的时间
	startTime := time.Unix(start, 0).In(location)
	endTime := time.Unix(end, 0).In(location)

	// 找到第一个整1小时的时间点
	if startTime.Minute() != 0 || startTime.Second() != 0 {
		// 调整到下一个整1小时点
		startTime = startTime.Add(time.Duration(60-startTime.Minute()) * time.Minute).Truncate(time.Hour)
	}

	// 从第一个整1小时的时间点开始，每次增加1小时，直到超过结束时间
	for current := startTime; !current.After(endTime); current = current.Add(1 * time.Hour) {
		result = append(result, current.Unix())
	}

	return result, nil
}

/*
对比4小时时间戳 带时间
找出所有4小时的时间戳并返回 包括开始和结束的时间戳在内
*/
func CompareHour4ResolutionTimeZone(start, end int64, timezone string) ([]int64, error) {
	var result []int64

	// 解析时区并创建 time.Location
	offset, err := parseTimezoneOffset(timezone)
	if err != nil {
		return nil, err
	}
	location := time.FixedZone(timezone, offset)

	// 将起始和结束时间转换为指定时区的时间
	startTime := time.Unix(start, 0).In(location)
	endTime := time.Unix(end, 0).In(location)

	// 找到第一个整4小时的时间点
	if startTime.Hour()%4 != 0 || startTime.Minute() != 0 || startTime.Second() != 0 {
		// 调整到下一个整4小时点
		startTime = startTime.Add(time.Duration(4-startTime.Hour()%4) * time.Hour).Truncate(time.Hour)
	}

	// 从第一个整4小时的时间点开始，每次增加4小时，直到超过结束时间
	for current := startTime; !current.After(endTime); current = current.Add(4 * time.Hour) {
		result = append(result, current.Unix())
	}

	return result, nil
}

/*
对比1天时间戳 带时间
找出所有1天的时间戳并返回 包括开始和结束的时间戳在内
*/
func CompareDayResolutionTimeZone(start, end int64, timezone string) ([]int64, error) {
	var result []int64

	// 解析时区并创建 time.Location
	offset, err := parseTimezoneOffset(timezone)
	if err != nil {
		return nil, err
	}
	location := time.FixedZone(timezone, offset)

	// 将起始和结束时间转换为指定时区的时间
	startTime := time.Unix(start, 0).In(location)
	endTime := time.Unix(end, 0).In(location)

	// 找到第一个整1天的时间点
	if startTime.Hour() != 0 || startTime.Minute() != 0 || startTime.Second() != 0 {
		// 调整到下一个整1天点
		startTime = startTime.Add(time.Duration(24-startTime.Hour()) * time.Hour).Truncate(24 * time.Hour)
	}

	// 从第一个整1天的时间点开始，每次增加24小时（1天），直到超过结束时间
	for current := startTime; !current.After(endTime); current = current.Add(24 * time.Hour) {
		result = append(result, current.Unix())
	}

	return result, nil
}

/*
对比1周时间戳 带时间
找出所有1周的时间戳并返回 包括开始和结束的时间戳在内
*/
func CompareWeekResolutionTimeZone(start, end int64, timezone string) ([]int64, error) {
	var result []int64

	// 解析时区并创建 time.Location
	offset, err := parseTimezoneOffset(timezone)
	if err != nil {
		return nil, err
	}
	location := time.FixedZone(timezone, offset)

	// 将起始和结束时间转换为指定时区的时间
	startTime := time.Unix(start, 0).In(location)
	endTime := time.Unix(end, 0).In(location)

	// 找到第一个整周的时间点（假设以星期一的 00:00:00 为整周起点）
	if startTime.Weekday() != time.Monday || startTime.Hour() != 0 || startTime.Minute() != 0 || startTime.Second() != 0 {
		// 计算到下一个周一的天数
		daysUntilNextMonday := (7 - int(startTime.Weekday()) + int(time.Monday)) % 7
		// 调整到下一个整周点
		startTime = startTime.AddDate(0, 0, daysUntilNextMonday).Truncate(24 * time.Hour)
	}

	// 从第一个整周的时间点开始，每次增加168小时（7天），直到超过结束时间
	for current := startTime; !current.After(endTime); current = current.Add(7 * 24 * time.Hour) {
		result = append(result, current.Unix())
	}

	return result, nil
}

/*
对比1月时间戳 带时间
找出所有1月的时间戳并返回 包括开始和结束的时间戳在内
*/
func CompareMonthResolutionTimeZone(start, end int64, timezone string) ([]structs.MonthRange, error) {
	var result []structs.MonthRange

	// 解析时区并创建 time.Location
	offset, err := parseTimezoneOffset(timezone)
	if err != nil {
		return nil, err
	}
	location := time.FixedZone(timezone, offset)

	// 将起始和结束时间转换为指定时区的时间
	startTime := time.Unix(start, 0).In(location)
	endTime := time.Unix(end, 0).In(location)

	// 找到第一个整月的时间点（假设每月的1号00:00:00为整月起点）
	if startTime.Day() != 1 || startTime.Hour() != 0 || startTime.Minute() != 0 || startTime.Second() != 0 {
		// 调整到下一个月的1号00:00:00
		startTime = startTime.AddDate(0, 1, -startTime.Day()+1).Truncate(24 * time.Hour)
	}

	// 从第一个整月的时间点开始，每次增加1个月，直到超过结束时间
	for current := startTime; !current.After(endTime); current = current.AddDate(0, 1, 0) {
		// 获取当前月份的起始时间戳
		monthStart := current.Unix()
		// 获取当前月份的结束时间戳，设置为下个月的第1天的前一秒
		monthEnd := current.AddDate(0, 1, 0).Add(-time.Second).Unix()

		// 将当前月的时间范围加入结果
		result = append(result, structs.MonthRange{
			Start: monthStart,
			End:   monthEnd,
		})
	}

	return result, nil
}

// 除运算后保留小数
func FloatingPointCalculation(dividend, divisor int64, precision int) float64 {
	if divisor <= 0 {
		return 0.00
	}
	// 转换为 big.Float
	dividendBig := new(big.Float).SetInt64(dividend)
	divisorBig := new(big.Float).SetInt64(divisor)
	// 进行高精度除法
	result := new(big.Float).Quo(dividendBig, divisorBig)
	// 转换为 float64
	floatResult, _ := result.Float64()

	// 保留指定小数位
	scale := math.Pow(10, float64(precision))
	return math.Round(floatResult*scale) / scale
}

/*
计算当前时间的浮点值，浮点值在整个周期内平滑变化且不超过最大浮动值
startTs 插针开始时间（毫秒）
currentTs 接收到这条k线的时间（毫秒）
riseTime 爬坡阶段时间（秒）
peakTime 峰值阶段时间（秒）
fallTime 回落阶段时间（秒）
*/
func CalculateFloatValue(tmp *structs.UserShadow, startTs, currentTs int64, riseT, peakT, fallT int) (int, error) {
	// 1) 将阶段时长转为毫秒，方便与时间戳对比
	riseDuration := float64(riseT) * 1000
	peakDuration := float64(peakT) * 1000
	fallDuration := float64(fallT) * 1000
	totalDuration := riseDuration + peakDuration + fallDuration

	// 2) 计算已过去的时间（毫秒）
	elapsed := float64(currentTs - startTs)
	// 数据如果延迟就记录进数据库供数据源头参考
	if elapsed < 0 {
		logger.WarnCtx(context.Background(), "数据延迟检测",
			zap.Int64("开始时间", startTs),
			zap.Int64("当前时间", currentTs),
			zap.Float64("延迟时间毫秒", elapsed),
			zap.String("平台", tmp.Platform),
			zap.String("产品", tmp.Symbol),
			zap.Uint("控制ID", tmp.ID))
		// 记录数据库, 数据源延迟记录
		mysql.M.Create(&structs.DataDelayRecord{
			DataSourceName: tmp.Platform,
			ProductName:    tmp.Symbol,
			ControlID:      cast.ToString(tmp.ID),
			StartTime:      startTs,
			ReceiveTime:    currentTs,
			DelayTime:      elapsed,
		})
	}
	if elapsed > totalDuration {
		logger.ErrorCtx(context.Background(), "插针时间超出总时长",
			zap.Int64("开始时间", startTs),
			zap.Int64("当前时间", currentTs),
			zap.Int("爬坡时间秒", riseT),
			zap.Int("峰值时间秒", peakT),
			zap.Int("回落时间秒", fallT),
			zap.Float64("总时长毫秒", totalDuration),
			zap.Float64("已过时间毫秒", elapsed))

		return 0, errors.New("当前时间已超出插针总时长")
	}
	var stage int

	// 6) 判断所处阶段
	if elapsed <= riseDuration {
		// ==================== 阶段 1：爬坡期 ====================
		stage = 1
	} else if elapsed <= riseDuration+peakDuration {
		// ==================== 阶段 2：峰值期 ====================
		stage = 2
	} else {
		// ==================== 阶段 3：回落期 ====================
		stage = 3
	}
	return stage, nil
}

/*
价格随机命中的概率
*/
func ShouldHit(probMin, probMax float64) bool {
	if probMin < 0 {
		probMin = 0
	}
	if probMax > 1 {
		probMax = 1
	}
	if probMin > probMax {
		probMin, probMax = probMax, probMin
	}
	p := probMin + rand.Float64()*(probMax-probMin)
	r := rand.Float64()
	return r < p
}

func ShouldHitValue(probMin, probMax float64) float64 {
	if probMin < 0 {
		probMin = 0
	}
	if probMax < 0 {
		probMax = 0
	}
	if probMin > probMax {
		probMin, probMax = probMax, probMin
	}
	p := probMin + rand.Float64()*(probMax-probMin)
	return p
}

/*
返回指定小数位数，补齐后面的0
*/
func FloorPriceDecimal(price float64, dp int32) float64 {
	d := decimal.NewFromFloat(price).Truncate(dp)
	v, _ := d.Float64()
	return v
}

// FloatRange 根据最大小数位数生成 [start, end] 区间的浮点序列
func FloatRange(start, end float64) float64 {
	// 如果 start > end，你可以根据需要决定返回空或者做交换，这里假设 start <= end
	if start > end {
		return 0
	}

	// 将 start、end 转为字符串，拿到小数部分长度
	startStr := fmt.Sprintf("%f", start)
	endStr := fmt.Sprintf("%f", end)

	// 去掉多余的尾部 0 和小数点后多余的部分
	startStr = strings.TrimRight(strings.TrimRight(startStr, "0"), ".")
	endStr = strings.TrimRight(strings.TrimRight(endStr, "0"), ".")

	// 提取小数部分长度
	startDecimals := 0
	endDecimals := 0

	if idx := strings.IndexByte(startStr, '.'); idx != -1 {
		startDecimals = len(startStr) - idx - 1
	}
	if idx := strings.IndexByte(endStr, '.'); idx != -1 {
		endDecimals = len(endStr) - idx - 1
	}

	// 取两者的较大小数位数
	maxDecimals := startDecimals
	if endDecimals > maxDecimals {
		maxDecimals = endDecimals
	}

	// 计算乘数 base = 10^maxDecimals
	base := math.Pow10(maxDecimals)

	// 将 start、end 转为整数
	// 用 Round 而不是 Trunc，可以让 1.9999999 这类接近边界的值得到更准确的整数
	startInt := int(math.Round(start * base))
	endInt := int(math.Round(end * base))

	// 遍历并生成结果
	var results []float64
	for i := startInt; i <= endInt; i++ {
		val := float64(i) / base
		results = append(results, val)
	}
	if len(results) < 1 {
		return 0
	}

	// 从切片里随机选一个
	idx := rand.Intn(len(results))

	return results[idx]
}

// RandomFloat 返回 [min, max) 范围内的随机 float64
func RandomFloat(min, max float64) float64 {
	if max <= min {
		return min
	}
	r := float64(rand.Int63()) / float64((1<<63)-1)
	return min + r*(max-min)
}

// RandomBool 返回随机 true 或 false
func RandomBool() bool {
	return rand.Intn(2) == 1
}
