package influxdb

import (
	"bytes"
	"context"
	"fmt"
	"net/http"
	"os"
	"time"
	"trading_tick_server/lib/global"

	influxdb2 "github.com/influxdata/influxdb-client-go/v2"
	"github.com/influxdata/influxdb-client-go/v2/api"
	http2 "github.com/influxdata/influxdb-client-go/v2/api/http"
	"github.com/influxdata/influxdb-client-go/v2/domain"
	"go.uber.org/zap"
)

var client influxdb2.Client
var writeAPI api.WriteAPIBlocking
var nonBlockingWriteAPI api.WriteAPI
var queryAPI api.QueryAPI

func Run() {
	// 创建新的 InfluxDB 客户端
	client = influxdb2.NewClient(global.Yaml.InfluxDB.Url, global.Yaml.InfluxDB.Token)

	// 创建一个用于写入数据的上下文
	writeAPI = client.WriteAPIBlocking(global.Yaml.InfluxDB.Org, global.Yaml.InfluxDB.Bucket)
	_ = writeAPI
	// 创建一个非阻塞用于写入数据的上下文
	nonBlockingWriteAPI = client.WriteAPI(global.Yaml.InfluxDB.Org, global.Yaml.InfluxDB.Bucket)
	// **在非阻塞写入 API 上设置写入失败回调**
	nonBlockingWriteAPI.SetWriteFailedCallback(func(batch string, err http2.Error, retryAttempts uint) bool {
		// 判断是否已经重试过到指定次数
		if retryAttempts < global.Yaml.InfluxDB.RetryQuantity {
			// 尚未重试过，返回 true，表示继续重试
			return true
		} else {
			// 已经重试到指定次数，返回 false，表示放弃该批次
			// 在这里处理失败的批次数据和错误信息
			if global.Yaml.Logging.InfluxDB {
				global.Lg.Error(
					"写入失败的批次",
					zap.String("component", "InfluxDB"),
					zap.Uint("retryAttempts", retryAttempts),
					zap.String("failed_batch", batch),
					zap.Any("err", err),
				)
			}
			return false
		}
	})

	errorsCh := nonBlockingWriteAPI.Errors()
	go func() {
		for err := range errorsCh {
			if global.Yaml.Logging.InfluxDB {
				global.Lg.Error(
					"influxdb非阻塞写入错误",
					zap.String("component", "InfluxDB"),
					zap.Int64("time", time.Now().Unix()),
					zap.Error(err),
				)
			}
		}
	}()

	// 查询数据
	queryAPI = client.QueryAPI(global.Yaml.InfluxDB.Org)
	// 发送一个简单的 Ping 查询来测试连接
	ready, err := client.Ready(context.Background())
	if err != nil {
		if global.Yaml.Logging.InfluxDB {
			global.Lg.Error(
				"InfluxDB is not ready1",
				zap.String("Url", global.Yaml.InfluxDB.Url),
				zap.String("Token", global.Yaml.InfluxDB.Token),
				zap.String("component", "InfluxDB"),
				zap.Error(err),
			)
		}
		os.Exit(-1)
	}
	if ready == nil || ready.Status == nil || string(*ready.Status) != string(domain.ReadyStatusReady) {
		if global.Yaml.Logging.InfluxDB {
			global.Lg.Error(
				"InfluxDB is not ready2",
				zap.String("Url", global.Yaml.InfluxDB.Url),
				zap.String("Token", global.Yaml.InfluxDB.Token),
				zap.String("Status", string(*ready.Status)),
				zap.String("ReadyStatusReady", string(domain.ReadyStatusReady)),
				zap.String("component", "InfluxDB"),
			)
		}
		os.Exit(-1)
	}
	query := `buckets()`
	_, err = queryAPI.Query(context.Background(), query)
	if err != nil {
		if global.Yaml.Logging.InfluxDB {
			global.Lg.Error(
				"InfluxDB Query failed",
				zap.String("Url", global.Yaml.InfluxDB.Url),
				zap.String("Token", global.Yaml.InfluxDB.Token),
				zap.String("component", "InfluxDB"),
				zap.Error(err),
			)
		}
		os.Exit(-1)
	}
}

// 取数据量
func GetCount(measurement, symbol string) (int64, error) {
	fluxQuery := fmt.Sprintf(`
	from(bucket: "%s")
		|> range(start: 1970-01-01T00:00:00Z)
		|> filter(fn: (r) => r["_measurement"] == "%s")
		|> filter(fn: (r) => r["symbol"] == "%s")
		|> filter(fn: (r) => r["_field"] == "open")
		|> group(columns: [])
		|> count()
`, global.Yaml.InfluxDB.Bucket, measurement, symbol)
	result, err := queryAPI.Query(context.Background(), fluxQuery)
	if err != nil {
		return 0, err
	}
	var count int64
	if result.Next() {
		val := result.Record().ValueByKey("_value")
		if valFloat, ok := val.(int64); ok {
			count = valFloat
		}
	}
	if result.Err() != nil {
		return 0, result.Err()
	}

	return count, nil
}

// 取最大时间戳
func GetLastTimestamp(measurement, symbol string) (int64, error) {
	fluxQuery := fmt.Sprintf(`
	from(bucket: "%s")
		|> range(start: 1970-01-01T00:00:00Z)
		|> filter(fn: (r) => r["_measurement"] == "%s")
		|> filter(fn: (r) => r["symbol"] == "%s")
		|> filter(fn: (r) => r["_field"] == "open")
		|> group(columns: [])
		|> last()
`, global.Yaml.InfluxDB.Bucket, measurement, symbol)
	result, err := queryAPI.Query(context.Background(), fluxQuery)
	if err != nil {
		return 0, err
	}

	var maxTime int64 = -1

	// 解析查询结果
	for result.Next() {
		record := result.Record()
		t := record.Time().Unix()
		if t > maxTime {
			maxTime = t
		}
	}

	// 检查查询中的错误
	if result.Err() != nil {
		return 0, result.Err()
	}

	return maxTime, nil
}

// 取最小时间戳
func GetFirstTimestamp(measurement, symbol string) (int64, error) {
	fluxQuery := fmt.Sprintf(`
	from(bucket: "%s")
		|> range(start: 1970-01-01T00:00:00Z)
		|> filter(fn: (r) => r["_measurement"] == "%s")
		|> filter(fn: (r) => r["symbol"] == "%s")
		|> filter(fn: (r) => r["_field"] == "open")
		|> group(columns: [])
		|> first()
`, global.Yaml.InfluxDB.Bucket, measurement, symbol)
	result, err := queryAPI.Query(context.Background(), fluxQuery)
	if err != nil {
		return 0, err
	}

	var maxTime int64

	// 解析查询结果
	for result.Next() {
		record := result.Record()
		t := record.Time().Unix()
		if t > maxTime {
			maxTime = t
		}
	}

	// 检查查询中的错误
	if result.Err() != nil {
		return 0, result.Err()
	}

	return maxTime, nil
}

// 取最大时间戳
func GetMaxTimestamp(measurement, symbol string) (int64, error) {
	fluxQuery := fmt.Sprintf(`
	from(bucket: "%s")
	  |> range(start: -1y)
	  |> filter(fn: (r) => r["_measurement"] == "%s")
	  |> filter(fn: (r) => r["symbol"] == "%s")
	  |> filter(fn: (r) => r["_field"] == "close")
	  |> sort(columns: ["_time"], desc: true)
	  |> group()
	  |> limit(n: 1)
	  |> keep(columns: ["_time"])
`, global.Yaml.InfluxDB.Bucket, measurement, symbol)

	result, err := queryAPI.Query(context.Background(), fluxQuery)
	if err != nil {
		return 0, err
	}

	var maxTime int64 = -1

	// 解析查询结果
	for result.Next() {
		record := result.Record()
		t := record.Time().Unix()
		if t > maxTime {
			maxTime = t
		}
	}

	// 检查查询中的错误
	if result.Err() != nil {
		return 0, result.Err()
	}

	return maxTime, nil
}

// 取最小时间戳
func GetMinTimestamp(measurement, symbol string) (int64, error) {
	fluxQuery := fmt.Sprintf(`
	from(bucket: "%s")
	  |> range(start: -5y)
	  |> filter(fn: (r) => r["_measurement"] == "%s")
	  |> filter(fn: (r) => r["symbol"] == "%s")
	  |> filter(fn: (r) => r["_field"] == "close")
	  |> sort(columns: ["_time"])
	  |> limit(n: 1)
	  |> keep(columns: ["_time"])
`, global.Yaml.InfluxDB.Bucket, measurement, symbol)

	result, err := queryAPI.Query(context.Background(), fluxQuery)
	if err != nil {
		return 0, err
	}

	var maxTime int64

	// 解析查询结果
	for result.Next() {
		record := result.Record()
		t := record.Time().Unix()
		if t > maxTime {
			maxTime = t
		}
	}

	// 检查查询中的错误
	if result.Err() != nil {
		return 0, result.Err()
	}

	return maxTime, nil
}

// 删除所有数据
func DeleteMeasurementData(measurement string) error {
	url := fmt.Sprintf("%s/api/v2/delete?org=%s&bucket=%s", global.Yaml.InfluxDB.Url, global.Yaml.InfluxDB.Org, global.Yaml.InfluxDB.Bucket)

	// 构建 JSON 请求体
	jsonStr := []byte(`{
        "start": "1970-01-01T00:00:00Z",
        "stop": "2100-01-01T00:00:00Z",
        "predicate": "_measurement=\"` + measurement + `\""
    }`)

	// 创建请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	if err != nil {
		return err
	}

	// 设置请求头
	req.Header.Set("Authorization", "Token "+global.Yaml.InfluxDB.Token)
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusNoContent {
		return fmt.Errorf("failed to delete data, status code: %d", resp.StatusCode)
	}
	return nil
}
