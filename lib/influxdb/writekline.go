package influxdb

import (
	"context"
	"time"
	"trading_tick_server/lib/function"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/structs"
	pb "trading_tick_server/tickserver/data_source_grpc/data_source_proto"

	influxdb2 "github.com/influxdata/influxdb-client-go/v2"
	"go.uber.org/zap"
	"golang.org/x/time/rate"
)

// 初始化全局限流器，限制批次写入的频率
var limiter *rate.Limiter

func SetLimiter() {
	limiter = rate.NewLimiter(rate.Every(time.Second/time.Duration(global.Yaml.InfluxDB.IntervalWrite)), 1)
}

/*
写入最新K线数据
*/
func WriteKlineData(t []pb.ReceiveDataKline, timeZone string) error {
	if len(t) < 1 {
		if global.Yaml.Logging.InfluxDB {
			global.Lg.Error(
				"数据为空",
				zap.Int("len_t", len(t)),
				zap.String("component", "Write1mKlineData"),
			)
		}
		return nil
	}

	// 使用限流器限制批次写入的频率
	if err := limiter.Wait(context.Background()); err != nil {
		if global.Yaml.Logging.InfluxDB {
			global.Lg.Error(
				"批次限流失败",
				zap.String("component", "Write1mKlineData"),
				zap.Error(err),
			)
		}
		time.Sleep(time.Second * 2)
	}

	for _, v := range t {
		table := function.GetInfluxDBTable(v.Platform, v.Symbol, timeZone, v.Resolutions)
		p := influxdb2.NewPointWithMeasurement(table).
			AddTag("symbol", "real").
			AddField("open", v.Open).
			AddField("close", v.Close).
			AddField("high", v.High).
			AddField("low", v.Low).
			AddField("volume", v.Volume).
			AddField("turnover", v.Turnover).
			SetTime(time.Unix(v.Time, 0))
		nonBlockingWriteAPI.WritePoint(p)
	}
	// 提交批量写入请求
	nonBlockingWriteAPI.Flush()
	return nil
}

/*
批量写入K线数据
symbol real是真实的 其它标签是用户自己的
open 开盘价
close 收盘价
high 最高价
low 最低价
volume 成交量
turnover 成交额
mark 记录来源
*/
func BatchWriteKlineData(table string, t []structs.AllDataKline, mark string) error {
	if len(t) < 1 {
		if global.Yaml.Logging.InfluxDB {
			global.Lg.Error(
				"数据为空",
				zap.Int("len_t", len(t)),
				zap.String("mark", mark),
				zap.String("component", "InitWrite1mKlineData"),
			)
		}
		return nil
	}

	// 使用限流器限制批次写入的频率
	if err := limiter.Wait(context.Background()); err != nil {
		if global.Yaml.Logging.InfluxDB {
			global.Lg.Error(
				"批次限流失败",
				zap.String("mark", mark),
				zap.String("component", "InitWrite1mKlineData"),
				zap.Error(err),
			)
		}
		time.Sleep(time.Second * 2)
	}
	for _, v := range t {
		p := influxdb2.NewPointWithMeasurement(table).
			AddTag("symbol", "real").
			AddField("open", v.Open).
			AddField("close", v.Close).
			AddField("high", v.High).
			AddField("low", v.Low).
			AddField("volume", v.Volume).
			AddField("turnover", v.Turnover).
			SetTime(time.Unix(v.Time, 0))
		nonBlockingWriteAPI.WritePoint(p)
	}
	// 提交批量写入请求
	nonBlockingWriteAPI.Flush()
	return nil
}

/*
写入插针数据
*/
func WriteKlineShadowData(userID, platform, symbol, timeZone, interval string, timestamp int64, value string) {
	table := function.GetInfluxDBShadowTable(userID, platform, symbol, timeZone, interval)
	p := influxdb2.NewPointWithMeasurement(table).
		AddTag("symbol", interval).
		AddField("shadow", value).
		SetTime(time.Unix(timestamp, 0))
	nonBlockingWriteAPI.WritePoint(p)
}
