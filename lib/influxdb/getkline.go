package influxdb

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"trading_tick_server/lib/function"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/structs"

	"github.com/sknun/cf/cast"
)

// 取历史k线
func GetHistoryKlineData(o structs.QueryKlineParam) ([]structs.KlineDataRet, int64, int64, error) {
	table := function.GetInfluxDBTable(o.Platform, o.Symbol, o.TimeZone, o.Resolutions)
	// 判断开始和结束时间的范围
	var startTime int64
	switch o.Resolutions {
	case global.Resolutions.Minute1:
		startTime = o.EndTime - 86400*7
	case global.Resolutions.Minute5:
		startTime = o.EndTime - 86400*7
	case global.Resolutions.Minute15:
		startTime = o.EndTime - 86400*7
	case global.Resolutions.Minute30:
		startTime = o.EndTime - 86400*15
	case global.Resolutions.Hour1:
		startTime = o.EndTime - 86400*30
	case global.Resolutions.Hour4:
		startTime = o.EndTime - 86400*60
	case global.Resolutions.Day:
		startTime = o.EndTime - 86400*365
	case global.Resolutions.Week:
		startTime = o.EndTime - 86400*2555
	case global.Resolutions.Month:
		startTime = o.EndTime - 86400*36500
	default:
		startTime = o.EndTime - 86400
	}
	return GetKlineData(table, startTime, o.EndTime, o.Count)
}

/*
取k线
endTime+1 确保查询出前后时间都包含的数据
*/
func GetKlineData(table string, startTime, endTime int64, count int) ([]structs.KlineDataRet, int64, int64, error) {
	fluxQuery := fmt.Sprintf(`
	from(bucket: "%s")
	|> range(start: time(v: %d), stop: time(v: %d))
	|> filter(fn: (r) => r._measurement == "%s" and r.symbol == "real")
	|> sort(columns: ["_time"], desc: true)
	%s
	`, global.Yaml.InfluxDB.Bucket,
		function.SecondsToNanoseconds(startTime),
		function.SecondsToNanoseconds(endTime+1),
		table,
		func() string {
			if count > 0 {
				return fmt.Sprintf(`|> limit(n: %d)`, count)
			}
			return ""
		}(),
	)

	// log.Println(fluxQuery)
	result, err := queryAPI.Query(context.Background(), fluxQuery)
	if err != nil {
		return nil, 0, 0, err
	}

	var klineDataList []structs.KlineDataRet
	klineDataMap := map[int64]map[string]interface{}{}

	for result.Next() {
		record := result.Record()
		timestamp := record.Time().Unix()
		if klineDataMap[timestamp] == nil {
			klineDataMap[timestamp] = make(map[string]interface{})
		}
		// 获取 _field 的值，它存储了字段名
		field := record.ValueByKey("_field").(string)
		klineDataMap[timestamp][field] = record.ValueByKey("_value")
		if cast.ToString(klineDataMap[timestamp]["symbol"]) == "" {
			klineDataMap[timestamp]["symbol"] = record.ValueByKey("symbol").(string)
		}
	}
	if result.Err() != nil {
		return nil, 0, 0, result.Err()
	}
	// 找出时间最小和最大
	var minTime, maxTime int64
	for k := range klineDataMap {
		if minTime == 0 {
			minTime = k
		} else if k < minTime {
			minTime = k
		}
		if maxTime == 0 {
			maxTime = k
		} else if k > maxTime {
			maxTime = k
		}
	}

	for k, v := range klineDataMap {
		klineDataList = append(klineDataList, structs.KlineDataRet{
			Time:     k,
			Open:     cast.ToFloat64(v["open"]),
			Close:    cast.ToFloat64(v["close"]),
			High:     cast.ToFloat64(v["high"]),
			Low:      cast.ToFloat64(v["low"]),
			Volume:   cast.ToFloat64(v["volume"]),
			Turnover: cast.ToFloat64(v["turnover"]),
		})
	}
	// 使用 sort.Slice 进行排序
	sort.Slice(klineDataList, func(i, j int) bool {
		return klineDataList[i].Time < klineDataList[j].Time // 按 Value 字段升序排序
	})
	return klineDataList, minTime, maxTime, nil
}

/*
取1条k线
*/
func GetOneKlineData(table string, targetTime int64) (structs.KlineDataRet, error) {
	var data structs.KlineDataRet
	fluxQuery := fmt.Sprintf(`
	from(bucket: "%s")
	|> range(start: time(v: %d), stop: time(v: %d))
	|> filter(fn: (r) => 
		r._measurement == "%s" 
		and r.symbol == "real" 
		and r._time == time(v: %d)
	)
`,
		global.Yaml.InfluxDB.Bucket,
		function.SecondsToNanoseconds(targetTime-1),
		function.SecondsToNanoseconds(targetTime+1),
		table,
		function.SecondsToNanoseconds(targetTime),
	)

	result, err := queryAPI.Query(context.Background(), fluxQuery)
	if err != nil {
		return data, err
	}

	for result.Next() {
		record := result.Record()
		field := record.Field()
		value := record.Value()
		data.Time = record.Time().Unix()
		switch field {
		case "open":
			if v, ok := value.(float64); ok {
				data.Open = v
			}
		case "high":
			if v, ok := value.(float64); ok {
				data.High = v
			}
		case "low":
			if v, ok := value.(float64); ok {
				data.Low = v
			}
		case "close":
			if v, ok := value.(float64); ok {
				data.Close = v
			}
		case "volume":
			if v, ok := value.(float64); ok {
				data.Volume = v
			}
		case "turnover":
			if v, ok := value.(float64); ok {
				data.Turnover = v
			}
		}
	}
	if data.Open < 0 || data.High < 0 || data.Low < 0 || data.Close < 0 {
		return data, errors.New("查询出来的值有非法小于0")
	}
	if result.Err() != nil {
		return data, result.Err()
	}
	if data.Time != targetTime {
		return data, errors.New("查询出来的时间不相符")
	}
	return data, nil
}
