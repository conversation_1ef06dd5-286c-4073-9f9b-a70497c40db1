package influxdb

import (
	"context"
	"fmt"
	"github.com/sknun/cf/cast"
	"sort"
	"trading_tick_server/lib/function"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/structs"
)

// QueryKlineData 查询指定时间范围的K线数据，返回structs.AllDataKline格式
func QueryKlineData(table string, startTime, endTime int64) ([]structs.AllDataKline, error) {
	fluxQuery := fmt.Sprintf(`
	from(bucket: "%s")
	|> range(start: time(v: %d), stop: time(v: %d))
	|> filter(fn: (r) => r._measurement == "%s" and r.symbol == "real")
	|> sort(columns: ["_time"])
	`, global.Yaml.InfluxDB.Bucket,
		function.SecondsToNanoseconds(startTime),
		function.SecondsToNanoseconds(endTime+1),
		table,
	)

	result, err := queryAPI.Query(context.Background(), fluxQuery)
	if err != nil {
		return nil, fmt.Errorf("查询K线数据失败: %w", err)
	}

	klineDataMap := map[int64]map[string]interface{}{}

	// 解析查询结果
	for result.Next() {
		record := result.Record()
		timestamp := record.Time().Unix()
		if klineDataMap[timestamp] == nil {
			klineDataMap[timestamp] = make(map[string]interface{})
		}

		// 获取字段名和值
		field := record.ValueByKey("_field").(string)
		value := record.ValueByKey("_value")
		klineDataMap[timestamp][field] = value

		// 保存平台和标的信息
		if klineDataMap[timestamp]["platform"] == nil {
			if platform := record.ValueByKey("platform"); platform != nil {
				klineDataMap[timestamp]["platform"] = platform.(string)
			}
		}
		if klineDataMap[timestamp]["symbol_name"] == nil {
			if symbolName := record.ValueByKey("symbol_name"); symbolName != nil {
				klineDataMap[timestamp]["symbol_name"] = symbolName.(string)
			}
		}
	}

	if result.Err() != nil {
		return nil, fmt.Errorf("查询结果解析失败: %w", result.Err())
	}

	// 转换为AllDataKline格式
	var klineDataList []structs.AllDataKline
	for timestamp, fields := range klineDataMap {
		// 确保所有必要字段都存在
		if fields["open"] == nil || fields["close"] == nil ||
			fields["high"] == nil || fields["low"] == nil ||
			fields["volume"] == nil || fields["turnover"] == nil {
			continue // 跳过不完整的数据
		}

		klineData := structs.AllDataKline{
			Time:     timestamp,
			Open:     cast.ToFloat64(fields["open"]),
			Close:    cast.ToFloat64(fields["close"]),
			High:     cast.ToFloat64(fields["high"]),
			Low:      cast.ToFloat64(fields["low"]),
			Volume:   cast.ToFloat64(fields["volume"]),
			Turnover: cast.ToFloat64(fields["turnover"]),
		}

		// 如果有平台和标的信息，也填入
		if platform, ok := fields["platform"].(string); ok {
			klineData.Platform = platform
		}
		if symbolName, ok := fields["symbol_name"].(string); ok {
			klineData.Symbol = symbolName
		}

		klineDataList = append(klineDataList, klineData)
	}

	// 按时间排序
	sort.Slice(klineDataList, func(i, j int) bool {
		return klineDataList[i].Time < klineDataList[j].Time
	})

	return klineDataList, nil
}

// QueryKlineDataWithLimit 查询指定时间范围和数量限制的K线数据
func QueryKlineDataWithLimit(table string, startTime, endTime int64, limit int) ([]structs.AllDataKline, error) {
	fluxQuery := fmt.Sprintf(`
	from(bucket: "%s")
	|> range(start: time(v: %d), stop: time(v: %d))
	|> filter(fn: (r) => r._measurement == "%s" and r.symbol == "real")
	|> sort(columns: ["_time"])
	%s
	`, global.Yaml.InfluxDB.Bucket,
		function.SecondsToNanoseconds(startTime),
		function.SecondsToNanoseconds(endTime+1),
		table,
		func() string {
			if limit > 0 {
				return fmt.Sprintf(`|> limit(n: %d)`, limit)
			}
			return ""
		}(),
	)

	result, err := queryAPI.Query(context.Background(), fluxQuery)
	if err != nil {
		return nil, fmt.Errorf("查询K线数据失败: %w", err)
	}

	klineDataMap := map[int64]map[string]interface{}{}

	for result.Next() {
		record := result.Record()
		timestamp := record.Time().Unix()
		if klineDataMap[timestamp] == nil {
			klineDataMap[timestamp] = make(map[string]interface{})
		}

		field := record.ValueByKey("_field").(string)
		value := record.ValueByKey("_value")
		klineDataMap[timestamp][field] = value

		// 保存其他标签信息
		if klineDataMap[timestamp]["platform"] == nil {
			if platform := record.ValueByKey("platform"); platform != nil {
				klineDataMap[timestamp]["platform"] = platform.(string)
			}
		}
		if klineDataMap[timestamp]["symbol_name"] == nil {
			if symbolName := record.ValueByKey("symbol_name"); symbolName != nil {
				klineDataMap[timestamp]["symbol_name"] = symbolName.(string)
			}
		}
	}

	if result.Err() != nil {
		return nil, fmt.Errorf("查询结果解析失败: %w", result.Err())
	}

	var klineDataList []structs.AllDataKline
	for timestamp, fields := range klineDataMap {
		if fields["open"] == nil || fields["close"] == nil ||
			fields["high"] == nil || fields["low"] == nil ||
			fields["volume"] == nil || fields["turnover"] == nil {
			continue
		}

		klineData := structs.AllDataKline{
			Time:     timestamp,
			Open:     cast.ToFloat64(fields["open"]),
			Close:    cast.ToFloat64(fields["close"]),
			High:     cast.ToFloat64(fields["high"]),
			Low:      cast.ToFloat64(fields["low"]),
			Volume:   cast.ToFloat64(fields["volume"]),
			Turnover: cast.ToFloat64(fields["turnover"]),
		}

		if platform, ok := fields["platform"].(string); ok {
			klineData.Platform = platform
		}
		if symbolName, ok := fields["symbol_name"].(string); ok {
			klineData.Symbol = symbolName
		}

		klineDataList = append(klineDataList, klineData)
	}

	sort.Slice(klineDataList, func(i, j int) bool {
		return klineDataList[i].Time < klineDataList[j].Time
	})

	return klineDataList, nil
}
