package cacheimport

import (
	"encoding/json"
	"time"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/redis"

	"trading_tick_server/lib/structs"

	"github.com/sknun/cf/cast"
	"go.uber.org/zap"
)

func UserKlineShadowSet(param structs.UserShadow) error {
	result, err := json.Marshal(param)
	if err != nil {
		return err
	}
	key := "UserKlineShadow:" + cast.ToString(param.UserID) + "_" + param.Symbol
	err = redis.SetString(key, result, 999999*time.Hour) // 这里的 TTL 因该改为这条控盘的总持续时间,时间到了自动删除
	return err
}

/*
读取是否有插针要处理
key=userID_Symbol
*/
func UserKlineShadowGet(userID, symbol string) structs.UserShadow {
	var param structs.UserShadow
	key := "UserKlineShadow:" + userID + "_" + symbol
	val, err := redis.GetString(key)
	if err != nil {
		return param
	}
	if val == "" || val == "{}" {
		return param
	}
	err = json.Unmarshal([]byte(val), &param)
	if err != nil {
		global.Lg.Error(
			"redis取读插针操作json.Unmarshal失败",
			zap.String("key", key),
			zap.String("val", val),
			zap.String("component", "UserKlineShadowGet"),
			zap.Error(err),
		)
	}
	return param
}

// 删除插针缓存
func UserKlineShadowDelStruct(param structs.UserShadow) error {
	key := "UserKlineShadow:" + cast.ToString(param.UserID) + "_" + param.Symbol
	return redis.DelString(key)
}

// 删除插针缓存(和上面一样功能)
func UserKlineShadowDelKey(userID, symbol string) error {
	key := "UserKlineShadow:" + userID + "_" + symbol
	return redis.DelString(key)
}
