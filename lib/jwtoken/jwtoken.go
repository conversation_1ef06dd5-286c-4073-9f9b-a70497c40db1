package jwtoken

import (
	"fmt"
	"time"
	"trading_tick_server/lib/global"

	"github.com/golang-jwt/jwt/v4"
	"go.uber.org/zap"
)

type Claims struct {
	jwt.RegisteredClaims
}

// GenerateToken 生成 JWT token
func GenerateToken(id string) (string, error) {
	claims := Claims{
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Duration(global.Yaml.WebServer.JWTExpiryTime) * time.Hour)),
			Issuer:    global.Yaml.WebServer.JWTIssuer,
			ID:        id,
		},
	}

	// 使用指定的签名方法和声明生成 token
	token, err := jwt.NewWithClaims(jwt.SigningMethodHS256, claims).SignedString([]byte(global.Yaml.WebServer.JWTSECRETKEY))
	if err != nil {
		zap.L().Error("failed to generate token", zap.Error(err))
		return "", err
	}
	return token, nil
}

// ParseToken 解析并验证 token
func ParseToken(tokenStr string) (*Claims, error) {
	// 解析 token，使用自定义的回调函数返回签名密钥
	token, err := jwt.ParseWithClaims(tokenStr, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			zap.L().Error("unexpected signing method", zap.String("alg", token.Header["alg"].(string)))
			return nil, fmt.Errorf("unexpected signing method")
		}
		return global.Yaml.WebServer.JWTSECRETKEY, nil
	})
	if err != nil {
		zap.L().Error("failed to parse token", zap.Error(err))
		return nil, err
	}

	// 检查 token 是否有效
	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	} else {
		zap.L().Error("invalid token")
		return nil, fmt.Errorf("invalid token")
	}
}
