package shard_count

/*
分片计数器
*/
import (
	"hash/fnv"
	"sort"
	"sync"
)

type shard struct {
	mu sync.Mutex
	m  map[string]int64
}

type ShardedCounter struct {
	shardCount int
	shards     []*shard
}

func NewShardedCounter(shardCount int) *ShardedCounter {
	sc := &ShardedCounter{
		shardCount: shardCount,
		shards:     make([]*shard, shardCount),
	}
	for i := 0; i < shardCount; i++ {
		sc.shards[i] = &shard{
			m: make(map[string]int64),
		}
	}
	return sc
}

func (sc *ShardedCounter) getShard(key string) *shard {
	h := fnv.New32a()
	_, _ = h.Write([]byte(key))
	return sc.shards[uint(h.Sum32())%uint(sc.shardCount)]
}

func (sc *ShardedCounter) Inc(key string) {
	s := sc.getShard(key)
	s.mu.Lock()
	s.m[key]++
	s.mu.Unlock()
}

func (sc *ShardedCounter) Get(key string) int64 {
	var result int64
	s := sc.getShard(key)
	s.mu.Lock()
	defer s.mu.Unlock()
	val, ok := s.m[key]
	if ok {
		// key 存在
		result = val
		s.m[key] = 0
	} else {
		// key 不存在
		result = -1
	}
	return result
}

// GetAll 如果存在该 key，则返回实际的计数值并置 0；如果 key 不存在，则返回 -1。
func (sc *ShardedCounter) GetAll(keys []string) map[string]int64 {
	result := make(map[string]int64, len(keys))

	// 1. 找出各 key 所属的分片
	shardMap := make(map[*shard][]string)
	for _, key := range keys {
		s := sc.getShard(key)
		shardMap[s] = append(shardMap[s], key)
	}

	// 2. 收集并排序这些分片，保证加锁顺序固定，避免死锁
	uniqueShards := make([]*shard, 0, len(shardMap))
	for s := range shardMap {
		uniqueShards = append(uniqueShards, s)
	}

	// 创建一个 map 记录各分片在 sc.shards 中的下标
	shardIndex := make(map[*shard]int, len(sc.shards))
	for i, s := range sc.shards {
		shardIndex[s] = i
	}

	// 按分片的索引顺序排序
	sort.Slice(uniqueShards, func(i, j int) bool {
		return shardIndex[uniqueShards[i]] < shardIndex[uniqueShards[j]]
	})

	// 3. 按顺序一次性锁住所有需要操作的分片
	for _, s := range uniqueShards {
		s.mu.Lock()
	}

	// 4. 读取并置 0；若 key 不存在，则返回 -1
	for s, keyList := range shardMap {
		for _, key := range keyList {
			val, ok := s.m[key]
			if ok {
				// key 存在
				result[key] = val
				s.m[key] = 0
			} else {
				// key 不存在
				result[key] = -1
			}
		}
	}

	// 5. 解锁（顺序与加锁相反）
	for i := len(uniqueShards) - 1; i >= 0; i-- {
		uniqueShards[i].mu.Unlock()
	}

	return result
}
