package cache

import (
	"encoding/json"
	"errors"
	"time"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/redis"
	"trading_tick_server/lib/structs"
)

/*
 后台登录token和用户数据
*/

func SysUserSet(token string, user structs.SysUser) error {
	result, err := json.<PERSON>(user)
	if err != nil {
		return err
	}
	key := "SYSTEMUSER:" + token
	err = redis.SetString(key, result, time.Duration(global.Yaml.WebServer.JWTExpiryTime)*time.Hour)
	return err
}

func SysUserGet(token string) (structs.SysUser, error) {
	var u structs.SysUser
	key := "SYSTEMUSER:" + token
	val, err := redis.GetString(key)
	if err != nil {
		return u, err
	}
	if val == "" || val == "{}" {
		return u, errors.New("空数据")
	}
	err = json.Unmarshal([]byte(val), &u)
	if err != nil {
		return u, err
	}
	return u, nil
}

func SysUserDel(token string) error {
	key := "SYSTEMUSER:" + token
	return redis.DelString(key)
}
