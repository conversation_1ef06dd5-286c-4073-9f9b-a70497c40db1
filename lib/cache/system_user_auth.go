package cache

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"
	"trading_tick_server/lib/function"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/mysql"
	"trading_tick_server/lib/redis"
	"trading_tick_server/lib/structs"

	"github.com/sknun/cf/cast"
)

func SystemUserAuth(id uint, path_method string) bool {
	var user structs.SysUser
	key := "SYSTEMUSERAUTH:" + function.MD5(cast.ToString(id))
	permissions, err := getAuthString(key)

	if err != nil {
		if err = mysql.M.Preload("SysUserRole.SysRolePermission").First(&user, id).Error; err != nil {
			return false
		}

		for _, role := range user.SysUserRole {
			for _, perm := range role.SysRolePermission {
				permissions = append(permissions, perm.Resource)
			}
		}

		if len(permissions) < 1 {
			return true
		}

		b, err := json.Marshal(&permissions)
		if err != nil {
			return false
		}

		if err = redis.SetString(key, string(b), time.Duration(1)*time.Hour); err != nil {
			return false
		}
	}
	permissions = append(permissions, global.SystemCommonAuth...)
	fmt.Println(permissions)
	for _, v := range permissions {
		if v == path_method {
			return true
		}
	}
	return false
}

func getAuthString(key string) ([]string, error) {
	var perm []string
	val, err := redis.GetString(key)
	if err != nil {
		return perm, err
	}
	if val == "" || val == "{}" {
		return perm, errors.New("空数据")
	}
	err = json.Unmarshal([]byte(val), &perm)
	return perm, err
}

func SystemUserAuthDel(id uint) {
	key := "SYSTEMUSERAUTH:" + function.MD5(cast.ToString(id))
	redis.DelString(key)
}

func SystemUserAuthDelAll() {
	key := "SYSTEMUSERAUTH:"
	redis.DelAllString(key)
}
