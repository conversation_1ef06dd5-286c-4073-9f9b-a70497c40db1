package cache

import (
	"time"

	"trading_tick_server/lib/function"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/redis"
)

/*
 后台登录token和用户数据
*/

func GoogleTotpHeartbeatSet(token string) error {
	key := "GoogleTotpHeartbeat:" + function.MD5(token)
	err := redis.SetString(key, "heartbeat", time.Duration(global.Yaml.WebServer.HeartbeatValidity)*time.Minute)
	return err
}

func GoogleTotpHeartbeatGet(token string) bool {
	key := "GoogleTotpHeartbeat:" + function.MD5(token)
	val, err := redis.GetString(key)
	if err != nil {
		return false
	}
	if val != "heartbeat" {
		return false
	}
	return true
}

func GoogleTotpHeartbeatDel(token string) error {
	key := "GoogleTotpHeartbeat:" + function.MD5(token)
	return redis.DelString(key)
}
