package cache

import (
	"sync"
	"sync/atomic"
	"trading_tick_server/lib/function"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/mysql"
	"trading_tick_server/lib/structs"

	"go.uber.org/zap"
)

// key是 平台_产品
var cacheTick sync.Map

var (
	tickFunc   atomic.Value
	tickFuncMu sync.Mutex
)

// 将键值对存储到 sync.Map 中
func cacheTickSetOne(key string, value structs.TickCache) {
	cacheTick.Store(key, value)
}

// 根据键获取值，如果键不存在，返回空数据 非nil
func CacheTickGetOne(key string) structs.TickCache {
	value, _ := cacheTick.Load(key)
	if value == nil {
		return structs.TickCache{}
	}
	return value.(structs.TickCache)
}

// 读取操作，无需加锁
func CacheTickSliceGet() []structs.TickCache {
	return tickFunc.Load().([]structs.TickCache)
}

// 写入操作，需要加锁
func CacheTickSliceSet() {
	tickFuncMu.Lock()
	defer tickFuncMu.Unlock()
	if tickFunc.Load() == nil {
		tickFunc.Store([]structs.TickCache{})
	}
	var list []structs.Tick
	exists := make(map[string]bool)
	err := mysql.M.Model(&structs.Tick{}).Where("status = 1").Find(&list).Error
	if len(list) < 1 {
		global.Lg.Info(
			"产品数据为空，或读取失败",
			zap.String("component", "CacheTickSliceSet"),
			zap.Error(err),
		)
		return
	}
	list2 := make([]structs.TickCache, 0, len(list))
	for _, v := range list {
		vTmp := structs.TickCache{
			ID:             v.ID,
			Symbolname:     v.Symbolname,
			Symbol:         v.Symbol,
			ReplaceSymbol:  v.ReplaceSymbol,
			Type:           v.Type,
			SubType:        v.SubType,
			Platform:       v.Platform,
			InitDone:       v.InitDone,
			InitTime:       v.InitTime,
			Maximum:        v.Maximum,
			Oncecount:      v.Oncecount,
			DesignatedTime: v.DesignatedTime,
			TimeZone:       function.GetTickTimeZone(v.TimeZone),
			ShadowDebug:    v.ShadowDebug,
			KlineDebug:     v.KlineDebug,
			KlineFix:       v.KlineFix,
			ErrorMargin:    v.ErrorMargin,
		}
		list2 = append(list2, vTmp)
		cacheTickSetOne(function.GetPlatformSymbol(v.Platform, v.Symbol), vTmp)
		exists[function.GetPlatformSymbol(v.Platform, v.Symbol)] = true
	}
	tickFunc.Store(list2)
	var toDelete []string
	// 遍历sync.Map 删除不存在的数据
	cacheTick.Range(func(key, value interface{}) bool {
		k, ok := key.(string)
		if ok && !exists[k] {
			toDelete = append(toDelete, k)
		}
		return true
	})
	for _, key := range toDelete {
		cacheTick.Delete(key)
	}
}
