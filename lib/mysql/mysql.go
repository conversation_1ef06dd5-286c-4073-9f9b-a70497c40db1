package mysql

import (
	"encoding/hex"
	"fmt"
	"strings"
	"time"
	kcc_model "trading_tick_server/internal/kline_control_center/model"
	koi_model "trading_tick_server/internal/kline_offset_immediate/model"
	kol_model "trading_tick_server/internal/kline_offset_linear/model"
	"trading_tick_server/lib/global"
	mylogger "trading_tick_server/lib/logger"
	"trading_tick_server/lib/structs"

	"github.com/sknun/cf"
	"github.com/sknun/cf/cast"
	"golang.org/x/crypto/argon2"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

var M *gorm.DB

var Prefix string

func Run() {
	var logLevel logger.LogLevel
	var err error
	switch global.Yaml.MySQL.LogLevel {
	case "info":
		logLevel = logger.Info
	case "warn":
		logLevel = logger.Warn
	case "error":
		logLevel = logger.Error
	case "silent":
		logLevel = logger.Silent
	}
	Prefix = global.Yaml.MySQL.Prefix
	dsn := fmt.Sprintf(
		"%v:%v@tcp(%v:%v)/%v?charset=utf8mb4&parseTime=True&loc=Local",
		global.Yaml.MySQL.Username,
		global.Yaml.MySQL.Password,
		global.Yaml.MySQL.Host,
		global.Yaml.MySQL.Port,
		global.Yaml.MySQL.Database,
	)
	M, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			TablePrefix:   Prefix, // 表前辍
			SingularTable: true,   // 禁止表名复数
		},
		Logger: mylogger.NewGormLogger(logLevel),
	})
	if err != nil {
		panic(fmt.Errorf("mySQL database connection failed: %w", err))
	}

	sqlDB, err := M.DB()
	if err != nil {
		panic(fmt.Errorf("failed to get sql.DB: %w", err))
	}
	// 设置连接池参数
	sqlDB.SetMaxOpenConns(100)                // 最大100个连接
	sqlDB.SetMaxIdleConns(10)                 // 保持10个空闲连接
	sqlDB.SetConnMaxLifetime(5 * time.Minute) // 连接5分钟后重新创建
	sqlDB.SetConnMaxIdleTime(2 * time.Minute) // 空闲连接2分钟后关闭

	/*
		自动迁移
	*/
	err = migrate()
	if err != nil {
		panic(fmt.Errorf("failed to migrate: %w", err))
	}
}

// 取最大值
func MaxValue(table string) int {
	var max int
	M.Table(GetFullTable(table)).Select("MAX(sort_order)").Scan(&max)
	return max
}

// 转换数据库表名
func ConvertTableName(value string) string {
	values := []byte(value)
	var results string

	for i := 0; i < len(values); i++ {
		if 'A' <= values[i] && values[i] <= 'Z' {
			values[i] = values[i] - 'A' + 'a'
			if i != 0 {
				results += "_"
			}
		}
		results += string(values[i])
	}

	return results
}

// 取完整表名 带前辍
func GetFullTable(v string) string {
	return Prefix + ConvertTableName(v)
}

// 生成批量更新SQL
// table 表名
// data 必须包含id字段
func ParseUpdateSql(table string, data []map[string]interface{}) string {
	var sql = ""
	if strings.Contains(table, "_") {
		// 有下划线时认为已经是个全的表名
		sql = "UPDATE " + table + " SET \n"
	} else {
		sql = "UPDATE " + GetFullTable(table) + " SET \n"
	}
	var sqlMap = map[string]string{}
	var ids []uint64
	for _, val := range data {
		var id uint64 = 0
		for k, v := range val {
			if k == "id" {
				id = cast.ToUint64(v)
				ids = append(ids, id)

				break
			}
		}
		for k, v := range val {
			if k != "id" {
				if sqlMap[k] == "" {
					sqlMap[k] = k + " = CASE id \n"
				}
				sqlMap[k] += "WHEN " + cast.ToString(id) + " THEN '" + cast.ToString(v) + "' \n"
			}
		}
	}
	for _, v := range sqlMap {
		v += "END,\n"
		sql += v
	}
	bt := []rune(sql)
	end := len(bt) - 2
	sql = string(bt[0:end]) + "\n"
	sql += " WHERE id IN (" + cf.SliceUint64ToString(ids) + ")"
	return sql
}

func migrate() error {
	// 自动迁移生成表
	M.AutoMigrate(
		&structs.User{},
		&structs.UserSymbol{},
		&structs.Tick{},
		&structs.TickShadowLog{},
		&structs.OBSendStatistics{},
		&structs.OBReceiveStatistics{},
		&structs.TradeSendStatistics{},
		&structs.TradeReceiveStatistics{},
		&structs.UserShadow{},
		&structs.SysUser{},
		&structs.SysRole{},
		&structs.SysPermission{},
		&structs.SysUserRole{},
		&structs.SysRolePermission{},
		&structs.SysLoginLog{},
		&structs.SysOperationLog{},
		// 数据源延迟记录
		&structs.DataDelayRecord{},
		// 临时错误数据记录
		&structs.WorryDataRecord{},
		// K线偏移量控制(立即模式)
		&koi_model.KlineOffsetImmediateDB{},
		// K线偏移量控制(线性模式)
		&kol_model.KlineOffsetLinearDB{},
		// K线控制中心
		&kcc_model.KlineControlRecordDB{},
	)

	if err := seedSysRole(); err != nil {
		return err
	}
	if err := seedSysPermission(); err != nil {
		return err
	}
	if err := seedSysUser(); err != nil {
		return err
	}
	if err := seedOther(); err != nil {
		return err
	}

	return nil
}

func seedSysRole() error {
	var count int64
	M.Model(&structs.SysRole{}).Count(&count)
	if count > 0 {
		return nil
	}

	defaultSysRole := []structs.SysRole{
		{Name: "超级管理员", Description: "超级管理员"},
		{Name: "管理员", Description: "系统管理员"},
	}

	if err := M.Create(&defaultSysRole).Error; err != nil {
		return err
	}

	return nil
}

func seedSysPermission() error {
	var count int64
	M.Model(&structs.SysPermission{}).Count(&count)
	if count > 0 {
		return nil
	}

	defaultSysPermission := []structs.SysPermission{
		{NoTimeModel: structs.NoTimeModel{ID: 1}, Name: "系统管理", Resource: "", Type: "menu", ParentID: 0, SortOrder: 1},
		{NoTimeModel: structs.NoTimeModel{ID: 2}, Name: "系统用户列表", Resource: "system:user:get", Type: "menu", ParentID: 1, SortOrder: 2},
		{NoTimeModel: structs.NoTimeModel{ID: 3}, Name: "系统用户添加", Resource: "system:user:post", Type: "action", ParentID: 2, SortOrder: 3},
		{NoTimeModel: structs.NoTimeModel{ID: 4}, Name: "系统用户修改", Resource: "system:user:put", Type: "action", ParentID: 2, SortOrder: 4},
		{NoTimeModel: structs.NoTimeModel{ID: 5}, Name: "系统用户删除", Resource: "system:user:delete", Type: "action", ParentID: 2, SortOrder: 5},
		{NoTimeModel: structs.NoTimeModel{ID: 6}, Name: "系统用户更新状态", Resource: "system:user:status:put", Type: "action", ParentID: 2, SortOrder: 6},
		{NoTimeModel: structs.NoTimeModel{ID: 7}, Name: "系统用户重置GoogleSecret", Resource: "system:user:reset_gs:put", Type: "action", ParentID: 2, SortOrder: 7},
		{NoTimeModel: structs.NoTimeModel{ID: 8}, Name: "系统用户角色列表", Resource: "system:user:role_list:get", Type: "action", ParentID: 2, SortOrder: 8},
		{NoTimeModel: structs.NoTimeModel{ID: 9}, Name: "系统用户角色设置", Resource: "system:user:role_set:put", Type: "action", ParentID: 2, SortOrder: 9},
		{NoTimeModel: structs.NoTimeModel{ID: 10}, Name: "角色列表", Resource: "system:role:get", Type: "menu", ParentID: 1, SortOrder: 10},
		{NoTimeModel: structs.NoTimeModel{ID: 11}, Name: "角色添加", Resource: "system:role:post", Type: "action", ParentID: 10, SortOrder: 11},
		{NoTimeModel: structs.NoTimeModel{ID: 12}, Name: "角色编辑", Resource: "system:role:put", Type: "action", ParentID: 10, SortOrder: 12},
		{NoTimeModel: structs.NoTimeModel{ID: 13}, Name: "角色删除", Resource: "system:role:delete", Type: "action", ParentID: 10, SortOrder: 13},
		{NoTimeModel: structs.NoTimeModel{ID: 14}, Name: "角色权限列表", Resource: "system:role:perm_list:get", Type: "action", ParentID: 10, SortOrder: 14},
		{NoTimeModel: structs.NoTimeModel{ID: 15}, Name: "角色权限关联", Resource: "system:role:perm_set:put", Type: "action", ParentID: 10, SortOrder: 15},
		{NoTimeModel: structs.NoTimeModel{ID: 16}, Name: "权限列表", Resource: "system:perm:get", Type: "menu", ParentID: 1, SortOrder: 16},
		{NoTimeModel: structs.NoTimeModel{ID: 17}, Name: "权限添加", Resource: "system:perm:post", Type: "action", ParentID: 16, SortOrder: 17},
		{NoTimeModel: structs.NoTimeModel{ID: 18}, Name: "权限编辑", Resource: "system:perm:put", Type: "action", ParentID: 16, SortOrder: 18},
		{NoTimeModel: structs.NoTimeModel{ID: 19}, Name: "权限删除", Resource: "system:perm:delete", Type: "action", ParentID: 16, SortOrder: 19},
		{NoTimeModel: structs.NoTimeModel{ID: 20}, Name: "上传文件", Resource: "upload_file:post", Type: "action", ParentID: 0, SortOrder: 20},
		{NoTimeModel: structs.NoTimeModel{ID: 21}, Name: "上传图片", Resource: "upload_image:post", Type: "action", ParentID: 0, SortOrder: 21},
		{NoTimeModel: structs.NoTimeModel{ID: 22}, Name: "站点配置", Resource: "system:siteconfig:get", Type: "menu", ParentID: 1, SortOrder: 22},
		{NoTimeModel: structs.NoTimeModel{ID: 23}, Name: "站点配置更新", Resource: "system:siteconfig:put", Type: "action", ParentID: 22, SortOrder: 23},
	}
	if err := M.Create(&defaultSysPermission).Error; err != nil {
		return err
	}

	M.Model(&structs.SysRolePermission{}).Count(&count)
	if count > 0 {
		return nil
	}
	// 分配权限给角色
	if err := M.Model(&structs.SysRole{NoTimeModel: structs.NoTimeModel{ID: 1}}).Association("SysRolePermission").Append(&defaultSysPermission); err != nil {
		return err
	}

	return nil
}

func seedSysUser() error {
	var count int64
	M.Model(&structs.SysUser{}).Count(&count)
	if count > 0 {
		return nil
	}

	defaultSysUser := structs.SysUser{
		Username:     "superadmin",
		Password:     hashPasswordArgon2("123456q"),
		Email:        "<EMAIL>",
		GoogleSecret: "LAYSK75O37B3RJILX6WPQCSJ7CXYXYQL",
	}

	if err := M.Create(&defaultSysUser).Error; err != nil {
		return err
	}
	M.Model(&structs.SysUserRole{}).Count(&count)
	if count > 0 {
		return nil
	}
	// 分配权限给角色
	if err := M.Model(&defaultSysUser).Association("SysUserRole").Append(&structs.SysRole{NoTimeModel: structs.NoTimeModel{ID: 1}}); err != nil {
		return err
	}

	return nil
}

func seedOther() error {
	var count int64
	M.Model(&structs.Tick{}).Count(&count)
	if count < 1 {
		var defaultOtherr []structs.Tick
		defaultOtherr = append(defaultOtherr, structs.Tick{
			Symbolname:     "BTCUSD",
			Symbol:         "BTCUSDT",
			ReplaceSymbol:  "",
			Type:           "crypto",
			SubType:        "",
			Platform:       "alltick",
			Status:         1,
			InitDone:       0,
			InitTime:       0,
			Maximum:        0,
			Oncecount:      1000,
			DesignatedTime: 1722445200,
			TimeZone:       "UTC",
		})
		defaultOtherr = append(defaultOtherr, structs.Tick{
			Symbolname:     "ETHUSD",
			Symbol:         "ETHUSDT",
			ReplaceSymbol:  "",
			Type:           "crypto",
			SubType:        "",
			Platform:       "alltick",
			Status:         1,
			InitDone:       0,
			InitTime:       0,
			Maximum:        0,
			Oncecount:      1000,
			DesignatedTime: 1722445200,
			TimeZone:       "UTC",
		})
		defaultOtherr = append(defaultOtherr, structs.Tick{
			Symbolname:     "XAUUSD",
			Symbol:         "GOLD",
			ReplaceSymbol:  "",
			Type:           "commodity",
			SubType:        "",
			Platform:       "alltick",
			Status:         1,
			InitDone:       0,
			InitTime:       0,
			Maximum:        0,
			Oncecount:      1000,
			DesignatedTime: 1722445200,
			TimeZone:       "UTC",
		})
		defaultOtherr = append(defaultOtherr, structs.Tick{
			Symbolname:     "XAGUSD",
			Symbol:         "Silver",
			ReplaceSymbol:  "",
			Type:           "commodity",
			SubType:        "",
			Platform:       "alltick",
			Status:         1,
			InitDone:       0,
			InitTime:       0,
			Maximum:        0,
			Oncecount:      1000,
			DesignatedTime: 1722445200,
			TimeZone:       "UTC",
		})
		defaultOtherr = append(defaultOtherr, structs.Tick{
			Symbolname:     "APPLE",
			Symbol:         "AAPL.US",
			ReplaceSymbol:  "",
			Type:           "stock",
			SubType:        "",
			Platform:       "alltick",
			Status:         1,
			InitDone:       0,
			InitTime:       0,
			Maximum:        1000,
			Oncecount:      1000,
			DesignatedTime: 1722445200,
			TimeZone:       "UTC",
		})
		defaultOtherr = append(defaultOtherr, structs.Tick{
			Symbolname:     "MICROSOFT",
			Symbol:         "MSFT.US",
			ReplaceSymbol:  "",
			Type:           "stock",
			SubType:        "",
			Platform:       "alltick",
			Status:         1,
			InitDone:       0,
			InitTime:       0,
			Maximum:        1000,
			Oncecount:      1000,
			DesignatedTime: 1722445200,
			TimeZone:       "UTC",
		})
		defaultOtherr = append(defaultOtherr, structs.Tick{
			Symbolname:     "ALPHABET",
			Symbol:         "GOOGL.US",
			ReplaceSymbol:  "",
			Type:           "stock",
			SubType:        "",
			Platform:       "alltick",
			Status:         1,
			InitDone:       0,
			InitTime:       0,
			Maximum:        1000,
			Oncecount:      1000,
			DesignatedTime: 1722445200,
			TimeZone:       "UTC",
		})
		defaultOtherr = append(defaultOtherr, structs.Tick{
			Symbolname:     "TESLA",
			Symbol:         "TSLA.US",
			ReplaceSymbol:  "",
			Type:           "stock",
			SubType:        "",
			Platform:       "alltick",
			Status:         1,
			InitDone:       0,
			InitTime:       0,
			Maximum:        1000,
			Oncecount:      1000,
			DesignatedTime: 1722445200,
			TimeZone:       "UTC",
		})
		defaultOtherr = append(defaultOtherr, structs.Tick{
			Symbolname:     "NVDA",
			Symbol:         "NVDA.US",
			ReplaceSymbol:  "",
			Type:           "stock",
			SubType:        "",
			Platform:       "alltick",
			Status:         1,
			InitDone:       0,
			InitTime:       0,
			Maximum:        1000,
			Oncecount:      1000,
			DesignatedTime: 1722445200,
			TimeZone:       "UTC",
		})
		defaultOtherr = append(defaultOtherr, structs.Tick{
			Symbolname:     "EU50",
			Symbol:         "EUSTX50",
			ReplaceSymbol:  "",
			Type:           "stockindex",
			SubType:        "",
			Platform:       "alltick",
			Status:         1,
			InitDone:       0,
			InitTime:       0,
			Maximum:        0,
			Oncecount:      1000,
			DesignatedTime: 1722445200,
			TimeZone:       "UTC",
		})
		defaultOtherr = append(defaultOtherr, structs.Tick{
			Symbolname:     "FRA40",
			Symbol:         "FRA40",
			ReplaceSymbol:  "",
			Type:           "stockindex",
			SubType:        "",
			Platform:       "alltick",
			Status:         1,
			InitDone:       0,
			InitTime:       0,
			Maximum:        0,
			Oncecount:      1000,
			DesignatedTime: 1722445200,
			TimeZone:       "UTC",
		})
		defaultOtherr = append(defaultOtherr, structs.Tick{
			Symbolname:     "UK100",
			Symbol:         "UK100",
			ReplaceSymbol:  "",
			Type:           "stockindex",
			SubType:        "",
			Platform:       "alltick",
			Status:         1,
			InitDone:       0,
			InitTime:       0,
			Maximum:        0,
			Oncecount:      1000,
			DesignatedTime: 1722445200,
			TimeZone:       "UTC",
		})
		defaultOtherr = append(defaultOtherr, structs.Tick{
			Symbolname:     "JPN225",
			Symbol:         "JPN225",
			ReplaceSymbol:  "",
			Type:           "stockindex",
			SubType:        "",
			Platform:       "alltick",
			Status:         1,
			InitDone:       0,
			InitTime:       0,
			Maximum:        0,
			Oncecount:      1000,
			DesignatedTime: 1722445200,
			TimeZone:       "UTC",
		})
		defaultOtherr = append(defaultOtherr, structs.Tick{
			Symbolname:     "US30",
			Symbol:         "US30",
			ReplaceSymbol:  "",
			Type:           "stockindex",
			SubType:        "",
			Platform:       "alltick",
			Status:         1,
			InitDone:       0,
			InitTime:       0,
			Maximum:        0,
			Oncecount:      1000,
			DesignatedTime: 1722445200,
			TimeZone:       "UTC",
		})
		defaultOtherr = append(defaultOtherr, structs.Tick{
			Symbolname:     "AUS200",
			Symbol:         "AUS200",
			ReplaceSymbol:  "",
			Type:           "stockindex",
			SubType:        "",
			Platform:       "alltick",
			Status:         1,
			InitDone:       0,
			InitTime:       0,
			Maximum:        0,
			Oncecount:      1000,
			DesignatedTime: 1722445200,
			TimeZone:       "UTC",
		})
		defaultOtherr = append(defaultOtherr, structs.Tick{
			Symbolname:     "NAS100",
			Symbol:         "NAS100",
			ReplaceSymbol:  "",
			Type:           "stockindex",
			SubType:        "",
			Platform:       "alltick",
			Status:         1,
			InitDone:       0,
			InitTime:       0,
			Maximum:        0,
			Oncecount:      1000,
			DesignatedTime: 1722445200,
			TimeZone:       "UTC",
		})
		defaultOtherr = append(defaultOtherr, structs.Tick{
			Symbolname:     "HK50",
			Symbol:         "HK50",
			ReplaceSymbol:  "",
			Type:           "stockindex",
			SubType:        "",
			Platform:       "alltick",
			Status:         1,
			InitDone:       0,
			InitTime:       0,
			Maximum:        0,
			Oncecount:      1000,
			DesignatedTime: 1722445200,
			TimeZone:       "UTC",
		})
		defaultOtherr = append(defaultOtherr, structs.Tick{
			Symbolname:     "SP500",
			Symbol:         "US500",
			ReplaceSymbol:  "",
			Type:           "stockindex",
			SubType:        "",
			Platform:       "alltick",
			Status:         1,
			InitDone:       0,
			InitTime:       0,
			Maximum:        0,
			Oncecount:      1000,
			DesignatedTime: 1722445200,
			TimeZone:       "UTC",
		})
		if err := M.Create(&defaultOtherr).Error; err != nil {
			return err
		}
	}

	M.Model(&structs.User{}).Count(&count)
	if count < 1 {
		var defaultOtherr []structs.User
		defaultOtherr = append(defaultOtherr, structs.User{
			Username: "user1",
			Password: "123123",
			Token:    "e5c6ce77c8132e8ace60e456c6d33471",
			Status:   1,
			Remark:   "sigma",
		})
		defaultOtherr = append(defaultOtherr, structs.User{
			Username: "user2",
			Password: "123123",
			Token:    "314c465cf7225bdf8549f3e6e474f874",
			Status:   1,
			Remark:   "premier",
		})
		defaultOtherr = append(defaultOtherr, structs.User{
			Username: "user3",
			Password: "123123",
			Token:    "499f7ef850ddbf0e7a3f9b501a5732ff",
			Status:   1,
			Remark:   "teda",
		})
		defaultOtherr = append(defaultOtherr, structs.User{
			Username: "user4",
			Password: "123123",
			Token:    "bc8b52d080e043da001249a2d0ae48bd",
			Status:   1,
			Remark:   "exchange",
		})
		defaultOtherr = append(defaultOtherr, structs.User{
			Username: "user5",
			Password: "123123",
			Token:    "645caaf08b8e393ff724a34a4481fb73",
			Status:   1,
			Remark:   "",
		})
		defaultOtherr = append(defaultOtherr, structs.User{
			Username: "user6",
			Password: "123123",
			Token:    "9e98aff119db652c3b2c6f6a027f98df",
			Status:   1,
			Remark:   "",
		})
		defaultOtherr = append(defaultOtherr, structs.User{
			Username: "user7",
			Password: "123123",
			Token:    "c0c4746db014d3e05b2caf3f49fa540b",
			Status:   1,
			Remark:   "",
		})
		defaultOtherr = append(defaultOtherr, structs.User{
			Username: "user8",
			Password: "123123",
			Token:    "0f08404eb9215a64a018d188cf4e123c",
			Status:   1,
			Remark:   "",
		})
		defaultOtherr = append(defaultOtherr, structs.User{
			Username: "user9",
			Password: "123123",
			Token:    "56ed49e023a8e02cd4fa291d93c5e323",
			Status:   1,
			Remark:   "",
		})
		if err := M.Create(&defaultOtherr).Error; err != nil {
			return err
		}
	}
	return nil
}

// Argon2 加密密码
func hashPasswordArgon2(password string) string {
	// 生成密码哈希
	hash := argon2.IDKey([]byte(password), []byte(global.Salt), 1, 64*1024, 4, 32)
	return hex.EncodeToString(hash)
}
