package google_totp

import (
	"github.com/pquerna/otp/totp"
)

// GenerateKey 生成一个用于 Google Authenticator 的密钥
func GenerateKey(user string, issuer string) (string, error) {
	// 生成密钥
	key, err := totp.Generate(totp.GenerateOpts{
		Issuer:      issuer, // 服务名称
		AccountName: user,   // 用户账号名称
	})
	if err != nil {
		return "", err
	}

	// 返回密钥
	return key.Secret(), nil
}

// ValidateCode 验证用户输入的动态验证码是否正确
func ValidateCode(secret string, code string) bool {
	// 使用密钥和当前时间验证验证码
	valid := totp.Validate(code, secret)
	return valid
}
