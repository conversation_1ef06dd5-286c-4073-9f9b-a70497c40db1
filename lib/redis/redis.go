package redis

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"os"
	"strconv"
	"time"
	"trading_tick_server/lib/decouplingfunction"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/mysql"
	"trading_tick_server/lib/structs"

	goredislib "github.com/redis/go-redis/v9"
	"github.com/sknun/cf/cast"
	"go.uber.org/zap"
)

var Ctx = context.Background()
var RDB *goredislib.Client
var RDBPrefix string
var defaultExpireTime int = 60

/*
主站和客户端共用一个redis
*/
func Run() {
	RDBPrefix = global.Yaml.Redis.Prefix
	defaultExpireTime = global.Yaml.Redis.DefaultExpireTime
	_ = defaultExpireTime
	RDB = goredislib.NewClient(&goredislib.Options{
		Addr:     global.Yaml.Redis.Host + ":" + strconv.Itoa(global.Yaml.Redis.Port),
		Username: global.Yaml.Redis.Username,
		Password: global.Yaml.Redis.Password, // no password set
		DB:       0,                          // use default DB
	})
	_, err := RDB.Ping(Ctx).Result()
	if err != nil {
		global.Lg.Error(
			"Failed to connect to Redis",
			zap.String("Host", global.Yaml.Redis.Host),
			zap.Int("Port", global.Yaml.Redis.Port),
			zap.String("component", "Redis"),
			zap.Error(err),
		)
		os.Exit(-1)
	}
}

func SetString(key string, value interface{}, expiration time.Duration) error {
	if RDB == nil {
		err := errors.New("redis client is not initialized")
		global.Lg.Error(
			"Failed to get value from Redis - client not initialized",
			zap.String("Host", global.Yaml.Redis.Host),
			zap.Int("Port", global.Yaml.Redis.Port),
			zap.String("component", "Redis"),
			zap.Error(err),
		)
		return err
	}
	key = RDBPrefix + key
	_, err := RDB.Set(Ctx, key, value, expiration).Result()
	if err != nil {
		global.Lg.Error(
			"Failed to set value in Redis",
			zap.String("key", key),
			zap.Any("value", value),
			zap.String("component", "Redis"),
			zap.Error(err),
		)
	}
	return err
}

func GetString(key string) (string, error) {
	if RDB == nil {
		err := errors.New("redis client is not initialized")
		global.Lg.Error(
			"Failed to get value from Redis - client not initialized",
			zap.String("Host", global.Yaml.Redis.Host),
			zap.Int("Port", global.Yaml.Redis.Port),
			zap.String("component", "Redis"),
			zap.Error(err),
		)
		return "", err
	}
	key = RDBPrefix + key
	val, err := RDB.Get(Ctx, key).Result()
	if err != nil {
		// 记录错误信息以及关键信息
		if err != goredislib.Nil {
			global.Lg.Error(
				"Failed to get value from Redis",
				zap.String("key", key),
				zap.String("component", "Redis"),
				zap.Error(err),
			)
		}
		return "", err
	}
	return val, err
}

func DelString(key string) error {
	if RDB == nil {
		err := errors.New("redis client is not initialized")
		global.Lg.Error(
			"Failed to get value from Redis - client not initialized",
			zap.String("Host", global.Yaml.Redis.Host),
			zap.Int("Port", global.Yaml.Redis.Port),
			zap.String("component", "Redis"),
			zap.Error(err),
		)
		return err
	}
	key = RDBPrefix + key
	err := RDB.Del(Ctx, key).Err()
	if err != nil {
		global.Lg.Error(
			"Failed to delete key from Redis",
			zap.String("key", key),
			zap.String("component", "Redis"),
			zap.Error(err),
		)
		return err
	}
	return nil
}

/*
删除指定前缀的缓存
传入指定前缀 不需要带入*
*/
func DelAllString(key string) error {
	if RDB == nil {
		err := errors.New("redis client is not initialized")
		global.Lg.Error(
			"Failed to get value from Redis - client not initialized",
			zap.String("Host", global.Yaml.Redis.Host),
			zap.Int("Port", global.Yaml.Redis.Port),
			zap.String("component", "Redis"),
			zap.Error(err),
		)
		return err
	}
	key = RDBPrefix + key + "*"

	var cursor uint64
	count := int64(1000)

	for {
		keys, newCursor, err := RDB.Scan(Ctx, cursor, key, count).Result()
		if err != nil {
			global.Lg.Error(
				"Failed to scanning Redis",
				zap.String("key", key),
				zap.String("component", "Redis"),
				zap.Error(err),
			)
			return err
		}

		for _, v := range keys {
			err := RDB.Del(Ctx, v).Err()
			if err != nil {
				global.Lg.Error(
					"Failed to all delete key from Redis",
					zap.String("key", key),
					zap.String("component", "Redis"),
					zap.Error(err),
				)
				return err
			}
		}

		cursor = newCursor

		if cursor == 0 {
			break
		}
	}
	return nil
}

/*
按时间存储数据
userID 用户ID
platform 平台
symbol 产品
timeZone 时区
interval 时间间隔
value K线的json数据 解开是structs.KlineDataRet
*/
func KlineShadowStoreData(userID, platform, symbol, timeZone, interval string, timestamp int64, value string) {
	table := decouplingfunction.GetRedisTable(userID, platform, symbol, timeZone, interval)
	err := RDB.ZAdd(Ctx, table, goredislib.Z{
		Score:  float64(timestamp),
		Member: timestamp,
	}).Err()
	if err != nil {
		global.Lg.Error(
			"redis存储k线数据失败",
			zap.String("key", table),
			zap.String("value", value),
			zap.Int64("timestamp", timestamp),
			zap.String("component", "Redis"),
			zap.Error(err),
		)
		fmt.Printf("❌reidis存储k线数据失败 err:%v\n", err)
	}

	// 实际存数据到influxdb
	// influxdb.WriteKlineShadowData(userID, platform, symbol, timeZone, interval, timestamp, value)
	// 还是存redis
	err = RDB.Set(Ctx, table+":"+cast.ToString(timestamp), value, 0).Err()
	if err != nil {
		fmt.Printf("❌redis存储k线数据失败,UserID, Platform, symbol, err:%v\n", userID, platform, symbol, err)
	}
}

/*
按时间范围查询数据
userID 用户ID
platform 平台
symbol 产品
timeZone 时区
interval 时间间隔
*/
func KlineShadowQueryData(userID, platform, symbol, timeZone, interval string, start, end int64) []structs.KlineDataShadow {
	var shadow []structs.KlineDataShadow
	table := decouplingfunction.GetRedisTable(userID, platform, symbol, timeZone, interval)
	results, err := RDB.ZRevRangeByScore(Ctx, table, &goredislib.ZRangeBy{
		Min: fmt.Sprintf("%d", start),
		Max: fmt.Sprintf("%d", end),
	}).Result()
	if err != nil {
		global.Lg.Error(
			"redis查询k线数据失败",
			zap.String("key", table),
			zap.Int64("start", start),
			zap.Int64("end", end),
			zap.String("component", "Redis"),
			zap.Error(err),
		)
		return shadow
	}
	//global.Lg.Info("KlineShadowQueryData",
	//	zap.String("table", table),
	//	zap.Int64("start", start),
	//	zap.Int64("end", end),
	//	zap.Any("results", results))
	// 如果有k线就查询数据
	for _, v := range results {
		val, err := RDB.Get(Ctx, table+":"+v).Result()
		if err == nil {
			var tmp structs.KlineDataShadow
			if err = json.Unmarshal([]byte(val), &tmp); err == nil {
				shadow = append(shadow, tmp)
			} else {
				fmt.Printf("redis反序列化失败 err:%v\n", err)
			}
		}
	}
	return shadow
}

/*
按时间范围删除数据
userID 用户ID
platform 平台
symbol 产品
timeZone 时区
*/
func KlineShadowDeleteData(id uint, userID, platform, symbol, timeZone string, start, end int64) {
	// 获取所有时间颗粒
	allResolutions := decouplingfunction.GetAllResolutions()
	var table string
	var newStart, newEnd int64
	for _, v := range allResolutions {
		table = decouplingfunction.GetRedisTable(userID, platform, symbol, timeZone, v)
		newStart = decouplingfunction.CurrentKlineTimestamp(start, timeZone, v)
		newEnd = decouplingfunction.CurrentKlineTimestamp(end, timeZone, v)
		results, err := RDB.ZRangeByScore(Ctx, table, &goredislib.ZRangeBy{
			Min: fmt.Sprintf("%d", newStart),
			Max: fmt.Sprintf("%d", newEnd),
		}).Result()
		if err == nil && len(results) > 0 {
			for _, v2 := range results {
				_ = RDB.Del(Ctx, table+":"+v2).Err()
			}
			err = RDB.ZRemRangeByScore(Ctx, table, fmt.Sprintf("%d", newStart), fmt.Sprintf("%d", newEnd)).Err()
			if err != nil {
				global.Lg.Error(
					"redis删除k线数据失败",
					zap.String("key", table),
					zap.Int64("start", newStart),
					zap.Int64("end", newEnd),
					zap.String("component", "Redis"),
					zap.Error(err),
				)
			}
		} else {
			global.Lg.Error(
				"redis ZRangeByScore删除k线数据失败",
				zap.String("key", table),
				zap.Int64("start", newStart),
				zap.Int64("end", newEnd),
				zap.String("component", "Redis"),
				zap.Error(err),
			)
		}
		// 查询TickLog里面的KlineTime是否等于newStart并且ShadowID不等于param.ID 如果有就把收盘价格换成真实 然后再保存数据
		var tickLog, tickLog2 structs.TickShadowLog
		mysql.M.Where("kline_time=? AND shadow_id!=? AND resolutions=?", newStart, id, v).Order("kline_id DESC").First(&tickLog)
		if tickLog.ID > 0 {
			if tickLog.UseOpen && tickLog.UseClose {
				// 先保留
			} else {
				b, _ := json.Marshal(structs.KlineDataShadow{
					Time:      tickLog.KlineTime,
					Open:      cast.ToFloat64(tickLog.Open),
					Close:     cast.ToFloat64(tickLog.OrigClose),
					High:      cast.ToFloat64(tickLog.High),
					Low:       cast.ToFloat64(tickLog.Low),
					Volume:    cast.ToFloat64(tickLog.Volume),
					Turnover:  cast.ToFloat64(tickLog.Turnover),
					UseOpen:   false,
					UseClose:  true,
					Direction: tickLog.Direction, // 偏移方向
				})
				KlineShadowStoreData(userID, platform, symbol, timeZone, v, start, string(b))
				log.Println("有开始数据", tickLog)
			}

		}
		// 查询TickLog里面的KlineTime是否等于newEnd并且ShadowID不等于param.ID 如果有就把开盘价格换成真实 然后再保存数据
		mysql.M.Where("kline_time=? AND shadow_id!=? AND resolutions=?", newEnd, id, v).Order("kline_id ASC").First(&tickLog2)
		if tickLog2.ID > 0 {
			if tickLog2.UseOpen && tickLog2.UseClose {
				// 先保留
			} else {
				b, _ := json.Marshal(structs.KlineDataShadow{
					Time:      tickLog2.KlineTime,
					Open:      cast.ToFloat64(tickLog2.OrigOpen),
					Close:     cast.ToFloat64(tickLog2.Close),
					High:      cast.ToFloat64(tickLog2.High),
					Low:       cast.ToFloat64(tickLog2.Low),
					Volume:    cast.ToFloat64(tickLog2.Volume),
					Turnover:  cast.ToFloat64(tickLog2.Turnover),
					UseOpen:   true,
					UseClose:  false,
					Direction: tickLog2.Direction, // 偏移方向
				})
				KlineShadowStoreData(userID, platform, symbol, timeZone, v, start, string(b))
				log.Println("有结束数据", tickLog)
			}
		}
	}
}

/*
删除整个有序集合
userID 用户ID
platform 平台
symbol 产品
timeZone 时区
*/
func KlineShadowDeleteAll(userID, platform, symbol, timeZone string) {
	tables := decouplingfunction.GetAllRedisTable(userID, platform, symbol, timeZone)
	for _, v := range tables {
		results, err := RDB.ZRangeByScore(Ctx, v, &goredislib.ZRangeBy{
			Min: "-inf",
			Max: "+inf",
		}).Result()
		if err == nil && len(results) > 0 {
			for _, vResults := range results {
				RDB.Del(Ctx, v+vResults)
			}
		}

		pattern := v + ":*"
		var cursor uint64
		var batchSize int64 = 500
		for {
			keys, newCursor, err := RDB.Scan(Ctx, cursor, pattern, batchSize).Result()
			if err != nil {
				global.Lg.Error(
					"Scan error",
					zap.String("key", v),
					zap.String("component", "Redis"),
					zap.Error(err),
				)
				return
			}

			if len(keys) > 0 {
				pipe := RDB.Pipeline()
				for _, key := range keys {
					pipe.Del(Ctx, key)
				}
				_, err := pipe.Exec(Ctx)
				if err != nil {
					global.Lg.Error(
						"Pipeline Exec error",
						zap.String("key", v),
						zap.String("component", "Redis"),
						zap.Error(err),
					)
					return
				}
			}
			cursor = newCursor
			if cursor == 0 {
				break
			}
		}
		err = RDB.ZRemRangeByScore(Ctx, v, "-inf", "+inf").Err()
		if err != nil {
			global.Lg.Error(
				"redis删除全部k线数据失败",
				zap.String("key", v),
				zap.String("component", "Redis"),
				zap.Error(err),
			)
		}
	}
}
