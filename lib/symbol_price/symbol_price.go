package symbol_price

/*
最新价格保存和读取
*/
import (
	"sync"
)

// alltick平台
var allTick *symbolPrice

type symbolPrice struct {
	mu sync.Mutex
	m  map[string]float64 // 这个需改为安全 map, 不用手动锁
}

func InitSymbolPrice() {
	allTick = &symbolPrice{
		m: make(map[string]float64),
	}
}

func Set(platform, symbol string, value float64) {
	switch platform {
	case "alltick":
		allTick.mu.Lock()
		allTick.m[symbol] = value
		allTick.mu.Unlock()
	}
}

func Get(platform, symbol string) float64 {
	switch platform {
	case "alltick":
		allTick.mu.Lock()
		defer allTick.mu.Unlock()
		return allTick.m[symbol]
	}
	return 0
}
