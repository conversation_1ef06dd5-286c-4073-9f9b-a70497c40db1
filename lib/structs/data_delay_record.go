package structs

type DataDelayRecord struct {
	MyModel
	// 数据源名称(数据源平台名称)
	DataSourceName string `gorm:"index" mapstructure:"data_source_name" json:"data_source_name"`
	// 产品名称
	ProductName string `gorm:"index" mapstructure:"product_name" json:"product_name"`
	// 插针ID(控盘ID)
	ControlID string `gorm:"index" mapstructure:"control_id" json:"control_id"`
	// 插针开始时间(时间戳)秒
	StartTime int64 `gorm:"index" mapstructure:"start_time" json:"start_time"`
	// 收到的数据的时间(时间戳)秒
	ReceiveTime int64 `gorm:"index" mapstructure:"receive_time" json:"receive_time"`
	// 数据源延迟时间(秒)
	DelayTime float64 `gorm:"index" mapstructure:"delay_time" json:"delay_time"`
}
