package structs

import (
	"encoding/json"

	"github.com/shopspring/decimal"
	"gorm.io/datatypes"
)

// 给 json.RawMessage 起个本地名字
type LocalRawMessage = json.RawMessage

// 管理用户表
type SysUser struct {
	MyModel
	Username     string    `gorm:"uniqueIndex;type:varchar(100);not null;default:'';" json:"username"` // 账号
	Password     string    `gorm:"type:varchar(100);not null;default:'';" json:"-"`                    // 密码
	Email        string    `gorm:"uniqueIndex;type:varchar(60);not null;default:'';" json:"email"`     // 邮箱
	Phone        string    `gorm:"type:varchar(60);not null;default:'';" json:"phone"`                 // 手机号
	Token        string    `gorm:"type:varchar(255);not null;default:'';" json:"token"`                // token
	GoogleSecret string    `gorm:"type:varchar(50);not null;default:'';" json:"google_secret"`         // GoogleSecret
	Status       uint8     `gorm:"index;not null;default:1;" json:"status"`                            // 状态 0禁用 1启用
	SysUserRole  []SysRole `gorm:"many2many:sys_user_role;" json:"sys_user_role"`                      // 用户和角色多对多关系
}

// 管理角色表
type SysRole struct {
	NoTimeModel
	Name              string          `gorm:"uniqueIndex;type:varchar(100);not null;default:'';" json:"name"`
	Description       string          `gorm:"type:varchar(200);not null;default:'';" json:"description"`
	SortOrder         int             `gorm:"type:int;not null;default:0;" json:"sort_order"`            // 排序字段
	SysRolePermission []SysPermission `gorm:"many2many:sys_role_permission;" json:"sys_role_permission"` // 角色和权限多对多关系
}

// 权限表
type SysPermission struct {
	NoTimeModel
	Name        string `gorm:"type:varchar(100);not null;default:'';" json:"name"`
	Resource    string `gorm:"type:varchar(100);not null;default:'';" json:"resource"`
	Type        string `gorm:"type:varchar(20);not null;default:'';" json:"type"` // menu 或 action
	ParentID    uint   `gorm:"not null;default:0;" json:"parent_id"`              // 父级权限ID，0表示顶级
	SortOrder   int    `gorm:"type:int;not null;default:0;" json:"sort_order"`    // 排序字段
	Description string `gorm:"type:varchar(200);not null;default:'';" json:"description"`
}

// 用户与角色关联表
type SysUserRole struct {
	SysUserID uint `gorm:"primaryKey" json:"user_id"`
	SysRoleID uint `gorm:"primaryKey" json:"role_id"`
}

// 角色与权限关联表
type SysRolePermission struct {
	SysRoleID       uint `gorm:"primaryKey" json:"role_id"`
	SysPermissionID uint `gorm:"primaryKey" json:"permission_id"`
}

type SysLoginLog struct {
	MyModel
	UserId     uint   `gorm:"not null;default:0;" json:"user_id"`                        // 用户ID
	LoginToken string `gorm:"type:varchar(200);not null;default:'';" json:"login_token"` // 登录时的token
	LoginIp    string `gorm:"type:varchar(32);not null;default:'';"`                     // 登录IP
}

type SysOperationLog struct {
	MyModel
	Address   string `gorm:"type:varchar(200);not null;default:'';" json:"address"` // 地址
	UserId    uint   `gorm:"not null;default:0;" json:"user_id"`                    // 用户ID
	Path      string `gorm:"column:path" json:"path"`                               // 操作路径
	IP        string `gorm:"column:ip" json:"ip"`                                   // IP 地址
	Operation string `gorm:"column:operation" json:"operation"`                     // 操作
}

// PermissionTree 用于返回前端的权限数据结构
type SysPermissionTree struct {
	ID       uint                `json:"id"`
	Name     string              `json:"name"`
	Key      string              `json:"key"`
	Type     string              `json:"type"`
	Children []SysPermissionTree `json:"children"`
}

/*
站点配置表
Input Textarea Radio Checkbox Select ImageUpload
*/
type SiteConfig struct {
	ID          uint64 `gorm:"primaryKey;AUTO_INCREMENT;" json:"id"`
	Title       string `gorm:"type:varchar(100);not null;default:'';" json:"title"`        // 标题
	ConfigName  string `gorm:"type:varchar(100);not null;default:'';" json:"config_name"`  // 配置名称
	ConfigValue string `gorm:"type:varchar(500);not null;default:'';" json:"config_value"` // 配置值
	GroupName   string `gorm:"type:varchar(50);not null;default:'';" json:"group_name"`    // 分组名称
	ConfigType  string `gorm:"type:varchar(20);not null;default:'';" json:"config_type"`   // 类型 Input Textarea Radio Checkbox Checkboxs Select ImageUpload
	UseState    uint8  `gorm:"not null;default:0;" json:"-"`                               // 0不显示、禁用 1显示、正常
	SortOrder   int    `gorm:"not null;default:0;" json:"-"`                               // 排序
	ValueScope  string `gorm:"type:varchar(100);not null;default:'';" json:"value_scope"`  // 选择参数
	TitleScope  string `gorm:"type:varchar(100);not null;default:'';" json:"title_scope"`  // 选择参数名称
	ConfigDesc  string `gorm:"type:varchar(600);not null;default:'';" json:"config_desc"`  // 描述
}

// 用于返回前端的站点配置结构
type SiteConfigGroup struct {
	// 分组名称
	GroupName string `json:"group_name"`
	// 参数
	Configs []SiteConfigTree `json:"configs"`
}

type SiteConfigTree struct {
	ID uint64 `json:"id"`
	// 标题
	Title string `json:"title"`
	// 配置名称
	ConfigName string `json:"config_name"`
	// 配置值
	ConfigValue string `json:"config_value"`
	// 类型 Input Textarea Radio Checkbox 复选框 Checkboxs 多选框 Select ImageUpload
	ConfigType string `json:"config_type"`
	// 选择参数
	ValueScope string `json:"value_scope"`
	// 选择参数名称
	TitleScope string `json:"title_scope"`
	// 描述
	ConfigDesc string `json:"config_desc"`
}

// 用户表
/*
[{"platform":"alltick","symbol":"BTCUSDT","type":"kline"},{"platform":"alltick","symbol":"BTCUSDT","type":"orderbook1"},{"platform":"alltick","symbol":"DOGEUSDT","type":"kline"},{"platform":"alltick","symbol":"DOGEUSDT","type":"orderbook1"},{"platform":"alltick","symbol":"ADAUSDT","type":"kline"},{"platform":"alltick","symbol":"ADAUSDT","type":"orderbook1"},{"platform":"alltick","symbol":"AVAXUSDT","type":"kline"},{"platform":"alltick","symbol":"AVAXUSDT","type":"orderbook1"},{"platform":"alltick","symbol":"GOLD","type":"kline"},{"platform":"alltick","symbol":"GOLD","type":"orderbook1"},{"platform":"alltick","symbol":"Silver","type":"kline"},{"platform":"alltick","symbol":"Silver","type":"orderbook1"},{"platform":"alltick","symbol":"Aluminum","type":"kline"},{"platform":"alltick","symbol":"Aluminum","type":"orderbook1"},{"platform":"alltick","symbol":"COPPER","type":"kline"},{"platform":"alltick","symbol":"COPPER","type":"orderbook1"},{"platform":"alltick","symbol":"AAPL.US","type":"kline"},{"platform":"alltick","symbol":"AAPL.US","type":"orderbook1"},{"platform":"alltick","symbol":"MSFT.US","type":"kline"},{"platform":"alltick","symbol":"MSFT.US","type":"orderbook1"},{"platform":"alltick","symbol":"GOOG.US","type":"kline"},{"platform":"alltick","symbol":"GOOG.US","type":"orderbook1"},{"platform":"alltick","symbol":"USDJPY","type":"kline"},{"platform":"alltick","symbol":"USDJPY","type":"orderbook1"},{"platform":"alltick","symbol":"USDCAD","type":"kline"},{"platform":"alltick","symbol":"USDCAD","type":"orderbook1"},{"platform":"alltick","symbol":"USDCHF","type":"kline"},{"platform":"alltick","symbol":"USDCHF","type":"orderbook1"},{"platform":"alltick","symbol":"USDCNH","type":"kline"},{"platform":"alltick","symbol":"USDCNH","type":"orderbook1"},{"platform":"alltick","symbol":"UKOIL","type":"kline"},{"platform":"alltick","symbol":"UKOIL","type":"orderbook1"},{"platform":"alltick","symbol":"USOIL","type":"kline"},{"platform":"alltick","symbol":"USOIL","type":"orderbook1"},{"platform":"alltick","symbol":"NGAS","type":"kline"},{"platform":"alltick","symbol":"NGAS","type":"orderbook1"}]
*/
type User struct {
	MyModel
	Username               string       `gorm:"uniqueIndex;type:varchar(100);not null;default:'';" json:"username"`                    // 账号
	Password               string       `gorm:"type:varchar(100);not null;default:'';" json:"password"`                                // 密码
	Token                  string       `gorm:"type:varchar(255);not null;default:'';" json:"token"`                                   // token
	Status                 uint8        `gorm:"index;not null;default:1;" json:"status"`                                               // 状态 0禁用 1启用
	KlineQueryNumber1m     int64        `gorm:"column:kline_query_number_1m;not null;default:0;" json:"kline_query_number_1m"`         // k线可查询最多数量
	KlineQueryNumber5m     int64        `gorm:"column:kline_query_number_5m;not null;default:0;" json:"kline_query_number_5m"`         // k线可查询最多数量
	KlineQueryNumber15m    int64        `gorm:"column:kline_query_number_15m;not null;default:0;" json:"kline_query_number_15m"`       // k线可查询最多数量
	KlineQueryNumber30m    int64        `gorm:"column:kline_query_number_30m;not null;default:0;" json:"kline_query_number_30m"`       // k线可查询最多数量
	KlineQueryNumber1hour  int64        `gorm:"column:kline_query_number_1hour;not null;default:0;" json:"kline_query_number_1hour"`   // k线可查询最多数量
	KlineQueryNumber2hour  int64        `gorm:"column:kline_query_number_2hour;not null;default:0;" json:"kline_query_number_2hour"`   // k线可查询最多数量
	KlineQueryNumber4hour  int64        `gorm:"column:kline_query_number_4hour;not null;default:0;" json:"kline_query_number_4hour"`   // k线可查询最多数量
	KlineQueryNumber1day   int64        `gorm:"column:kline_query_number_1day;not null;default:0;" json:"kline_query_number_1day"`     // k线可查询最多数量
	KlineQueryNumber1week  int64        `gorm:"column:kline_query_number_1week;not null;default:0;" json:"kline_query_number_1week"`   // k线可查询最多数量
	KlineQueryNumber1month int64        `gorm:"column:kline_query_number_1month;not null;default:0;" json:"kline_query_number_1month"` // k线可查询最多数量
	Remark                 string       `gorm:"type:varchar(100);not null;default:'';" json:"remark"`                                  // 备注
	UserSymbols            []UserSymbol `gorm:"foreignKey:UserID" json:"user_symbols"`
	StartDuration          int32        `json:"start_duration" gorm:"column:start_duration;default:60;comment:开始控盘偏移时间(秒)"`
	EndDuration            int32        `json:"end_duration" gorm:"column:end_duration;default:60;comment:结束控盘偏移时间(秒)"`
	Symbols                []uint       `gorm:"-" json:"symbols"` // 用户权限(可获取的数据资源ID)
}

/*
用户可用的产品表
Type kline 获取k线 orderbook 盘口
*/
type UserSymbol struct {
	UserID        uint    `gorm:"index;not null;default:0;" json:"user_id"`                     // 用户ID
	SymbolID      uint    `gorm:"index;not null;default:0;" json:"symbol_id"`                   // 产品ID
	Type          string  `gorm:"type:varchar(30);not null;default:'';" json:"type"`            // 类型
	Platform      string  `gorm:"type:varchar(30);not null;default:'';" json:"platform"`        // 产品平台
	Symbol        string  `gorm:"type:varchar(30);not null;default:'';" json:"symbol"`          // 产品标识
	DecimalPlaces uint    `gorm:"not null;default:0;" json:"decimal_places"`                    // 小数位数
	FloatRange    float64 `gorm:"type:decimal(20,10);not null;default:0.0;" json:"float_range"` // 浮动绝对值范围，默认0
}

// 用户表
type UserCache struct {
	ID          uint
	Username    string           // 账号
	Password    string           // 密码
	Thumbprint  string           // 指纹
	Token       string           // token
	KlineSymbol map[string]int32 // kline
	ObSymbol    map[string]int32 // 盘口数据
	UserSymbols []UserSymbol     // 用户可用的产品表
}

type UserShadow struct {
	MyModel
	UserID          uint   `gorm:"not null;default:0;" json:"user_id"`                           // 用户ID
	Platform        string `gorm:"type:varchar(30);not null;default:'';" json:"-"`               // 产品平台
	Symbol          string `gorm:"type:varchar(30);not null;default:'';" json:"symbol"`          // 产品标识
	Type            string `gorm:"type:varchar(30);not null;default:'';" json:"type"`            // 插针类型 intuitive fluctuation
	StartTime       int64  `gorm:"not null;default:0;" json:"start_time"`                        // 插针开始时间
	EndTime         int64  `gorm:"not null;default:0;" json:"end_time"`                          // 插针结束时间
	ShadowStartTime int64  `gorm:"not null;default:0;" json:"shadow_start_time"`                 // 1m插针开始时间线
	ShadowEndTime   int64  `gorm:"not null;default:0;" json:"shadow_end_time"`                   // 1m插针结束时间线
	RiseTime        int    `gorm:"not null;default:0;" json:"rise_time"`                         // 爬坡阶段时间（秒）
	PeakTime        int    `gorm:"not null;default:0;" json:"peak_time"`                         // 峰值阶段时间（秒）
	FallTime        int    `gorm:"not null;default:0;" json:"fall_time"`                         // 回落阶段时间（秒）
	SpikeFactor     string `gorm:"type:varchar(30);not null;default:0;" json:"spike_factor"`     // 插针幅度
	Status          uint8  `gorm:"index;not null;default:0;" json:"status"`                      // 状态 0禁用 1启用
	Candlestick     string `gorm:"type:varchar(30);not null;default:0;" json:"candlestick"`      // 实时k线的close值
	AsksPriceMin    string `gorm:"type:varchar(30);not null;default:0;" json:"asks_price_min"`   // 买跌浮动值
	AsksPriceMax    string `gorm:"type:varchar(30);not null;default:0;" json:"asks_price_max"`   // 买跌浮动值
	RiseHitMin      string `gorm:"type:varchar(30);not null;default:0;" json:"rise_hit_min"`     // 爬坡期的命中最小值
	RiseHitMax      string `gorm:"type:varchar(30);not null;default:0;" json:"rise_hit_max"`     // 爬坡期的命中最大值
	PeakHitMin      string `gorm:"type:varchar(30);not null;default:0;" json:"peak_hit_min"`     // 峰值期的命中最小值
	PeakHitMax      string `gorm:"type:varchar(30);not null;default:0;" json:"peak_hit_max"`     // 峰值期的命中最大值
	PeakFloat       string `gorm:"type:varchar(30);not null;default:0;" json:"peak_float"`       // 峰值期的浮动值
	FallHitMin      string `gorm:"type:varchar(30);not null;default:0;" json:"fall_hit_min"`     // 回落期的命中最小值
	FallHitMax      string `gorm:"type:varchar(30);not null;default:0;" json:"fall_hit_max"`     // 回落期的命中最大值
	RiseValueMin    string `gorm:"type:varchar(30);not null;default:0;" json:"rise_value_min"`   // 爬坡期的命中后取值范围最小值
	RiseValueMax    string `gorm:"type:varchar(30);not null;default:0;" json:"rise_value_max"`   // 爬坡期的命中后取值范围最大值
	PeakValueMin    string `gorm:"type:varchar(30);not null;default:0;" json:"peak_value_min"`   // 峰值期的命中后取值范围最小值
	PeakValueMax    string `gorm:"type:varchar(30);not null;default:0;" json:"peak_value_max"`   // 峰值期的命中后取值范围最大值
	FallValueMin    string `gorm:"type:varchar(30);not null;default:0;" json:"fall_value_min"`   // 回落期的命中后取值范围最小值
	FallValueMax    string `gorm:"type:varchar(30);not null;default:0;" json:"fall_value_max"`   // 回落期的命中后取值范围最大值
	UpperShadowMax  string `gorm:"type:varchar(30);not null;default:0;" json:"upper_shadow_max"` // 上影线最大值
	LowerShadowMax  string `gorm:"type:varchar(30);not null;default:0;" json:"lower_shadow_max"` // 下影线最大值
	// 爬坡期完整蜡烛图开始时间和是否需要改变
	RiseCandlesInfo json.RawMessage `gorm:"type:json;default:null" json:"rise_candles_info" swaggertype:"object"`
	// 峰值期完整蜡烛图开始时间和是否需要改变
	PeakCandlesInfo json.RawMessage `gorm:"type:json;default:null" json:"peak_candles_info" swaggertype:"object"`
	// 回落期完整蜡烛图开始时间和是否需要改变
	FallCandlesInfo json.RawMessage `gorm:"type:json;default:null" json:"fall_candles_info" swaggertype:"object"`
}

type OutputSymbol struct {
	Platform string `json:"platform"`
	Symbol   string `json:"symbol"`
}

type OutputSymbolOriginal struct {
	Platform string `json:"platform"`
	Symbol   string `json:"symbol"`
	Type     string `json:"type"`
}

/*
数据表
Maximum 有的平台取历史数据只能取N条，0就是不限制，可以取n年的历史，其它值就是平台只支持取N条历史数据
LastReplenishTime int64  `gorm:"not null;default:0;" json:"last_replenish_time"` // 记录最后一次更新的时间 断连可以继续
stock股票 forex外汇 crypto代币 commodity商品 energy能源 stockindex 指数
*/
type Tick struct {
	MyModel
	Symbolname           string         `gorm:"type:varchar(30);not null;default:'';" json:"symbolname"`                                    // 产品名称
	Symbol               string         `gorm:"uniqueIndex:idx_symbol_type_platform;type:varchar(30);not null;default:'';" json:"symbol"`   // 产品标识
	ReplaceSymbol        string         `gorm:"type:varchar(30);not null;default:'';" json:"replace_symbol"`                                // 替换产品标识
	Type                 string         `gorm:"uniqueIndex:idx_symbol_type_platform;type:varchar(30);not null;default:'';" json:"type"`     // 类型
	SubType              string         `gorm:"type:varchar(30);not null;default:'';" json:"sub_type"`                                      // 子类型
	Platform             string         `gorm:"uniqueIndex:idx_symbol_type_platform;type:varchar(30);not null;default:'';" json:"platform"` // 来源平台 alltick
	Status               uint8          `gorm:"index;not null;default:0;" json:"status"`                                                    // 状态 0禁用 1启用
	InitDone             uint8          `gorm:"index;not null;default:0;" json:"init_done"`                                                 // 初始化是否完成 初始化中不补齐数据 等完成后再进行补齐
	InitTime             int64          `gorm:"not null;default:0;" json:"init_time"`                                                       // 初始化的时间
	Maximum              uint32         `gorm:"not null;default:0;" json:"maximum"`                                                         // 最大更新数据量上限
	Oncecount            uint32         `gorm:"not null;default:0;" json:"oncecount"`                                                       // 单次数量
	DesignatedTime       int64          `gorm:"not null;default:0;" json:"designated_time"`                                                 // 指定取到历史数据的时间
	TimeZone             string         `gorm:"type:varchar(300);not null;default:'';" json:"time_zone"`                                    // 数据源的默认时区 UTC UTC+2
	ShadowDebug          bool           `gorm:"type:TINYINT(1);not null;default:false;" json:"shadow_debug"`                                // 是否记录插针日志
	KlineDebug           bool           `gorm:"type:TINYINT(1);not null;default:false;" json:"kline_debug"`                                 // 是否记录开、收盘异常的k线 0禁用 1启用
	KlineFix             bool           `gorm:"type:TINYINT(1);not null;default:false;" json:"kline_fix"`                                   // 是否修复开、收盘异常的k线 0禁用 1启用
	ErrorMargin          float64        `gorm:"type:decimal(10,2);not null;default:0.2;" json:"error_margin"`                               // 允许误差范围最大值
	AvailablePlatforms   datatypes.JSON `gorm:"type:json;default:null" json:"available_platforms"`                                          // 可选平台
	DefaultDecimalPlaces uint           `gorm:"not null;default:5;" json:"default_decimal_places"`                                          // 默认小数位数(应客户要求 K 线显示5位小数)
}

/*
产品日志
LogType 类型 1 上下两条k线产生的误差超过设定范围 2 插针记录 上条为插针改变的 下条为原始未改变的
*/
type TickShadowLog struct {
	MyModel
	Symbol             string `gorm:"type:varchar(30);not null;default:'';" json:"symbol"`                // 产品标识
	Platform           string `gorm:"type:varchar(30);not null;default:'';" json:"platform"`              // 来源平台 alltick
	Resolutions        string `gorm:"type:varchar(30);not null;default:'';" json:"resolutions"`           // 时间分辨率
	SfStartTs          int64  `gorm:"not null;default:0;" json:"sf_start_ts"`                             // 插针参数开始时间
	SfCurrentTs        int64  `gorm:"not null;default:0;" json:"sf_current_ts"`                           // 插针参数当前时间
	SfRiseTime         int    `gorm:"not null;default:0;" json:"sf_rise_time"`                            // 插针参数爬坡时间
	SfPeakTime         int    `gorm:"not null;default:0;" json:"sf_peak_time"`                            // 插针参数峰值时间
	SfFallTime         int    `gorm:"not null;default:0;" json:"sf_fall_time"`                            // 插针参数回落时间
	SfFluc             string `gorm:"type:varchar(30);not null;default:'0';" json:"sf_fluc"`              // 插针幅度
	SfStartCandlestick string `gorm:"type:varchar(30);not null;default:'0';" json:"sf_start_candlestick"` // 插针开始价格
	Stage              int    `gorm:"not null;default:0;" json:"stage"`                                   // 插针时期
	UserID             uint   `gorm:"not null;default:0;" json:"user_id"`                                 // 用户ID
	ShadowID           uint   `gorm:"not null;default:0;" json:"shadow_id"`                               // 插针ID
	KlineID            string `gorm:"type:varchar(30);not null;default:'0';" json:"kline_id"`             // 上条生成的ID
	KlineTime          int64  `gorm:"not null;default:0;" json:"kline_time"`                              // 时间戳
	Open               string `gorm:"type:varchar(30);not null;default:'0';" json:"open"`                 // 开盘价
	Close              string `gorm:"type:varchar(30);not null;default:'0';" json:"close"`                // 收盘价
	High               string `gorm:"type:varchar(30);not null;default:'0';" json:"high"`                 // 最高价
	Low                string `gorm:"type:varchar(30);not null;default:'0';" json:"low"`                  // 最低价
	Volume             string `gorm:"type:varchar(30);not null;default:'0';" json:"volume"`               // 成交量
	Turnover           string `gorm:"type:varchar(30);not null;default:'0';" json:"turnover"`             // 成交额
	UseOpen            bool   `gorm:"type:TINYINT(1);not null;default:false;" json:"use_open"`            // 是否使用开盘价格 在插针结束的那分钟内要使用做假的开盘价格
	UseClose           bool   `gorm:"type:TINYINT(1);not null;default:false;" json:"use_close"`           // 是否使用收盘价格 在插针开始的那分钟内要使用做假的收盘价格
	IsOpen             bool   `gorm:"type:TINYINT(1);not null;default:false;" json:"is_open"`             // 是否为一分钟的开始
	OrigKlineTime      int64  `gorm:"not null;default:0;" json:"orig_kline_time"`                         // 下条时间戳
	OrigOpen           string `gorm:"type:varchar(30);not null;default:'0';" json:"orig_open"`            // 下条开盘价
	OrigClose          string `gorm:"type:varchar(30);not null;default:'0';" json:"orig_close"`           // 下条收盘价
	OrigHigh           string `gorm:"type:varchar(30);not null;default:'0';" json:"orig_high"`            // 下条最高价
	OrigLow            string `gorm:"type:varchar(30);not null;default:'0';" json:"orig_low"`             // 下条最低价
	OrigVolume         string `gorm:"type:varchar(30);not null;default:'0';" json:"orig_volume"`          // 下条成交量
	OrigTurnover       string `gorm:"type:varchar(30);not null;default:'0';" json:"orig_turnover"`        // 下条成交额
	Direction          string `json:"direction"`                                                          // 偏移方向
}

type TickCache struct {
	ID             uint
	Symbolname     string  // 产品名称
	Symbol         string  // 产品标识
	ReplaceSymbol  string  // 替换产品标识
	Type           string  // 类型
	SubType        string  // 子类型
	Platform       string  // 来源平台 alltick
	InitDone       uint8   // 初始化是否完成 初始化中不补齐数据 等完成后再进行补齐
	InitTime       int64   // 初始化的时间
	Maximum        uint32  // 最大更新数据量上限
	Oncecount      uint32  // 单次数量
	DesignatedTime int64   // 指定取到历史数据的时间
	TimeZone       string  // 数据源的默认时区 UTC UTC+2
	ShadowDebug    bool    // 是否记录插针日志
	KlineDebug     bool    // 是否记录开、收盘异常的k线 0禁用 1启用
	KlineFix       bool    // 是否修复开、收盘异常的k线 0禁用 1启用
	ErrorMargin    float64 // 允许误差范围
}

/*
盘口报价下发给用户统计
*/
type OBSendStatistics struct {
	MyModel
	UserID         uint   `gorm:"not null;default:0;" json:"user_id"`                    // 用户ID
	Platform       string `gorm:"type:varchar(30);not null;default:'';" json:"platform"` // 来源平台
	Symbol         string `gorm:"type:varchar(30);not null;default:'';" json:"symbol"`   // 产品标识
	Interval       int    `gorm:"not null;default:0;" json:"interval"`                   // 时间间隔(分钟)
	Count          int64  `gorm:"not null;default:0;" json:"ount"`                       // 发下数量
	StatisticsTime int64  `gorm:"index;not null;default:0;" json:"statistics_time"`      // 统计时间
}

/*
盘口报价接收统计
*/
type OBReceiveStatistics struct {
	MyModel
	Platform        string `gorm:"type:varchar(30);not null;default:'';" json:"platform"` // 来源平台
	Symbol          string `gorm:"type:varchar(30);not null;default:'';" json:"symbol"`   // 产品标识
	Interval        int    `gorm:"not null;default:0;" json:"interval"`                   // 时间间隔(分钟)
	Count           int64  `gorm:"not null;default:0;" json:"count"`                      // 接收数量
	NoChangeCount   int64  `gorm:"not null;default:0;" json:"no_change_count"`            // 第一组价格未改变的数量
	TimeBeforeCount int64  `gorm:"not null;default:0;" json:"time_before_count"`          // 时间过晚的数量
	StatisticsTime  int64  `gorm:"index;not null;default:0;" json:"statistics_time"`      // 统计时间
}

/*
成交报价下发给用户统计
*/
type TradeSendStatistics struct {
	MyModel
	UserID         uint   `gorm:"not null;default:0;" json:"user_id"`                    // 用户ID
	Platform       string `gorm:"type:varchar(30);not null;default:'';" json:"platform"` // 来源平台
	Symbol         string `gorm:"type:varchar(30);not null;default:'';" json:"symbol"`   // 产品标识
	Interval       int    `gorm:"not null;default:0;" json:"interval"`                   // 时间间隔(分钟)
	Count          int64  `gorm:"not null;default:0;" json:"ount"`                       // 发下数量
	StatisticsTime int64  `gorm:"index;not null;default:0;" json:"statistics_time"`      // 统计时间
}

/*
成交报价
*/
type TradeReceiveStatistics struct {
	MyModel
	Platform        string `gorm:"type:varchar(30);not null;default:'';" json:"platform"` // 来源平台
	Symbol          string `gorm:"type:varchar(30);not null;default:'';" json:"symbol"`   // 产品标识
	Interval        int    `gorm:"not null;default:0;" json:"interval"`                   // 时间间隔(分钟)
	Count           int64  `gorm:"not null;default:0;" json:"count"`                      // 数量
	NoChangeCount   int64  `gorm:"not null;default:0;" json:"no_change_count"`            // 价格未改变的数量
	NoDataCount     int64  `gorm:"not null;default:0;" json:"no_data_count"`              // 没有实际数据的数量
	TimeBeforeCount int64  `gorm:"not null;default:0;" json:"time_before_count"`          // 时间过晚的数量
	StatisticsTime  int64  `gorm:"index;not null;default:0;" json:"statistics_time"`      // 统计时间
}

// 反向蜡烛图信息
// 默认所有完整的k线都是按照设定的插针方向线性增长,涨或是跌
type CandlesInfo struct {
	StartTime         int64           // 开始时间,这一根完整k线的起始时间(秒)
	EndTime           int64           // 这根完整k线的结束时间(秒)
	IsChange          bool            // 这根K线是否需要反向走势
	DefaultOpenPrice  decimal.Decimal // 预设的这根完整K线的开盘价(这个价格是一个上涨的浮动值)
	DefaultClosePrice decimal.Decimal // 预设的这根完整K线的收盘价
}
