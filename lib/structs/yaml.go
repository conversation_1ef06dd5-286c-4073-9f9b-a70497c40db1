package structs

import "time"

// Yaml配置文件的结构体
type YamlConf struct {
	Externalgrpc       YamlExternalgrpc `yaml:"externalgrpc"`
	WebServer          YamlWebServer    `yaml:"web_server"`
	InfluxDB           YamlInfluxDB     `yaml:"influx_db"`
	MySQL              YamlMySQL        `yaml:"mysql"`
	Redis              YamlRedis        `yaml:"redis"`
	Logging            Logging          `yaml:"logging"`
	Kline              YamlKline        `yaml:"kline"`
	StatisticsInterval int              `yaml:"statistics_interval"` // 统计的时间间隔
}

type YamlRedis struct {
	Host              string `yaml:"host"`
	Port              int    `yaml:"port"`
	Username          string `yaml:"username"`
	Password          string `yaml:"password"`
	DB                string `yaml:"db"`
	Prefix            string `yaml:"prefix"`
	DefaultExpireTime int    `yaml:"default_expire_time"`
}

type YamlMySQL struct {
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	Database string `yaml:"database"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
	Prefix   string `yaml:"prefix"`
	LogLevel string `yaml:"log_level"`
}

type YamlInfluxDB struct {
	Url                string  `yaml:"url"`
	Token              string  `yaml:"token"`
	Org                string  `yaml:"org"`
	Bucket             string  `yaml:"bucket"`
	IntervalWrite      int     `yaml:"interval_write"`
	RetryQuantity      uint    `yaml:"retry_quantity"`
	IntervalBasicKline uint    `yaml:"interval_basic_kline"`
	LimitDiff          float64 `yaml:"limit_diff"`
}

type YamlWebServer struct {
	Name                string    `yaml:"name"`
	Version             string    `yaml:"version"`
	Mode                string    `yaml:"mode"`
	LogLevel            string    `yaml:"log_Level"`
	Charset             string    `yaml:"charset"`
	TimeFormat          string    `yaml:"time_format"`
	Host                string    `yaml:"host"`
	Port                int       `yaml:"port"`
	JWTSECRETKEY        string    `yaml:"jwt_secret_key"`
	JWTIssuer           string    `yaml:"jwt_issuer"`
	JWTExpiryTime       int       `yaml:"jwt_expiry_time"`
	GoogleTotp          bool      `yaml:"google_totp"`
	GoogleTotpHeartbeat bool      `yaml:"google_totp_heartbeat"`
	HeartbeatValidity   int       `yaml:"heartbeat_validity"`
	LocalPath           string    `yaml:"local_path"`
	Admin               YamlAdmin `yaml:"admin"`
}

type YamlAdmin struct {
	DefaultPageSize int `yaml:"default_page_size"`
}

/*
数据源连接的grpc
*/
type YamlExternalgrpc struct {
	Host string `yaml:"host"`
	Port string `yaml:"port"`
}

/*
记录日志
*/
type Logging struct {
	InfluxDB      bool `yaml:"influx_db"`       // 记录InfluxDB日志
	AddShadowLog  bool `yaml:"add_shadow_log"`  // 记录插针添加的日志
	ExecShadowLog bool `yaml:"exec_shadow_log"` // 记录插针执行的日志
	InitRepLog    bool `yaml:"init_rep_log"`    // 记录数据源初始化和补齐的日志 这个是大量的日志 单独标记
	// 新增的日志配置
	LogLevel     string        `yaml:"log_level"`     // 日志级别
	LogDir       string        `yaml:"log_dir"`       // 日志目录
	MaxAge       time.Duration `yaml:"max_age"`       // 文件最大保存时间
	RotationTime time.Duration `yaml:"rotation_time"` // 日志切割时间
	IsDev        bool          `yaml:"is_dev"`
	DataFlow     bool          `yaml:"data_flow"` // 记录数据流日志（高频数据）
}

// k线控制配置
type YamlKline struct {
	InverseProbability float64 `yaml:"inverse_probability"` // k线完整蜡烛图反向拉的概率
	FluctuateStepSize  int64   `yaml:"fluctuate_step_size"` // k线完整蜡烛图反向拉步长数
}
