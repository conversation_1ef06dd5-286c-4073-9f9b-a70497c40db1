package structs

// 返回报文
type Response struct {
	/*
		返回值
		200 成功
		520 通用错误代码
		550 参数错误
		551 需要登录
		552 需要谷歌验证
		553 令牌生成失败
		555 密码错误
		556 权限不足
		557 设置缓存失败
		558 取权限错误
		559 mysql错误
	*/
	Code int `json:"code"`
	// 返回数据
	Data any `json:"data,omitempty"`
	// 返回消息
	Message string `json:"message,omitempty"`
	// 返回错误消息 如果为空参考返回消息
	Error string `json:"error,omitempty"`
}

// 返回简化报文
type ResponseSimplify struct {
	// 返回值
	Code int `json:"code"`
	// 返回消息
	Message string `json:"message,omitempty"`
}
