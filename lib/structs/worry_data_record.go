package structs

import (
	"encoding/json"
	"github.com/shopspring/decimal"
)

// 临时错误记录表

type WorryDataRecord struct {
	MyModel
	// 传过来的真实数据属于的时间
	ItemTime int64 `gorm:"index" mapstructure:"item_time" json:"receive_time"`
	// 数据源名称(数据源平台名称)
	DataSourceName string `gorm:"index" mapstructure:"data_source_name" json:"data_source_name"`
	// 产品名称
	ProductName string `gorm:"index" mapstructure:"product_name" json:"product_name"`
	// 当时的价格
	Price decimal.Decimal `gorm:"index" mapstructure:"bids" json:"bids"`
	// 计算出来的价格
	NewPrice decimal.Decimal `gorm:"index" mapstructure:"new_price" json:"new_price"`
	// 是否控盘
	IsControl bool `gorm:"index" mapstructure:"is_control" json:"is_control"`
	// 插针阶段
	Stage int `gorm:"index" mapstructure:"stage" json:"stage"`
	// 插针详情
	ControlDetailJson json.RawMessage `gorm:"type:json;default:null" mapstructure:"control_detail_json" json:"control_detail_json"`
}
