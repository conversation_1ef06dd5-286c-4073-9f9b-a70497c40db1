package structs

// 定义成交数据结构体
type TradeData struct {
	Seq            int64   // 交易序列号
	TickTime       int64   // 时间戳（以毫秒为单位）
	Price          float64 // 成交价格
	Volume         float64 // 成交量
	Turnover       float64 // 成交额（价格 * 成交量）
	TradeDirection int     // 买卖方向（1表示买入，2表示卖出）
	Symbol         string  // 标签
}

// 定义盘口数据结构体
type OrderBookData struct {
	Seq         int64   // 交易序列号
	TickTime    int64   // 时间戳（以毫秒为单位）
	BidsPrice1  float64 // 买入价格
	BidsVolume1 float64 // 买入量
	BidsPrice2  float64 // 买入价格
	BidsVolume2 float64 // 买入量
	BidsPrice3  float64 // 买入价格
	BidsVolume3 float64 // 买入量
	BidsPrice4  float64 // 买入价格
	BidsVolume4 float64 // 买入量
	BidsPrice5  float64 // 买入价格
	BidsVolume5 float64 // 买入量
	AsksPrice1  float64 // 卖出价格
	AsksVolume1 float64 // 卖出量
	AsksPrice2  float64 // 卖出价格
	AsksVolume2 float64 // 卖出量
	AsksPrice3  float64 // 卖出价格
	AsksVolume3 float64 // 卖出量
	AsksPrice4  float64 // 卖出价格
	AsksVolume4 float64 // 卖出量
	AsksPrice5  float64 // 卖出价格
	AsksVolume5 float64 // 卖出量
	Symbol      string  // 标签
}

// 定义成交数据结构体
type KlineRetData struct {
	ID       string  `json:"id"`       // 传送ID
	Code     string  `json:"code"`     // 交易产品代码
	Interval string  `json:"interval"` // K 线的周期长度
	Time     int64   `json:"time"`     // K线时间
	Open     float64 `json:"open"`     // 开盘价
	Close    float64 `json:"close"`    // 收盘价
	High     float64 `json:"high"`     // 最高价
	Low      float64 `json:"low"`      // 最低价
	Volume   float64 `json:"volume"`   // 成交量
	Turnover float64 `json:"turnover"` // 成交额
}

// 插针使用的缓存数据
type KlineShadowData struct {
	Open     float64 `json:"open"`     // 开盘价
	Close    float64 `json:"close"`    // 收盘价
	High     float64 `json:"high"`     // 最高价
	Low      float64 `json:"low"`      // 最低价
	Volume   float64 `json:"volume"`   // 成交量
	Turnover float64 `json:"turnover"` // 成交额
}

// 数据库中的结构 用于返回
type KlineDataRet struct {
	// Symbol   string  `json:"-"`        // 标签
	Time     int64   `json:"time"`     // K线时间
	Open     float64 `json:"open"`     // 开盘价
	Close    float64 `json:"close"`    // 收盘价
	High     float64 `json:"high"`     // 最高价
	Low      float64 `json:"low"`      // 最低价
	Volume   float64 `json:"volume"`   // 成交量
	Turnover float64 `json:"turnover"` // 成交额
}

// 插针的k线数据
type KlineDataShadow struct {
	UseOpen   bool    `json:"use_open"`  // 是否使用开盘价格 在插针结束的那分钟内要使用做假的开盘价格
	UseClose  bool    `json:"use_close"` // 是否使用收盘价格 在插针开始的那分钟内要使用做假的收盘价格
	Time      int64   `json:"time"`      // K线时间
	Open      float64 `json:"open"`      // 开盘价
	Close     float64 `json:"close"`     // 收盘价
	High      float64 `json:"high"`      // 最高价
	Low       float64 `json:"low"`       // 最低价
	Volume    float64 `json:"volume"`    // 成交量
	Turnover  float64 `json:"turnover"`  // 成交额
	Direction string  `json:"direction"` // 偏移方向
}

// 其它k线的参数
type OtherKlineParam struct {
	Platform       string // 平台
	Symbol         string // 产品
	Resolutions    string // 时间颗粒
	NewResolutions string // 新存储的时间颗粒
	TimeZone       string // 时区
	StartTime      int64  // 查询开始时间(包含) 也是k线存储时间
	EndTime        int64  // 查询结束时间(不包含)
}

// 查询k线的参数
type QueryKlineParam struct {
	Platform    string // 平台
	Symbol      string // 产品
	Resolutions string // 时间颗粒
	TimeZone    string // 时区
	EndTime     int64  // 查询结束时间(不包含)
	Count       int    // 查询多少条
}

/*
k线数据
*/
type AllDataKline struct {
	Platform string  `json:"platform"` // 平台
	Symbol   string  `json:"symbol"`   // 标签
	Time     int64   `json:"time"`     // 时间戳 接收时使用秒单位
	Open     float64 `json:"open"`     // 开盘价
	Close    float64 `json:"close"`    // 收盘价
	High     float64 `json:"high"`     // 最高价
	Low      float64 `json:"low"`      // 最低价
	Volume   float64 `json:"volume"`   // 成交量
	Turnover float64 `json:"turnover"` // 成交额
}

// 定义结构体以存储每个月的起始和结束时间
type MonthRange struct {
	Start int64 // 月初时间戳
	End   int64 // 月末时间戳
}
