package main

import (
	"fmt"
	"trading_tick_server/config"
	"trading_tick_server/internal/data_transfer_station/service"
	"trading_tick_server/internal/data_transfer_station/types"
	kcc_service "trading_tick_server/internal/kline_control_center/service"
	koi_service "trading_tick_server/internal/kline_offset_immediate/service"
	kol_service "trading_tick_server/internal/kline_offset_linear/service"
	"trading_tick_server/internal/user/user_cache"
	"trading_tick_server/lib/cache"
	"trading_tick_server/lib/function"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/influxdb"
	"trading_tick_server/lib/logger"
	"trading_tick_server/lib/mysql"
	"trading_tick_server/lib/redis"
	"trading_tick_server/lib/symbol_price"
	ciclientgrpc "trading_tick_server/tickserver/ci_client_grpc"
	"trading_tick_server/tickserver/ci_client_grpc/operate"
	externalgrpc "trading_tick_server/tickserver/data_source_grpc"
	"trading_tick_server/web/routers"

	_ "trading_tick_server/pkg/validate"
)

func main() {
	function.InitPath()
	function.InitYaml()
	/*
		新配置
	*/
	config.Init()
	// 成交数据最新价格记录初始化
	symbol_price.InitSymbolPrice()
	influxdb.SetLimiter()
	logger.InitNew(&global.Yaml.Logging)
	mysql.Run()
	redis.Run()
	influxdb.Run()
	/*
		从数据库缓存所有产品信息
	*/
	cache.CacheTickSliceSet()
	user_cache.CacheUserSliceSet()
	ciclientgrpc.CiClientGrpc = ciclientgrpc.NewUserConnManager()
	/*
		创建数据传输站
	*/
	service.GetStation[types.ExtraDataPkg]()
	/*
		模块恢复
	*/
	koi_service.OffsetImmediateRecover()
	kol_service.OffsetLinearRecover()
	_ = kcc_service.SetAllControlEnd()
	operate.ProcessChannelData()
	go externalgrpc.Run()

	r := routers.Run()
	err := r.Run(fmt.Sprintf("%s:%d", global.Yaml.WebServer.Host, global.Yaml.WebServer.Port))
	if err != nil {
		panic(fmt.Errorf("start gin server failed, error: %w", err))
	}
}
