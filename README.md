## 项目目录说明
```

myapp/
|── main.go                       # 应用启动入口
│
├── docs/                         # Swagger 文档等
│
├── iface/                        # ✅ 全局接口定义（跨模块）
│   ├── cache.go                  # interface Cache
│   ├── notifier.go               # interface Notifier
│   └── auth_service.go           # interface AuthService
│
├── internal/                     # 应用核心（按模块划分）
│   └── user/                     # user 模块
│       ├── controller/           # 控制器层（接收 HTTP 请求）
│       │   └── user_controller.go
│       ├── service/              # 业务逻辑实现层
│       │   └── user_service.go   # 实现 iface.UserService
│       ├── repository/           # 数据访问实现层
│       │   └── user_repo.go      # 实现 iface.UserRepo
│       ├── dto/                  # 请求 / 响应结构体
│       │   ├── create_user_request.go
│       │   └── user_response.go
│       ├── model/                # ORM 模型
│       │   └── user.go
│       └── iface/                # ✅ user 模块内部接口
│           ├── user_repo.go      # interface UserRepo
│           └── user_service.go   # interface UserService
│
├── middleware/                   # 中间件（如 JWT 校验）
│   └── auth.go
│
├── pkg/                          # 可复用第三方库、通用工具
│   ├── logger/                   # 日志组件
│   └── validatorx/               # ✅ 自定义 validator 注册
│       └── decimal.go
│
├── public/                       # 公共结构与工具（多模块共用）
│   
├── common/                       # 通用工具（如 util, time, etc） 
│   └── common/                  
│
├── router/                       # Gin 路由注册
│   └── router.go
│
├── storage/                      # 文件存储目录（可挂载）
│
├── go.mod
└── README.md
```
## 文件命名规范
>例如在 internal/data_transfer_station/model/ 下,<br />
功能模块是 data_transfer_station (数据传输站), 具体的功能是数据传输站的模型, <br />
所以文件命名为 data_transfer_station_model.go 这样就会过长, <br />
所以可以简写为 dts_model.go, 这样就不会过长了, <br />