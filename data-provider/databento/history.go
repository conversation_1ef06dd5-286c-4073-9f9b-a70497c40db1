package databento

import (
	"context"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"trading_tick_server/lib/function"
	"trading_tick_server/lib/logger"
	pb "trading_tick_server/tickserver/data_source_grpc/data_source_proto"
)

func QueryKlineHistory(uri, token, symbol string, startTime, endTime int64) ([]pb.ReceiveDataKline, error) {
	var m pb.RetDataKline
	url_ := fmt.Sprintf(
		"%v/databentoKlineHistory?token=%v&symbol=%v&startTime=%v&endTime=%v",
		uri,
		token,
		symbol,
		startTime,
		endTime,
	)

	// HTTP 请求
	b2, err := function.HttpCurlGet(url_)
	if err != nil {
		logger.ErrorCtx(context.Background(), "❌Databento HTTP请求失败",
			zap.String("URL", url_),
			zap.String("产品", symbol),
			zap.Error(err))
		return nil, fmt.Errorf("HTTP请求失败: %w", err)
	}

	// 检查响应内容
	if len(b2) == 0 {
		return nil, fmt.Errorf("API返回空响应")
	}

	// 记录响应内容用于调试
	logger.DebugCtx(context.Background(), "Databento API响应",
		zap.String("产品", symbol),
		zap.Int("响应长度", len(b2)),
		zap.String("响应前200字符", string(b2[:min(200, len(b2))])))

	// JSON 解析
	err = json.Unmarshal(b2, &m)
	if err != nil {
		logger.ErrorCtx(context.Background(), "Databento JSON解析失败",
			zap.String("产品", symbol),
			zap.Error(err),
			zap.String("响应内容", string(b2)))
		return nil, fmt.Errorf("JSON解析失败: %w", err)
	}

	// 检查业务状态码
	if m.Code != 200 {
		logger.WarnCtx(context.Background(), "Databento API返回错误状态",
			zap.String("产品", symbol),
			zap.Int("状态码", m.Code),
			zap.Any("响应数据", m))
		return nil, fmt.Errorf("API返回错误状态码: %d", m.Code)
	}
	return m.Data, nil
}
