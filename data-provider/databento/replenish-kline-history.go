package databento

import (
	"fmt"
	"sort"
	"sync"
	"time"
	"trading_tick_server/lib/function"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/influxdb"
	"trading_tick_server/lib/structs"
	pb "trading_tick_server/tickserver/data_source_grpc/data_source_proto"
	"trading_tick_server/utils"

	"go.uber.org/zap"
)

// 添加批量大小限制
const maxBatchSize = 10000

type KlineCache struct {
	sync.RWMutex
	data map[string][]structs.AllDataKline // key: table_startTime_endTime
}

var cache = &KlineCache{
	data: make(map[string][]structs.AllDataKline),
}

type ReplenishProgress struct {
	Symbol         string
	TotalWeeks     int
	CompletedWeeks int
	StartTime      time.Time
	Errors         []error
	MainLoopCount  int // 主循环计数
	mu             sync.Mutex
}

func (p *ReplenishProgress) Log() {
	progress := float64(p.CompletedWeeks) / float64(p.TotalWeeks) * 100
	elapsed := time.Since(p.StartTime)
	fmt.Printf("📊 [%s] 进度: %.1f%% (%d/%d周) 主循环: %d 耗时: %v\n",
		p.Symbol, progress, p.CompletedWeeks, p.TotalWeeks, p.MainLoopCount, elapsed)
}

func (p *ReplenishProgress) AddComplete() {
	p.mu.Lock()
	defer p.mu.Unlock()
	p.CompletedWeeks++
	p.Log()
}

func (p *ReplenishProgress) AddError(err error) {
	p.mu.Lock()
	defer p.mu.Unlock()
	p.Errors = append(p.Errors, err)
}

func (p *ReplenishProgress) ResetWeekProgress(totalWeeks int) {
	p.mu.Lock()
	defer p.mu.Unlock()
	p.TotalWeeks = totalWeeks
	p.CompletedWeeks = 0
	p.MainLoopCount++
}

func (p *ReplenishProgress) GetMainLoopCount() int {
	p.mu.Lock()
	defer p.mu.Unlock()
	return p.MainLoopCount
}

func ReplenishKlineHistory(uri, token, symbol string, startTimeUnix, endTimeUnix int64, timeZone string) ([]pb.ReceiveDataKline, error) {
	table := function.GetInfluxDBTable(DATABENTO, symbol, timeZone, global.Resolutions.Minute1)
	var err error
	var replenishMinTime int64

	maxRetries := 3
	retryCount := 0
	lastMinTime := int64(0)

	// 初始化进度追踪
	weeksPeriod := utils.SplitByWeeks(startTimeUnix, endTimeUnix)
	progress := &ReplenishProgress{
		Symbol:     symbol,
		TotalWeeks: len(weeksPeriod),
		StartTime:  time.Now(),
		Errors:     []error{},
	}

	// 第一步：补齐1分钟K线数据
	maxMainLoopIterations := 100 // 防止死循环的最大迭代次数
	mainLoopCount := 0

	for {
		mainLoopCount++
		if mainLoopCount > maxMainLoopIterations {
			return nil, fmt.Errorf("补齐数据失败：主循环迭代次数超过限制 (%d)", maxMainLoopIterations)
		}

		replenishMinTime, err = influxdb.GetMinTimestamp(table, "real")
		if err != nil {
			global.Lg.Error("取最小时间戳失败", zap.Any("table", table), zap.Error(err))
			fmt.Printf("❌取最小时间戳失败: %v\n", err)
			return nil, err
		}

		fmt.Printf("🔍 [%s] 主循环第 %d 次，当前最小时间戳: %d, 目标时间戳: %d\n",
			symbol, mainLoopCount, replenishMinTime, startTimeUnix)

		// 处理最小时间戳的各种情况
		if replenishMinTime == 0 {
			// 最小时间戳为0，表示数据库中没有数据或查询失败
			fmt.Printf("✅ [%s] 数据库中没有数据或已补充完成（最小时间戳为0），补充结束\n", symbol)
			break
		} else if replenishMinTime <= startTimeUnix {
			// 最小时间戳小于等于目标时间戳，表示已经补充到目标时间
			fmt.Printf("✅ [%s] k线数据已经补充完成, 最小时间戳: %d, 目标时间戳: %d\n", symbol, replenishMinTime, startTimeUnix)
			break
		} else {
			// 还需要继续补充数据
			endTimeUnix = replenishMinTime
		}

		// 检查是否需要重试（只有在有有效时间戳且未更新时才重试）
		if replenishMinTime > 0 && replenishMinTime == lastMinTime {
			retryCount++
			if retryCount >= maxRetries {
				fmt.Printf("❌ [%s] 补齐数据失败：最小时间戳未更新，已重试 %d 次\n", symbol, maxRetries)
				break
			}
			fmt.Printf("⚠️  [%s] 最小时间戳未更新，重试 %d/%d\n", symbol, retryCount, maxRetries)
			time.Sleep(time.Second * time.Duration(retryCount))
		} else {
			retryCount = 0
			lastMinTime = replenishMinTime
		}

		weeksPeriod = utils.SplitByWeeks(startTimeUnix, endTimeUnix)

		// 如果没有需要处理的周期，说明数据已经完整
		if len(weeksPeriod) == 0 {
			fmt.Printf("✅ [%s] 没有需要处理的时间周期，数据补充完成\n", symbol)
			break
		}

		// 重置进度计数器，避免累积错误
		progress.ResetWeekProgress(len(weeksPeriod))

		fmt.Printf("📋 [%s] 主循环第 %d 轮，需要处理 %d 个周期\n", symbol, progress.GetMainLoopCount(), len(weeksPeriod))

		successCount := 0
		for i := len(weeksPeriod) - 1; i >= 0; i-- {
			fmt.Printf("🚀[Databento] Symbol:%v, %v\n", symbol, weeksPeriod[i].String())
			var data []pb.ReceiveDataKline
			data, err = QueryKlineHistory(uri, token, symbol, weeksPeriod[i].Start, weeksPeriod[i].End)
			if err != nil {
				global.Lg.Error("初始化向数据源取k线数据失败", zap.Any("symbol", symbol), zap.Int64("endTimeUnix", endTimeUnix), zap.Int64("startTimeUnix", startTimeUnix), zap.Error(err))
				fmt.Printf("❌初始化向数据源取k线数据失败, Symbol,%v, err: %v\n", symbol, err)
				progress.AddError(err)
				continue
			}

			// 写入1分钟数据
			var dataNew []structs.AllDataKline
			for _, v := range data {
				dataNew = append(dataNew, structs.AllDataKline{
					Platform: v.Platform,
					Symbol:   v.Symbol,
					Time:     v.Time,
					Open:     v.Open,
					Close:    v.Close,
					High:     v.High,
					Low:      v.Low,
					Volume:   v.Volume,
					Turnover: v.Turnover,
				})
			}
			// 使用批量写入函数
			err = writeBatchedKlineData(table, dataNew, "initDataMinute1")
			if err != nil {
				if global.Yaml.Logging.InfluxDB {
					global.Lg.Error("❌写入1分钟K线数据失败", zap.Error(err))
				}
				progress.AddError(err)
			} else {
				progress.AddComplete()
				successCount++
			}
		}

		fmt.Printf("📊 [%s] 本轮成功处理 %d/%d 个周期\n", symbol, successCount, len(weeksPeriod))

		// 如果有太多错误，中断处理
		if len(progress.Errors) > len(weeksPeriod)/2 {
			return nil, fmt.Errorf("补齐数据失败：错误过多 (%d/%d)", len(progress.Errors), len(weeksPeriod))
		}

		// 如果本轮没有成功处理任何数据，可能存在问题
		if successCount == 0 && len(weeksPeriod) > 0 {
			fmt.Printf("⚠️  [%s] 本轮没有成功处理任何数据，可能存在问题\n", symbol)
			time.Sleep(time.Second * 5) // 等待5秒后重试

			// 如果连续多轮都没有成功处理数据，提前退出
			if mainLoopCount > 10 {
				fmt.Printf("❌ [%s] 连续多轮无法处理数据，提前退出\n", symbol)
				break
			}
		}
	}

	// 第二步：聚合生成其他时间颗粒的K线数据
	global.Lg.Debug("📊开始聚合生成其他时间颗粒的K线数据...")

	// 获取实际的1分钟数据时间范围
	minTime, err := influxdb.GetMinTimestamp(table, "real")
	if err != nil {
		global.Lg.Error("❌获取最小时间戳失败", zap.Error(err))
		return nil, err
	}

	maxTime, err := influxdb.GetMaxTimestamp(table, "real")
	if err != nil {
		global.Lg.Error("❌获取最大时间戳失败", zap.Error(err))
		return nil, err
	}

	if minTime == 0 || maxTime == 0 {
		// 如果没有1分钟K线数据，直接返回
		global.Lg.Warn("⚠️没有1分钟K线数据，跳过聚合生成")
		return nil, nil
	}

	fmt.Printf("📊 实际数据时间范围: %s 到 %s\n",
		time.Unix(minTime, 0).Format("2006-01-02 15:04:05"),
		time.Unix(maxTime, 0).Format("2006-01-02 15:04:05"))

	// 使用实际的数据时间范围进行聚合
	err = GenerateAggregatedKlines(symbol, minTime, maxTime, timeZone)
	if err != nil {
		global.Lg.Error("❌聚合K线数据失败", zap.Error(err))
		return nil, err
	}

	global.Lg.Debug("✅ 所有时间颗粒的K线数据补齐完成")
	if len(progress.Errors) > 0 {
		// 如果有错误，打印错误数量
		global.Lg.Warn("⚠️ 补齐过程中出现错误", zap.Int("错误数量", len(progress.Errors)))
	}
	return nil, nil
}

// 聚合生成其他时间颗粒的K线数据
func GenerateAggregatedKlines(symbol string, startTimeUnix, endTimeUnix int64, timeZone string) error {
	global.Lg.Debug("🔄 开始聚合生成K线", zap.String("symbol", symbol),
		zap.String("startTime", time.Unix(startTimeUnix, 0).Format("2006-01-02 15:04:05")),
		zap.String("endTime", time.Unix(endTimeUnix, 0).Format("2006-01-02 15:04:05")))

	resolutions := []struct {
		resolution         string
		minutes            int64
		isUTC              bool
		needUTCInTableName bool
	}{
		{global.Resolutions.Minute5, 5, false, false},   // 5m 不需要UTC
		{global.Resolutions.Minute15, 15, false, false}, // 15m 不需要UTC
		{global.Resolutions.Minute30, 30, false, true},  // 30m 需要UTC
		{global.Resolutions.Hour1, 60, false, true},     // 1h 需要UTC
		{global.Resolutions.Hour4, 240, false, true},    // 4h 需要UTC
		{global.Resolutions.Day, 1440, true, true},      // 日K需要UTC0对齐
		{global.Resolutions.Week, 10080, true, true},    // 周K需要UTC0对齐，7*24*60=10080分钟
		{global.Resolutions.Month, 0, true, true},       // 月K需要UTC0对齐，使用特殊处理
	}

	// 使用 WaitGroup 而不是 errgroup，避免并发控制问题
	var wg sync.WaitGroup
	errChan := make(chan error, len(resolutions))
	semaphore := make(chan struct{}, 3) // 限制并发数为3

	for _, res := range resolutions {
		wg.Add(1)
		go func(r struct {
			resolution         string
			minutes            int64
			isUTC              bool
			needUTCInTableName bool
		}) {
			defer wg.Done()

			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			global.Lg.Info("🔄 开始生成 %s K线数据...", zap.String("symbol", symbol), zap.String("resolution", r.resolution), zap.Int64("minutes", r.minutes), zap.Bool("isUTC", r.isUTC))
			err := generateKlineForResolution(symbol, startTimeUnix, endTimeUnix, timeZone, r.resolution, r.minutes, r.isUTC, r.needUTCInTableName)
			if err != nil {
				errChan <- fmt.Errorf("生成 %s K线失败: %w", r.resolution, err)
				global.Lg.Error("❌ K线数据生成失败", zap.String("resolution", r.resolution), zap.Error(err))
			} else {
				global.Lg.Info("✅ K线数据生成完成", zap.String("resolution", r.resolution))
			}
		}(res)
	}

	// 等待所有goroutine完成
	wg.Wait()
	close(errChan)

	// 检查是否有错误
	var errors []error
	for err := range errChan {
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return fmt.Errorf("聚合过程中出现 %d 个错误: %v", len(errors), errors[0])
	}

	return nil
}

// 为特定时间颗粒生成K线数据
func generateKlineForResolution(symbol string, startTimeUnix, endTimeUnix int64, timeZone, resolution string, minutes int64, isUTC bool, needUTCInTableName bool) error {
	fmt.Printf("  🔍 生成 %s K线，时间范围: %s 到 %s\n",
		resolution,
		time.Unix(startTimeUnix, 0).Format("2006-01-02 15:04:05"),
		time.Unix(endTimeUnix, 0).Format("2006-01-02 15:04:05"))

	// 转换resolution格式以匹配实际表名
	tableResolution := resolution
	switch resolution {
	case "1": // 1分钟
		tableResolution = "1m"
	case "5": // 5分钟
		tableResolution = "5m"
	case "15": // 15分钟
		tableResolution = "15m"
	case "30": // 30分钟
		tableResolution = "30m"
	case "60": // 1小时
		tableResolution = "1h"
	case "240": // 4小时
		tableResolution = "4h"
	case "D": // 日线，保持大写
		tableResolution = "D"
	case "W": // 周线，保持大写
		tableResolution = "W"
	case "M": // 月线，保持大写
		tableResolution = "M"
	}

	// 获取1分钟K线数据
	// 1分钟数据的表名格式固定为 kline_platform_symbol_1m
	minute1Table := function.GetInfluxDBTable(DATABENTO, symbol, "", "1m")

	// 根据needUTCInTableName生成正确的目标表名
	var targetTable string
	if needUTCInTableName {
		// 30m/1h/4h/D/W/M 使用UTC时区
		targetTable = function.GetInfluxDBTable(DATABENTO, symbol, "utc", tableResolution)
	} else {
		// 1m/5m/15m 不使用UTC时区
		targetTable = function.GetInfluxDBTable(DATABENTO, symbol, timeZone, tableResolution)
	}

	// 先清空目标表的数据（避免重复）
	// fmt.Printf("  🗑️  清理 %s 表中的旧数据...\n", targetTable)
	// err := influxdb.DeleteDataByTimeRange(targetTable, startTimeUnix, endTimeUnix)
	// if err != nil {
	// 	fmt.Printf("  ⚠️  清理旧数据失败（可能表不存在）: %v\n", err)
	// }

	// 分批处理，每批7天
	batchSize := int64(7 * 24 * 60 * 60) // 7天的秒数
	currentStart := startTimeUnix

	var allAggregated []structs.AllDataKline
	totalBatches := 0

	for currentStart < endTimeUnix {
		currentEnd := currentStart + batchSize
		if currentEnd > endTimeUnix {
			currentEnd = endTimeUnix
		}

		totalBatches++
		fmt.Printf("  📦 处理第 %d 批数据 [%s - %s]\n",
			totalBatches,
			time.Unix(currentStart, 0).Format("01-02 15:04"),
			time.Unix(currentEnd, 0).Format("01-02 15:04"))

		// 查询批次数据
		minute1Data, err := influxdb.QueryKlineData(minute1Table, currentStart, currentEnd)
		if err != nil {
			return fmt.Errorf("查询1分钟K线数据失败 [%d-%d]: %w", currentStart, currentEnd, err)
		}

		fmt.Printf("    查询到 %d 条1分钟数据\n", len(minute1Data))

		if len(minute1Data) == 0 {
			currentStart = currentEnd
			continue
		}

		// 聚合批次数据
		var batchAggregated []structs.AllDataKline
		if isUTC {
			batchAggregated = aggregateKlineDataUTC(minute1Data, resolution)
		} else {
			batchAggregated = aggregateKlineDataRegular(minute1Data, minutes)
		}

		fmt.Printf("    聚合生成 %d 条 %s 数据\n", len(batchAggregated), resolution)
		allAggregated = append(allAggregated, batchAggregated...)
		currentStart = currentEnd
	}

	if len(allAggregated) == 0 {
		// 如果没有生成任何数据，直接返回
		global.Lg.Info("⚠️ 没有生成任何K线数据", zap.String("resolution", resolution), zap.String("symbol", symbol))
		return nil
	}

	global.Lg.Debug("准备写入K线数据",
		zap.Int("count", len(allAggregated)),
		zap.String("resolution", resolution),
		zap.String("table", targetTable))

	// 写入聚合后的数据
	err := writeBatchedKlineData(targetTable, allAggregated, fmt.Sprintf("aggregate_%s", resolution))
	if err != nil {
		return fmt.Errorf("写入聚合K线数据失败: %w", err)
	}

	global.Lg.Debug("✅ 成功写入K线数据",
		zap.Int("count", len(allAggregated)),
		zap.String("resolution", resolution),
		zap.String("table", targetTable))

	return nil
}

// 普通时间颗粒聚合（分钟、小时级别）
func aggregateKlineDataRegular(minute1Data []structs.AllDataKline, intervalMinutes int64) []structs.AllDataKline {
	if len(minute1Data) == 0 {
		return nil
	}

	var aggregatedData []structs.AllDataKline
	intervalSeconds := intervalMinutes * 60

	// 按时间排序
	sort.Slice(minute1Data, func(i, j int) bool {
		return minute1Data[i].Time < minute1Data[j].Time
	})

	// 使用map来按时间间隔分组
	intervalMap := make(map[int64][]structs.AllDataKline)

	// 将数据按时间间隔分组
	for _, kline := range minute1Data {
		// 计算当前K线所属的时间间隔起始时间
		intervalStart := (kline.Time / intervalSeconds) * intervalSeconds
		intervalMap[intervalStart] = append(intervalMap[intervalStart], kline)
	}

	// 聚合每个时间间隔的数据
	for intervalStart, klines := range intervalMap {
		if len(klines) == 0 {
			continue
		}

		// 对该时间间隔内的K线按时间排序
		sort.Slice(klines, func(i, j int) bool {
			return klines[i].Time < klines[j].Time
		})

		// 聚合该时间间隔的数据
		aggregated := structs.AllDataKline{
			Platform: klines[0].Platform,
			Symbol:   klines[0].Symbol,
			Time:     intervalStart,
			Open:     klines[0].Open,              // 第一条K线的开盘价
			Close:    klines[len(klines)-1].Close, // 最后一条K线的收盘价
			High:     klines[0].High,
			Low:      klines[0].Low,
			Volume:   0,
			Turnover: 0,
		}

		// 计算最高价、最低价和累计成交量
		for _, kline := range klines {
			if kline.High > aggregated.High {
				aggregated.High = kline.High
			}
			if kline.Low < aggregated.Low {
				aggregated.Low = kline.Low
			}
			aggregated.Volume += kline.Volume
			aggregated.Turnover += kline.Turnover
		}

		aggregatedData = append(aggregatedData, aggregated)
	}

	// 按时间排序
	sort.Slice(aggregatedData, func(i, j int) bool {
		return aggregatedData[i].Time < aggregatedData[j].Time
	})

	return aggregatedData
}

// UTC时区对齐的K线聚合（日K、周K、月K）
func aggregateKlineDataUTC(minute1Data []structs.AllDataKline, resolution string) []structs.AllDataKline {
	if len(minute1Data) == 0 {
		return nil
	}

	// 按时间排序
	sort.Slice(minute1Data, func(i, j int) bool {
		return minute1Data[i].Time < minute1Data[j].Time
	})

	var aggregatedData []structs.AllDataKline
	intervalMap := make(map[int64]*structs.AllDataKline)

	for _, kline := range minute1Data {
		var intervalStart int64

		// 根据不同类型计算UTC时区对齐的时间间隔
		switch resolution {
		case global.Resolutions.Day:
			// 日K：按UTC0的自然日对齐（00:00:00 UTC）
			utcTime := time.Unix(kline.Time, 0).UTC()
			dayStart := time.Date(utcTime.Year(), utcTime.Month(), utcTime.Day(), 0, 0, 0, 0, time.UTC)
			intervalStart = dayStart.Unix()

		case global.Resolutions.Week:
			// 周K：按UTC0的周一00:00:00对齐
			utcTime := time.Unix(kline.Time, 0).UTC()
			// 计算本周周一
			weekday := int(utcTime.Weekday())
			if weekday == 0 { // Sunday
				weekday = 7
			}
			daysToSubtract := weekday - 1 // 距离周一的天数
			weekStart := time.Date(utcTime.Year(), utcTime.Month(), utcTime.Day()-daysToSubtract, 0, 0, 0, 0, time.UTC)
			intervalStart = weekStart.Unix()

		case global.Resolutions.Month:
			// 月K：按UTC0的月初00:00:00对齐
			utcTime := time.Unix(kline.Time, 0).UTC()
			monthStart := time.Date(utcTime.Year(), utcTime.Month(), 1, 0, 0, 0, 0, time.UTC)
			intervalStart = monthStart.Unix()

		default:
			// 其他情况，按秒对齐（不应该到这里）
			intervalStart = kline.Time
		}

		// 检查是否已有该时间间隔的数据
		if existingInterval, exists := intervalMap[intervalStart]; exists {
			// 聚合数据
			existingInterval.Close = kline.Close // 更新收盘价
			if kline.High > existingInterval.High {
				existingInterval.High = kline.High
			}
			if kline.Low < existingInterval.Low {
				existingInterval.Low = kline.Low
			}
			existingInterval.Volume += kline.Volume
			existingInterval.Turnover += kline.Turnover
		} else {
			// 创建新的时间间隔数据
			newInterval := structs.AllDataKline{
				Platform: kline.Platform,
				Symbol:   kline.Symbol,
				Time:     intervalStart,
				Open:     kline.Open,
				Close:    kline.Close,
				High:     kline.High,
				Low:      kline.Low,
				Volume:   kline.Volume,
				Turnover: kline.Turnover,
			}
			intervalMap[intervalStart] = &newInterval
		}
	}

	// 将map转换为slice并按时间排序
	for _, interval := range intervalMap {
		aggregatedData = append(aggregatedData, *interval)
	}

	sort.Slice(aggregatedData, func(i, j int) bool {
		return aggregatedData[i].Time < aggregatedData[j].Time
	})

	return aggregatedData
}

// 批量写入K线数据
func writeBatchedKlineData(table string, data []structs.AllDataKline, tag string) error {
	totalCount := len(data)
	if totalCount == 0 {
		return nil
	}

	writtenCount := 0

	for i := 0; i < len(data); i += maxBatchSize {
		end := i + maxBatchSize
		if end > len(data) {
			end = len(data)
		}

		batch := data[i:end]
		if err := influxdb.BatchWriteKlineData(table, batch, tag); err != nil {
			return fmt.Errorf("写入批次 %d-%d 失败: %w", i, end, err)
		}

		writtenCount += len(batch)
		if totalCount > maxBatchSize {
			// 打印写入进度
			global.Lg.Debug("写入进度",
				zap.Int("已写入", writtenCount),
				zap.Int("总数量", totalCount),
				zap.Float64("完成百分比", float64(writtenCount)/float64(totalCount)*100))
		}
	}
	return nil
}
