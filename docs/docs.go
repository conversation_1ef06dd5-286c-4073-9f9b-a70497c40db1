// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/admin/User/User": {
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/用户管理"
                ],
                "summary": "修改用户",
                "parameters": [
                    {
                        "description": "参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/user_model.PutUserRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/用户管理"
                ],
                "summary": "添加用户",
                "parameters": [
                    {
                        "description": "参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/service_admin.UserAddRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/用户管理"
                ],
                "summary": "删除用户",
                "parameters": [
                    {
                        "description": "参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/service_admin.UserIDRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/build_google_totp": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/个人操作"
                ],
                "summary": "生成谷歌令牌密钥",
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/service_admin.GetBuildGoogleTotpResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/个人操作"
                ],
                "summary": "提交谷歌令牌密钥",
                "parameters": [
                    {
                        "description": "验证请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/service_admin.PostBuildGoogleTotpRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/login": {
            "post": {
                "description": "使用邮箱、用户名和密码登录。账号 (username) 和邮箱 (email) 必须至少填写其中一个。",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/个人操作"
                ],
                "summary": "登录",
                "parameters": [
                    {
                        "description": "登录请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/service_admin.LoginRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/service_admin.LoginResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/logout": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/个人操作"
                ],
                "summary": "退出登录",
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/reset_password": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/个人操作"
                ],
                "summary": "修改密码",
                "parameters": [
                    {
                        "description": "验证请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/service_admin.ResetPasswordRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/system/perm": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "无搜索条件。",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/权限管理"
                ],
                "summary": "权限列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "页码 可空 默认为1",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页显示数量 可空 默认值由项目启动参数决定",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/service_admin.SysPermListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/权限管理"
                ],
                "summary": "修改权限",
                "parameters": [
                    {
                        "description": "参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/service_admin.SysPermUpateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/权限管理"
                ],
                "summary": "添加权限",
                "parameters": [
                    {
                        "description": "参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/service_admin.SysPermAddRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/权限管理"
                ],
                "summary": "删除权限",
                "parameters": [
                    {
                        "description": "参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/service_admin.SysPermIDRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/system/role": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "无搜索条件。",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/角色管理"
                ],
                "summary": "角色列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "页码 可空 默认为1",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页显示数量 可空 默认值由项目启动参数决定",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/service_admin.SysRoleListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/角色管理"
                ],
                "summary": "修改角色",
                "parameters": [
                    {
                        "description": "参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/service_admin.SysRoleUpateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/角色管理"
                ],
                "summary": "添加角色",
                "parameters": [
                    {
                        "description": "参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/service_admin.SysRoleAddRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/角色管理"
                ],
                "summary": "删除角色",
                "parameters": [
                    {
                        "description": "参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/service_admin.SysRoleIDRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/system/role/perm_list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/角色管理"
                ],
                "summary": "获取角色权限列表",
                "parameters": [
                    {
                        "minimum": 1,
                        "type": "integer",
                        "description": "角色ID",
                        "name": "id",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/service_admin.SysRolePermListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/system/role/perm_set": {
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/角色管理"
                ],
                "summary": "修改角色权限",
                "parameters": [
                    {
                        "description": "参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/service_admin.SysRolePermSetRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/system/siteconfig": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "无搜索条件。",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/站点管理"
                ],
                "summary": "站点配置列表",
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/service_admin.SiteConfigListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/站点管理"
                ],
                "summary": "修改站点配置",
                "parameters": [
                    {
                        "description": "参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/service_admin.SiteConfigUpateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/system/user": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "无搜索条件。",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/系统用户管理"
                ],
                "summary": "系统用户列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "页码 可空 默认为1",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页显示数量 可空 默认值由项目启动参数决定",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/service_admin.SysUserListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/系统用户管理"
                ],
                "summary": "修改系统用户",
                "parameters": [
                    {
                        "description": "参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/service_admin.SysUserUpateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/系统用户管理"
                ],
                "summary": "添加系统用户",
                "parameters": [
                    {
                        "description": "参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/service_admin.SysUserAddRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/系统用户管理"
                ],
                "summary": "删除系统用户",
                "parameters": [
                    {
                        "description": "参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/service_admin.SysUserIDRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/system/user/reset_gs": {
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/系统用户管理"
                ],
                "summary": "重置系统用户谷歌密钥",
                "parameters": [
                    {
                        "description": "参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/service_admin.SysUserIDRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/system/user/role_list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/系统用户管理"
                ],
                "summary": "获取系统用户角色列表",
                "parameters": [
                    {
                        "minimum": 1,
                        "type": "integer",
                        "description": "后台用户ID",
                        "name": "id",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/service_admin.SysUserRoleListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/system/user/role_set": {
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/系统用户管理"
                ],
                "summary": "修改系统用户角色",
                "parameters": [
                    {
                        "description": "参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/service_admin.SysUserRoleSetRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/system/user/status": {
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/系统用户管理"
                ],
                "summary": "更改系统用户状态",
                "parameters": [
                    {
                        "description": "参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/service_admin.SysUserStatusRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/tick/kline_history": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "无搜索条件。",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/产品管理"
                ],
                "summary": "产品列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "产品标识 Symbol",
                        "name": "code",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "数量",
                        "name": "count",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "k线的最大时间",
                        "name": "kline_timestamp_end",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "k线类型 1 三方数据 2 本方数据 3 本方数据+用户插针",
                        "name": "kline_type",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "时间分辨率",
                        "name": "resolution",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "用户ID",
                        "name": "user_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/structs.Response"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/tick/tick": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "无搜索条件。",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/产品管理"
                ],
                "summary": "产品列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID",
                        "name": "id",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页码 可空 默认为1",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页显示数量 可空 默认值由项目启动参数决定",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "平台",
                        "name": "platform",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "产品标识",
                        "name": "symbol",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "类型",
                        "name": "type",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/structs.Response"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/产品管理"
                ],
                "summary": "修改产品",
                "parameters": [
                    {
                        "description": "参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/service_admin.TickUpateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/产品管理"
                ],
                "summary": "添加产品",
                "parameters": [
                    {
                        "description": "参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/service_admin.TickAddRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/产品管理"
                ],
                "summary": "删除产品",
                "parameters": [
                    {
                        "description": "参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/service_admin.TickIDRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/tick/tick_log": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "无搜索条件。",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/产品管理"
                ],
                "summary": "产品列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID",
                        "name": "id",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "是否为一分钟的开始",
                        "name": "is_open",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "类型 1 上下两条k线产生的误差超过设定范围 2 插针记录 上条为插针改变的 下条为原始未改变的",
                        "name": "log_type",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页码 可空 默认为1",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页显示数量 可空 默认值由项目启动参数决定",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "平台",
                        "name": "platform",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "时间分辨率",
                        "name": "resolutions",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "插针ID",
                        "name": "shadow_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "产品标识",
                        "name": "symbol",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/structs.Response"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/tick/tick_reset": {
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/产品管理"
                ],
                "summary": "重置缓存",
                "parameters": [
                    {
                        "description": "参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/service_admin.TickResetRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/tick/view": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "无搜索条件。",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/产品管理"
                ],
                "summary": "产品详情",
                "parameters": [
                    {
                        "minimum": 1,
                        "type": "integer",
                        "description": "用户ID",
                        "name": "id",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/structs.Response"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/upload_file": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/上传管理"
                ],
                "summary": "上传文件",
                "parameters": [
                    {
                        "type": "file",
                        "description": "上传的文件",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/service_common.CommonUriResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/upload_image": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/上传管理"
                ],
                "summary": "上传图片",
                "parameters": [
                    {
                        "type": "file",
                        "description": "上传的图片",
                        "name": "image",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/service_common.CommonUriResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/user/user": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "无搜索条件。",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/用户管理"
                ],
                "summary": "用户列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID",
                        "name": "id",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页码 可空 默认为1",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页显示数量 可空 默认值由项目启动参数决定",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "用户名",
                        "name": "username",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/service_admin.UserListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/user/view": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "无搜索条件。",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/用户管理"
                ],
                "summary": "用户详情",
                "parameters": [
                    {
                        "minimum": 1,
                        "type": "integer",
                        "description": "用户ID",
                        "name": "id",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/service_admin.UserViewResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/verify_google_totp": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "后台/个人操作"
                ],
                "summary": "重新验证谷歌令牌",
                "parameters": [
                    {
                        "description": "验证请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/service_admin.PostVerifyGoogleTotpRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/connection_delivery": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "无搜索条件。",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "api/用户管理"
                ],
                "summary": "连接请求",
                "parameters": [
                    {
                        "type": "string",
                        "description": "token",
                        "name": "token",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "用户ID",
                        "name": "user_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "你的服务器地址",
                        "name": "your_sever",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "你的验证token",
                        "name": "your_token",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/kline_history": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "无搜索条件。",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "api/历史k线"
                ],
                "summary": "批量获取历史k线",
                "parameters": [
                    {
                        "type": "string",
                        "description": "产品标识 Symbol",
                        "name": "code",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "数量",
                        "name": "count",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "截止时间",
                        "name": "kline_timestamp_end",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "时间分辨率",
                        "name": "resolution",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "token",
                        "name": "token",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "用户ID",
                        "name": "user_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/kline_shadow": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "无搜索条件。",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "api/k线插针"
                ],
                "summary": "k线插针列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "页码 可空 默认为1",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页显示数量 可空 默认值由项目启动参数决定",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "token",
                        "name": "token",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "用户ID",
                        "name": "user_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/service_api.KlineShadowListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "api/k线插针"
                ],
                "summary": "添加k线插针",
                "parameters": [
                    {
                        "description": "参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/service_api.KlineShadowAddRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "api/k线插针"
                ],
                "summary": "删除k线插针",
                "parameters": [
                    {
                        "description": "参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/service_api.KlineShadowIDRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/api/kline_shadow/fix": {
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "api/k线插针"
                ],
                "summary": "删除k线插针清空",
                "parameters": [
                    {
                        "description": "参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/service_api.KlineShadowSymbolRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回码看对照表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/structs.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "service_admin.GetBuildGoogleTotpResponse": {
            "type": "object",
            "properties": {
                "google_secret": {
                    "description": "谷歌验证器密钥",
                    "type": "string"
                }
            }
        },
        "service_admin.LoginRequest": {
            "type": "object",
            "required": [
                "password"
            ],
            "properties": {
                "email": {
                    "description": "邮箱 和账号必填写一项",
                    "type": "string"
                },
                "google_code": {
                    "description": "谷歌验证码",
                    "type": "string"
                },
                "password": {
                    "description": "密码",
                    "type": "string"
                },
                "username": {
                    "description": "账号 和邮箱必填写一项",
                    "type": "string"
                }
            }
        },
        "service_admin.LoginResponse": {
            "type": "object",
            "properties": {
                "need_build_totp": {
                    "description": "是否需要绑定谷歌验证器",
                    "type": "boolean"
                },
                "permissions": {
                    "description": "权限列表",
                    "type": "string"
                },
                "user": {
                    "description": "用户数据",
                    "allOf": [
                        {
                            "$ref": "#/definitions/structs.SysUser"
                        }
                    ]
                }
            }
        },
        "service_admin.OperationsRequest": {
            "type": "object",
            "required": [
                "operation"
            ],
            "properties": {
                "operation": {
                    "description": "操作类型",
                    "type": "string",
                    "enum": [
                        "yaml",
                        "user",
                        "tick"
                    ]
                }
            }
        },
        "service_admin.PostBuildGoogleTotpRequest": {
            "type": "object",
            "required": [
                "google_code",
                "google_secret"
            ],
            "properties": {
                "google_code": {
                    "description": "验证码",
                    "type": "string"
                },
                "google_secret": {
                    "description": "谷歌验证器密钥",
                    "type": "string"
                }
            }
        },
        "service_admin.PostVerifyGoogleTotpRequest": {
            "type": "object",
            "required": [
                "google_code"
            ],
            "properties": {
                "google_code": {
                    "description": "验证码",
                    "type": "string"
                }
            }
        },
        "service_admin.ResetPasswordRequest": {
            "type": "object",
            "required": [
                "google_code",
                "new_pass",
                "old_pass"
            ],
            "properties": {
                "google_code": {
                    "description": "验证码",
                    "type": "string"
                },
                "new_pass": {
                    "description": "新密码",
                    "type": "string",
                    "maxLength": 20,
                    "minLength": 6
                },
                "old_pass": {
                    "description": "旧密码",
                    "type": "string",
                    "maxLength": 20,
                    "minLength": 6
                }
            }
        },
        "service_admin.SiteConfigListResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "description": "配置数据",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/structs.SiteConfigGroup"
                    }
                }
            }
        },
        "service_admin.SiteConfigUpateRequest": {
            "type": "object",
            "properties": {
                "configs": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/service_admin.SiteConfigUpateRequestList"
                    }
                }
            }
        },
        "service_admin.SiteConfigUpateRequestList": {
            "type": "object",
            "required": [
                "config_value",
                "id"
            ],
            "properties": {
                "config_value": {
                    "description": "值",
                    "type": "string"
                },
                "id": {
                    "description": "ID",
                    "type": "integer",
                    "minimum": 1
                }
            }
        },
        "service_admin.SysPermAddRequest": {
            "type": "object",
            "required": [
                "name",
                "resource",
                "type"
            ],
            "properties": {
                "description": {
                    "description": "备注",
                    "type": "string"
                },
                "name": {
                    "description": "名称",
                    "type": "string"
                },
                "parent_id": {
                    "description": "父级权限ID，0表示顶级",
                    "type": "integer",
                    "minimum": 0
                },
                "resource": {
                    "description": "资源",
                    "type": "string"
                },
                "sort_order": {
                    "description": "排序字段",
                    "type": "integer"
                },
                "type": {
                    "description": "menu 或 action",
                    "type": "string",
                    "enum": [
                        "menu",
                        "action"
                    ]
                }
            }
        },
        "service_admin.SysPermIDRequest": {
            "type": "object",
            "required": [
                "id"
            ],
            "properties": {
                "id": {
                    "description": "ID",
                    "type": "integer",
                    "minimum": 1
                }
            }
        },
        "service_admin.SysPermListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "description": "权限列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/structs.SysPermission"
                    }
                },
                "page": {
                    "description": "返回当前页码",
                    "type": "integer"
                },
                "page_size": {
                    "description": "返回当前页码数量",
                    "type": "integer"
                },
                "total": {
                    "description": "总数量",
                    "type": "integer"
                }
            }
        },
        "service_admin.SysPermUpateRequest": {
            "type": "object",
            "required": [
                "id",
                "name",
                "resource",
                "type"
            ],
            "properties": {
                "description": {
                    "description": "备注",
                    "type": "string"
                },
                "id": {
                    "description": "ID",
                    "type": "integer",
                    "minimum": 1
                },
                "name": {
                    "description": "名称",
                    "type": "string"
                },
                "parent_id": {
                    "description": "父级权限ID，0表示顶级",
                    "type": "integer",
                    "minimum": 0
                },
                "resource": {
                    "description": "资源",
                    "type": "string"
                },
                "sort_order": {
                    "description": "排序字段",
                    "type": "integer"
                },
                "type": {
                    "description": "menu 或 action",
                    "type": "string",
                    "enum": [
                        "menu",
                        "action"
                    ]
                }
            }
        },
        "service_admin.SysRoleAddRequest": {
            "type": "object",
            "required": [
                "name"
            ],
            "properties": {
                "description": {
                    "description": "备注",
                    "type": "string"
                },
                "name": {
                    "description": "名称",
                    "type": "string"
                },
                "sort_order": {
                    "description": "排序字段",
                    "type": "integer"
                }
            }
        },
        "service_admin.SysRoleIDRequest": {
            "type": "object",
            "required": [
                "id"
            ],
            "properties": {
                "id": {
                    "description": "角色ID",
                    "type": "integer",
                    "minimum": 1
                }
            }
        },
        "service_admin.SysRoleListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "description": "角色列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/structs.SysRole"
                    }
                },
                "page": {
                    "description": "返回当前页码",
                    "type": "integer"
                },
                "page_size": {
                    "description": "返回当前页码数量",
                    "type": "integer"
                },
                "total": {
                    "description": "总数量",
                    "type": "integer"
                }
            }
        },
        "service_admin.SysRolePermListResponse": {
            "type": "object",
            "properties": {
                "sys_role_permission": {
                    "description": "角色已有的权限列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/structs.SysPermission"
                    }
                }
            }
        },
        "service_admin.SysRolePermSetRequest": {
            "type": "object",
            "required": [
                "id",
                "permission_ids"
            ],
            "properties": {
                "id": {
                    "description": "角色ID",
                    "type": "integer",
                    "minimum": 1
                },
                "permission_ids": {
                    "description": "权限ID数组",
                    "type": "array",
                    "minItems": 1,
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "service_admin.SysRoleUpateRequest": {
            "type": "object",
            "required": [
                "id",
                "name"
            ],
            "properties": {
                "description": {
                    "description": "备注",
                    "type": "string"
                },
                "id": {
                    "description": "ID",
                    "type": "integer",
                    "minimum": 1
                },
                "name": {
                    "description": "名称",
                    "type": "string"
                },
                "sort_order": {
                    "description": "排序字段",
                    "type": "integer"
                }
            }
        },
        "service_admin.SysUserAddRequest": {
            "type": "object",
            "required": [
                "email",
                "password",
                "username"
            ],
            "properties": {
                "email": {
                    "description": "邮箱",
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 6
                },
                "password": {
                    "description": "密码",
                    "type": "string",
                    "maxLength": 20,
                    "minLength": 6
                },
                "status": {
                    "description": "状态 0-1",
                    "type": "integer",
                    "maximum": 1,
                    "minimum": 0
                },
                "username": {
                    "description": "账号",
                    "type": "string",
                    "maxLength": 20,
                    "minLength": 6
                }
            }
        },
        "service_admin.SysUserIDRequest": {
            "type": "object",
            "required": [
                "id"
            ],
            "properties": {
                "id": {
                    "description": "ID",
                    "type": "integer",
                    "minimum": 1
                }
            }
        },
        "service_admin.SysUserListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "description": "系统用户列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/structs.SysUser"
                    }
                },
                "page": {
                    "description": "返回当前页码",
                    "type": "integer"
                },
                "page_size": {
                    "description": "返回当前页码数量",
                    "type": "integer"
                },
                "total": {
                    "description": "总数量",
                    "type": "integer"
                }
            }
        },
        "service_admin.SysUserRoleListResponse": {
            "type": "object",
            "properties": {
                "sys_user_role": {
                    "description": "系统用户已有角色列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/structs.SysRole"
                    }
                }
            }
        },
        "service_admin.SysUserRoleSetRequest": {
            "type": "object",
            "required": [
                "id",
                "role_ids"
            ],
            "properties": {
                "id": {
                    "description": "ID",
                    "type": "integer",
                    "minimum": 1
                },
                "role_ids": {
                    "description": "角色ID数组",
                    "type": "array",
                    "minItems": 1,
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "service_admin.SysUserStatusRequest": {
            "type": "object",
            "required": [
                "id"
            ],
            "properties": {
                "id": {
                    "description": "ID",
                    "type": "integer",
                    "minimum": 1
                },
                "status": {
                    "description": "状态 0-1",
                    "type": "integer",
                    "maximum": 1,
                    "minimum": 0
                }
            }
        },
        "service_admin.SysUserUpateRequest": {
            "type": "object",
            "required": [
                "id"
            ],
            "properties": {
                "email": {
                    "description": "邮箱",
                    "type": "string"
                },
                "id": {
                    "description": "ID",
                    "type": "integer",
                    "minimum": 1
                },
                "password": {
                    "description": "密码 不修改则留空",
                    "type": "string"
                },
                "status": {
                    "description": "状态 0-1",
                    "type": "integer",
                    "maximum": 1,
                    "minimum": 0
                },
                "username": {
                    "description": "账号",
                    "type": "string"
                }
            }
        },
        "service_admin.TickAddRequest": {
            "type": "object",
            "properties": {
                "available_platforms": {
                    "description": "可选择平台(可用平台)",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "default_decimal_places": {
                    "description": "默认小数位数",
                    "type": "integer"
                },
                "designated_time": {
                    "description": "指定最早初始时间",
                    "type": "integer"
                },
                "maximum": {
                    "description": "最大更新数据量上限",
                    "type": "integer"
                },
                "platform": {
                    "description": "平台",
                    "type": "string"
                },
                "replace_symbol": {
                    "description": "替换标识",
                    "type": "string"
                },
                "status": {
                    "description": "状态 0禁用 1启用",
                    "type": "integer",
                    "maximum": 1,
                    "minimum": 0
                },
                "sub_type": {
                    "description": "子类型",
                    "type": "string"
                },
                "symbol": {
                    "description": "标识",
                    "type": "string"
                },
                "symbolname": {
                    "description": "产品名",
                    "type": "string"
                },
                "time_zone": {
                    "description": "时区",
                    "type": "string"
                },
                "type": {
                    "description": "类型",
                    "type": "string"
                }
            }
        },
        "service_admin.TickIDRequest": {
            "type": "object",
            "required": [
                "id"
            ],
            "properties": {
                "id": {
                    "description": "用户ID",
                    "type": "integer",
                    "minimum": 1
                }
            }
        },
        "service_admin.TickResetRequest": {
            "type": "object"
        },
        "service_admin.TickUpateRequest": {
            "type": "object",
            "required": [
                "id"
            ],
            "properties": {
                "default_decimal_places": {
                    "description": "默认小数位数",
                    "type": "integer"
                },
                "designated_time": {
                    "description": "指定最早初始时间",
                    "type": "integer"
                },
                "id": {
                    "description": "ID",
                    "type": "integer",
                    "minimum": 1
                },
                "maximum": {
                    "description": "最大更新数据量上限",
                    "type": "integer"
                },
                "platform": {
                    "description": "平台",
                    "type": "string"
                },
                "replace_symbol": {
                    "description": "替换标识",
                    "type": "string"
                },
                "status": {
                    "description": "状态 0禁用 1启用",
                    "type": "integer",
                    "maximum": 1,
                    "minimum": 0
                },
                "sub_type": {
                    "description": "子类型",
                    "type": "string"
                },
                "symbol": {
                    "description": "标识",
                    "type": "string"
                },
                "symbolname": {
                    "description": "产品名",
                    "type": "string"
                },
                "time_zone": {
                    "description": "时区",
                    "type": "string"
                },
                "type": {
                    "description": "类型",
                    "type": "string"
                }
            }
        },
        "service_admin.UserAddRequest": {
            "type": "object",
            "required": [
                "password",
                "token",
                "username"
            ],
            "properties": {
                "password": {
                    "description": "密码",
                    "type": "string"
                },
                "status": {
                    "description": "状态 0禁用 1启用",
                    "type": "integer",
                    "maximum": 1,
                    "minimum": 0
                },
                "token": {
                    "description": "token",
                    "type": "string"
                },
                "username": {
                    "description": "账号",
                    "type": "string"
                }
            }
        },
        "service_admin.UserIDRequest": {
            "type": "object",
            "required": [
                "id"
            ],
            "properties": {
                "id": {
                    "description": "用户ID",
                    "type": "integer",
                    "minimum": 1
                }
            }
        },
        "service_admin.UserListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "description": "用户列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/structs.User"
                    }
                },
                "page": {
                    "description": "返回当前页码",
                    "type": "integer"
                },
                "page_size": {
                    "description": "返回当前页码数量",
                    "type": "integer"
                },
                "total": {
                    "description": "总数量",
                    "type": "integer"
                }
            }
        },
        "service_admin.UserViewResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "description": "用户详情数据",
                    "allOf": [
                        {
                            "$ref": "#/definitions/structs.User"
                        }
                    ]
                }
            }
        },
        "service_api.KlineShadowAddRequest": {
            "type": "object",
            "required": [
                "fall_time",
                "mode",
                "peak_time",
                "rise_time",
                "spike_factor",
                "start_time",
                "symbol",
                "token",
                "type",
                "user_id"
            ],
            "properties": {
                "asks_price_max": {
                    "description": "买跌浮动值 intuitive类型下才需要传递",
                    "type": "string"
                },
                "asks_price_min": {
                    "description": "买跌浮动值 intuitive类型下才需要传递",
                    "type": "string"
                },
                "candlestick": {
                    "description": "实时k线的close值 intuitive类型下才需要传递",
                    "type": "string"
                },
                "fall_hit_max": {
                    "description": "回落期的命中最大值 范围0.01-1之间 对应百分比1%到100%",
                    "type": "string"
                },
                "fall_hit_min": {
                    "description": "回落期的命中最小值 范围0.01-1之间 对应百分比1%到100%",
                    "type": "string"
                },
                "fall_time": {
                    "description": "回落时间(秒)",
                    "type": "integer"
                },
                "fall_value_max": {
                    "description": "回落期的命中后取值范围最大值 最小0.01 无上限",
                    "type": "string"
                },
                "fall_value_min": {
                    "description": "回落期的命中后取值范围最小值 最小0.01 无上限",
                    "type": "string"
                },
                "lower_shadow_max": {
                    "description": "下影线最大值",
                    "type": "string"
                },
                "mode": {
                    "description": "插针模式 模式1: 普通模式 模式2: 缩短K线模式",
                    "type": "integer",
                    "enum": [
                        1,
                        2
                    ]
                },
                "peak_float": {
                    "description": "峰值期浮动值, 比如当前峰值价格为10,这个值设定2,则峰值期的价格范围为8-12,每次随机",
                    "type": "string"
                },
                "peak_hit_max": {
                    "description": "峰值期的命中最大值 范围0.01-1之间 对应百分比1%到100%",
                    "type": "string"
                },
                "peak_hit_min": {
                    "description": "峰值期的命中最小值 范围0.01-1之间 对应百分比1%到100%",
                    "type": "string"
                },
                "peak_time": {
                    "description": "峰值时间(秒)",
                    "type": "integer"
                },
                "peak_value_max": {
                    "description": "峰值期的命中后取值范围最大值 最小0.01 无上限",
                    "type": "string"
                },
                "peak_value_min": {
                    "description": "峰值期的命中后取值范围最小值 最小0.01 无上限",
                    "type": "string"
                },
                "rise_hit_max": {
                    "description": "爬坡期的命中最大值 范围0.01-1之间 对应百分比1%到100%",
                    "type": "string"
                },
                "rise_hit_min": {
                    "description": "爬坡期的命中最小值 范围0.01-1之间 对应百分比1%到100%",
                    "type": "string"
                },
                "rise_time": {
                    "description": "爬坡时间(秒)",
                    "type": "integer"
                },
                "rise_value_max": {
                    "description": "爬坡期的命中后取值范围最大值 最小0.01 无上限",
                    "type": "string"
                },
                "rise_value_min": {
                    "description": "爬坡期的命中后取值范围最小值 最小0.01 无上限",
                    "type": "string"
                },
                "spike_factor": {
                    "description": "插针幅度 正数为上插 负数为下插 0为非法",
                    "type": "string"
                },
                "start_time": {
                    "description": "插针开始时间",
                    "type": "integer"
                },
                "symbol": {
                    "description": "名称",
                    "type": "string"
                },
                "token": {
                    "description": "token",
                    "type": "string"
                },
                "type": {
                    "description": "类型 intuitive fluctuation",
                    "type": "string"
                },
                "upper_shadow_max": {
                    "description": "上影线最大值",
                    "type": "string"
                },
                "user_id": {
                    "description": "用户ID",
                    "type": "integer"
                }
            }
        },
        "service_api.KlineShadowIDRequest": {
            "type": "object",
            "required": [
                "id",
                "token",
                "user_id"
            ],
            "properties": {
                "del_data": {
                    "description": "是否同时删除插针的k线数据",
                    "type": "integer"
                },
                "id": {
                    "description": "ID",
                    "type": "integer",
                    "minimum": 1
                },
                "token": {
                    "description": "token",
                    "type": "string"
                },
                "user_id": {
                    "description": "用户ID",
                    "type": "integer"
                }
            }
        },
        "service_api.KlineShadowListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "description": "列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/structs.UserShadow"
                    }
                },
                "page": {
                    "description": "返回当前页码",
                    "type": "integer"
                },
                "page_size": {
                    "description": "返回当前页码数量",
                    "type": "integer"
                },
                "total": {
                    "description": "总数量",
                    "type": "integer"
                }
            }
        },
        "service_api.KlineShadowSymbolRequest": {
            "type": "object",
            "required": [
                "symbols",
                "token",
                "user_id"
            ],
            "properties": {
                "symbols": {
                    "description": "要修复的产品 多个用,分开",
                    "type": "string"
                },
                "token": {
                    "description": "token",
                    "type": "string"
                },
                "user_id": {
                    "description": "用户ID",
                    "type": "integer"
                }
            }
        },
        "service_common.CommonUriResponse": {
            "type": "object",
            "properties": {
                "uri": {
                    "description": "图片或文件访问地址",
                    "type": "string"
                }
            }
        },
        "structs.Response": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "返回值\n\t\t200 成功\n\t\t520 通用错误代码\n\t\t550 参数错误\n\t\t551 需要登录\n\t\t552 需要谷歌验证\n\t\t553 令牌生成失败\n\t\t555 密码错误\n\t\t556 权限不足\n\t\t557 设置缓存失败\n\t\t558 取权限错误\n\t\t559 mysql错误",
                    "type": "integer"
                },
                "data": {
                    "description": "返回数据"
                },
                "error": {
                    "description": "返回错误消息 如果为空参考返回消息",
                    "type": "string"
                },
                "message": {
                    "description": "返回消息",
                    "type": "string"
                }
            }
        },
        "structs.SiteConfigGroup": {
            "type": "object",
            "properties": {
                "configs": {
                    "description": "参数",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/structs.SiteConfigTree"
                    }
                },
                "group_name": {
                    "description": "分组名称",
                    "type": "string"
                }
            }
        },
        "structs.SiteConfigTree": {
            "type": "object",
            "properties": {
                "config_desc": {
                    "description": "描述",
                    "type": "string"
                },
                "config_name": {
                    "description": "配置名称",
                    "type": "string"
                },
                "config_type": {
                    "description": "类型 Input Textarea Radio Checkbox 复选框 Checkboxs 多选框 Select ImageUpload",
                    "type": "string"
                },
                "config_value": {
                    "description": "配置值",
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "title": {
                    "description": "标题",
                    "type": "string"
                },
                "title_scope": {
                    "description": "选择参数名称",
                    "type": "string"
                },
                "value_scope": {
                    "description": "选择参数",
                    "type": "string"
                }
            }
        },
        "structs.SysPermission": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "id": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "parent_id": {
                    "description": "父级权限ID，0表示顶级",
                    "type": "integer"
                },
                "resource": {
                    "type": "string"
                },
                "sort_order": {
                    "description": "排序字段",
                    "type": "integer"
                },
                "type": {
                    "description": "menu 或 action",
                    "type": "string"
                }
            }
        },
        "structs.SysRole": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "id": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "sort_order": {
                    "description": "排序字段",
                    "type": "integer"
                },
                "sys_role_permission": {
                    "description": "角色和权限多对多关系",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/structs.SysPermission"
                    }
                }
            }
        },
        "structs.SysUser": {
            "type": "object",
            "properties": {
                "created_at": {
                    "description": "自动创建时间",
                    "allOf": [
                        {
                            "$ref": "#/definitions/structs.UTCDateTime"
                        }
                    ]
                },
                "email": {
                    "description": "邮箱",
                    "type": "string"
                },
                "google_secret": {
                    "description": "GoogleSecret",
                    "type": "string"
                },
                "id": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "phone": {
                    "description": "手机号",
                    "type": "string"
                },
                "status": {
                    "description": "状态 0禁用 1启用",
                    "type": "integer"
                },
                "sys_user_role": {
                    "description": "用户和角色多对多关系",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/structs.SysRole"
                    }
                },
                "token": {
                    "description": "token",
                    "type": "string"
                },
                "updated_at": {
                    "description": "自动更新时间",
                    "allOf": [
                        {
                            "$ref": "#/definitions/structs.UTCDateTime"
                        }
                    ]
                },
                "username": {
                    "description": "账号",
                    "type": "string"
                }
            }
        },
        "structs.UTCDateTime": {
            "type": "object",
            "properties": {
                "time.Time": {
                    "type": "string"
                }
            }
        },
        "structs.User": {
            "type": "object",
            "properties": {
                "created_at": {
                    "description": "自动创建时间",
                    "allOf": [
                        {
                            "$ref": "#/definitions/structs.UTCDateTime"
                        }
                    ]
                },
                "end_duration": {
                    "type": "integer"
                },
                "id": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "kline_query_number_15m": {
                    "description": "k线可查询最多数量",
                    "type": "integer"
                },
                "kline_query_number_1day": {
                    "description": "k线可查询最多数量",
                    "type": "integer"
                },
                "kline_query_number_1hour": {
                    "description": "k线可查询最多数量",
                    "type": "integer"
                },
                "kline_query_number_1m": {
                    "description": "k线可查询最多数量",
                    "type": "integer"
                },
                "kline_query_number_1month": {
                    "description": "k线可查询最多数量",
                    "type": "integer"
                },
                "kline_query_number_1week": {
                    "description": "k线可查询最多数量",
                    "type": "integer"
                },
                "kline_query_number_2hour": {
                    "description": "k线可查询最多数量",
                    "type": "integer"
                },
                "kline_query_number_30m": {
                    "description": "k线可查询最多数量",
                    "type": "integer"
                },
                "kline_query_number_4hour": {
                    "description": "k线可查询最多数量",
                    "type": "integer"
                },
                "kline_query_number_5m": {
                    "description": "k线可查询最多数量",
                    "type": "integer"
                },
                "password": {
                    "description": "密码",
                    "type": "string"
                },
                "remark": {
                    "description": "备注",
                    "type": "string"
                },
                "start_duration": {
                    "type": "integer"
                },
                "status": {
                    "description": "状态 0禁用 1启用",
                    "type": "integer"
                },
                "symbols": {
                    "description": "用户权限(可获取的数据资源ID)",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "token": {
                    "description": "token",
                    "type": "string"
                },
                "updated_at": {
                    "description": "自动更新时间",
                    "allOf": [
                        {
                            "$ref": "#/definitions/structs.UTCDateTime"
                        }
                    ]
                },
                "user_symbols": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/structs.UserSymbol"
                    }
                },
                "username": {
                    "description": "账号",
                    "type": "string"
                }
            }
        },
        "structs.UserShadow": {
            "type": "object",
            "properties": {
                "asks_price_max": {
                    "description": "买跌浮动值",
                    "type": "string"
                },
                "asks_price_min": {
                    "description": "买跌浮动值",
                    "type": "string"
                },
                "candlestick": {
                    "description": "实时k线的close值",
                    "type": "string"
                },
                "created_at": {
                    "description": "自动创建时间",
                    "allOf": [
                        {
                            "$ref": "#/definitions/structs.UTCDateTime"
                        }
                    ]
                },
                "end_time": {
                    "description": "插针结束时间",
                    "type": "integer"
                },
                "fall_candles_info": {
                    "description": "回落期完整蜡烛图开始时间和是否需要改变",
                    "type": "object"
                },
                "fall_hit_max": {
                    "description": "回落期的命中最大值",
                    "type": "string"
                },
                "fall_hit_min": {
                    "description": "回落期的命中最小值",
                    "type": "string"
                },
                "fall_time": {
                    "description": "回落阶段时间（秒）",
                    "type": "integer"
                },
                "fall_value_max": {
                    "description": "回落期的命中后取值范围最大值",
                    "type": "string"
                },
                "fall_value_min": {
                    "description": "回落期的命中后取值范围最小值",
                    "type": "string"
                },
                "id": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "lower_shadow_max": {
                    "description": "下影线最大值",
                    "type": "string"
                },
                "peak_candles_info": {
                    "description": "峰值期完整蜡烛图开始时间和是否需要改变",
                    "type": "object"
                },
                "peak_float": {
                    "description": "峰值期的浮动值",
                    "type": "string"
                },
                "peak_hit_max": {
                    "description": "峰值期的命中最大值",
                    "type": "string"
                },
                "peak_hit_min": {
                    "description": "峰值期的命中最小值",
                    "type": "string"
                },
                "peak_time": {
                    "description": "峰值阶段时间（秒）",
                    "type": "integer"
                },
                "peak_value_max": {
                    "description": "峰值期的命中后取值范围最大值",
                    "type": "string"
                },
                "peak_value_min": {
                    "description": "峰值期的命中后取值范围最小值",
                    "type": "string"
                },
                "rise_candles_info": {
                    "description": "爬坡期完整蜡烛图开始时间和是否需要改变",
                    "type": "object"
                },
                "rise_hit_max": {
                    "description": "爬坡期的命中最大值",
                    "type": "string"
                },
                "rise_hit_min": {
                    "description": "爬坡期的命中最小值",
                    "type": "string"
                },
                "rise_time": {
                    "description": "爬坡阶段时间（秒）",
                    "type": "integer"
                },
                "rise_value_max": {
                    "description": "爬坡期的命中后取值范围最大值",
                    "type": "string"
                },
                "rise_value_min": {
                    "description": "爬坡期的命中后取值范围最小值",
                    "type": "string"
                },
                "shadow_end_time": {
                    "description": "1m插针结束时间线",
                    "type": "integer"
                },
                "shadow_start_time": {
                    "description": "1m插针开始时间线",
                    "type": "integer"
                },
                "spike_factor": {
                    "description": "插针幅度",
                    "type": "string"
                },
                "start_time": {
                    "description": "插针开始时间",
                    "type": "integer"
                },
                "status": {
                    "description": "状态 0禁用 1启用",
                    "type": "integer"
                },
                "symbol": {
                    "description": "产品标识",
                    "type": "string"
                },
                "type": {
                    "description": "插针类型 intuitive fluctuation",
                    "type": "string"
                },
                "updated_at": {
                    "description": "自动更新时间",
                    "allOf": [
                        {
                            "$ref": "#/definitions/structs.UTCDateTime"
                        }
                    ]
                },
                "upper_shadow_max": {
                    "description": "上影线最大值",
                    "type": "string"
                },
                "user_id": {
                    "description": "用户ID",
                    "type": "integer"
                }
            }
        },
        "structs.UserSymbol": {
            "type": "object",
            "properties": {
                "decimal_places": {
                    "description": "小数位数",
                    "type": "integer"
                },
                "platform": {
                    "description": "产品平台",
                    "type": "string"
                },
                "symbol": {
                    "description": "产品标识",
                    "type": "string"
                },
                "symbol_id": {
                    "description": "产品ID",
                    "type": "integer"
                },
                "type": {
                    "description": "类型",
                    "type": "string"
                },
                "user_id": {
                    "description": "用户ID",
                    "type": "integer"
                }
            }
        },
        "user_model.PutUserRequest": {
            "type": "object",
            "required": [
                "id",
                "token",
                "username"
            ],
            "properties": {
                "id": {
                    "description": "ID",
                    "type": "integer",
                    "minimum": 1
                },
                "password": {
                    "description": "密码",
                    "type": "string"
                },
                "status": {
                    "description": "状态 0禁用 1启用",
                    "type": "integer",
                    "maximum": 1,
                    "minimum": 0
                },
                "symbols": {
                    "description": "用户权限(可获取的数据资源,每一种产品需要两个 Symbol 一个 kline, 一个 orderBook)",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user_model.UserUpdateRequestSymbol"
                    }
                },
                "token": {
                    "description": "token",
                    "type": "string"
                },
                "username": {
                    "description": "账号",
                    "type": "string"
                }
            }
        },
        "user_model.UserUpdateRequestSymbol": {
            "type": "object",
            "required": [
                "type"
            ],
            "properties": {
                "decimal_places": {
                    "description": "小数位数",
                    "type": "integer"
                },
                "id": {
                    "description": "产品ID",
                    "type": "integer"
                },
                "type": {
                    "description": "注意类型必须是 orderBook 或 kline",
                    "type": "string",
                    "enum": [
                        "orderBook",
                        "kline"
                    ]
                }
            }
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "",
	Host:             "",
	BasePath:         "",
	Schemes:          []string{},
	Title:            "",
	Description:      "",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
