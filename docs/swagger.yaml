definitions:
  service_admin.GetBuildGoogleTotpResponse:
    properties:
      google_secret:
        description: 谷歌验证器密钥
        type: string
    type: object
  service_admin.LoginRequest:
    properties:
      email:
        description: 邮箱 和账号必填写一项
        type: string
      google_code:
        description: 谷歌验证码
        type: string
      password:
        description: 密码
        type: string
      username:
        description: 账号 和邮箱必填写一项
        type: string
    required:
    - password
    type: object
  service_admin.LoginResponse:
    properties:
      need_build_totp:
        description: 是否需要绑定谷歌验证器
        type: boolean
      permissions:
        description: 权限列表
        type: string
      user:
        allOf:
        - $ref: '#/definitions/structs.SysUser'
        description: 用户数据
    type: object
  service_admin.OperationsRequest:
    properties:
      operation:
        description: 操作类型
        enum:
        - yaml
        - user
        - tick
        type: string
    required:
    - operation
    type: object
  service_admin.PostBuildGoogleTotpRequest:
    properties:
      google_code:
        description: 验证码
        type: string
      google_secret:
        description: 谷歌验证器密钥
        type: string
    required:
    - google_code
    - google_secret
    type: object
  service_admin.PostVerifyGoogleTotpRequest:
    properties:
      google_code:
        description: 验证码
        type: string
    required:
    - google_code
    type: object
  service_admin.ResetPasswordRequest:
    properties:
      google_code:
        description: 验证码
        type: string
      new_pass:
        description: 新密码
        maxLength: 20
        minLength: 6
        type: string
      old_pass:
        description: 旧密码
        maxLength: 20
        minLength: 6
        type: string
    required:
    - google_code
    - new_pass
    - old_pass
    type: object
  service_admin.SiteConfigListResponse:
    properties:
      data:
        description: 配置数据
        items:
          $ref: '#/definitions/structs.SiteConfigGroup'
        type: array
    type: object
  service_admin.SiteConfigUpateRequest:
    properties:
      configs:
        items:
          $ref: '#/definitions/service_admin.SiteConfigUpateRequestList'
        type: array
    type: object
  service_admin.SiteConfigUpateRequestList:
    properties:
      config_value:
        description: 值
        type: string
      id:
        description: ID
        minimum: 1
        type: integer
    required:
    - config_value
    - id
    type: object
  service_admin.SysPermAddRequest:
    properties:
      description:
        description: 备注
        type: string
      name:
        description: 名称
        type: string
      parent_id:
        description: 父级权限ID，0表示顶级
        minimum: 0
        type: integer
      resource:
        description: 资源
        type: string
      sort_order:
        description: 排序字段
        type: integer
      type:
        description: menu 或 action
        enum:
        - menu
        - action
        type: string
    required:
    - name
    - resource
    - type
    type: object
  service_admin.SysPermIDRequest:
    properties:
      id:
        description: ID
        minimum: 1
        type: integer
    required:
    - id
    type: object
  service_admin.SysPermListResponse:
    properties:
      list:
        description: 权限列表
        items:
          $ref: '#/definitions/structs.SysPermission'
        type: array
      page:
        description: 返回当前页码
        type: integer
      page_size:
        description: 返回当前页码数量
        type: integer
      total:
        description: 总数量
        type: integer
    type: object
  service_admin.SysPermUpateRequest:
    properties:
      description:
        description: 备注
        type: string
      id:
        description: ID
        minimum: 1
        type: integer
      name:
        description: 名称
        type: string
      parent_id:
        description: 父级权限ID，0表示顶级
        minimum: 0
        type: integer
      resource:
        description: 资源
        type: string
      sort_order:
        description: 排序字段
        type: integer
      type:
        description: menu 或 action
        enum:
        - menu
        - action
        type: string
    required:
    - id
    - name
    - resource
    - type
    type: object
  service_admin.SysRoleAddRequest:
    properties:
      description:
        description: 备注
        type: string
      name:
        description: 名称
        type: string
      sort_order:
        description: 排序字段
        type: integer
    required:
    - name
    type: object
  service_admin.SysRoleIDRequest:
    properties:
      id:
        description: 角色ID
        minimum: 1
        type: integer
    required:
    - id
    type: object
  service_admin.SysRoleListResponse:
    properties:
      list:
        description: 角色列表
        items:
          $ref: '#/definitions/structs.SysRole'
        type: array
      page:
        description: 返回当前页码
        type: integer
      page_size:
        description: 返回当前页码数量
        type: integer
      total:
        description: 总数量
        type: integer
    type: object
  service_admin.SysRolePermListResponse:
    properties:
      sys_role_permission:
        description: 角色已有的权限列表
        items:
          $ref: '#/definitions/structs.SysPermission'
        type: array
    type: object
  service_admin.SysRolePermSetRequest:
    properties:
      id:
        description: 角色ID
        minimum: 1
        type: integer
      permission_ids:
        description: 权限ID数组
        items:
          type: integer
        minItems: 1
        type: array
    required:
    - id
    - permission_ids
    type: object
  service_admin.SysRoleUpateRequest:
    properties:
      description:
        description: 备注
        type: string
      id:
        description: ID
        minimum: 1
        type: integer
      name:
        description: 名称
        type: string
      sort_order:
        description: 排序字段
        type: integer
    required:
    - id
    - name
    type: object
  service_admin.SysUserAddRequest:
    properties:
      email:
        description: 邮箱
        maxLength: 50
        minLength: 6
        type: string
      password:
        description: 密码
        maxLength: 20
        minLength: 6
        type: string
      status:
        description: 状态 0-1
        maximum: 1
        minimum: 0
        type: integer
      username:
        description: 账号
        maxLength: 20
        minLength: 6
        type: string
    required:
    - email
    - password
    - username
    type: object
  service_admin.SysUserIDRequest:
    properties:
      id:
        description: ID
        minimum: 1
        type: integer
    required:
    - id
    type: object
  service_admin.SysUserListResponse:
    properties:
      list:
        description: 系统用户列表
        items:
          $ref: '#/definitions/structs.SysUser'
        type: array
      page:
        description: 返回当前页码
        type: integer
      page_size:
        description: 返回当前页码数量
        type: integer
      total:
        description: 总数量
        type: integer
    type: object
  service_admin.SysUserRoleListResponse:
    properties:
      sys_user_role:
        description: 系统用户已有角色列表
        items:
          $ref: '#/definitions/structs.SysRole'
        type: array
    type: object
  service_admin.SysUserRoleSetRequest:
    properties:
      id:
        description: ID
        minimum: 1
        type: integer
      role_ids:
        description: 角色ID数组
        items:
          type: integer
        minItems: 1
        type: array
    required:
    - id
    - role_ids
    type: object
  service_admin.SysUserStatusRequest:
    properties:
      id:
        description: ID
        minimum: 1
        type: integer
      status:
        description: 状态 0-1
        maximum: 1
        minimum: 0
        type: integer
    required:
    - id
    type: object
  service_admin.SysUserUpateRequest:
    properties:
      email:
        description: 邮箱
        type: string
      id:
        description: ID
        minimum: 1
        type: integer
      password:
        description: 密码 不修改则留空
        type: string
      status:
        description: 状态 0-1
        maximum: 1
        minimum: 0
        type: integer
      username:
        description: 账号
        type: string
    required:
    - id
    type: object
  service_admin.TickAddRequest:
    properties:
      available_platforms:
        description: 可选择平台(可用平台)
        items:
          type: string
        type: array
      default_decimal_places:
        description: 默认小数位数
        type: integer
      designated_time:
        description: 指定最早初始时间
        type: integer
      maximum:
        description: 最大更新数据量上限
        type: integer
      platform:
        description: 平台
        type: string
      replace_symbol:
        description: 替换标识
        type: string
      status:
        description: 状态 0禁用 1启用
        maximum: 1
        minimum: 0
        type: integer
      sub_type:
        description: 子类型
        type: string
      symbol:
        description: 标识
        type: string
      symbolname:
        description: 产品名
        type: string
      time_zone:
        description: 时区
        type: string
      type:
        description: 类型
        type: string
    type: object
  service_admin.TickIDRequest:
    properties:
      id:
        description: 用户ID
        minimum: 1
        type: integer
    required:
    - id
    type: object
  service_admin.TickResetRequest:
    type: object
  service_admin.TickUpateRequest:
    properties:
      default_decimal_places:
        description: 默认小数位数
        type: integer
      designated_time:
        description: 指定最早初始时间
        type: integer
      id:
        description: ID
        minimum: 1
        type: integer
      maximum:
        description: 最大更新数据量上限
        type: integer
      platform:
        description: 平台
        type: string
      replace_symbol:
        description: 替换标识
        type: string
      status:
        description: 状态 0禁用 1启用
        maximum: 1
        minimum: 0
        type: integer
      sub_type:
        description: 子类型
        type: string
      symbol:
        description: 标识
        type: string
      symbolname:
        description: 产品名
        type: string
      time_zone:
        description: 时区
        type: string
      type:
        description: 类型
        type: string
    required:
    - id
    type: object
  service_admin.UserAddRequest:
    properties:
      password:
        description: 密码
        type: string
      status:
        description: 状态 0禁用 1启用
        maximum: 1
        minimum: 0
        type: integer
      token:
        description: token
        type: string
      username:
        description: 账号
        type: string
    required:
    - password
    - token
    - username
    type: object
  service_admin.UserIDRequest:
    properties:
      id:
        description: 用户ID
        minimum: 1
        type: integer
    required:
    - id
    type: object
  service_admin.UserListResponse:
    properties:
      list:
        description: 用户列表
        items:
          $ref: '#/definitions/structs.User'
        type: array
      page:
        description: 返回当前页码
        type: integer
      page_size:
        description: 返回当前页码数量
        type: integer
      total:
        description: 总数量
        type: integer
    type: object
  service_admin.UserViewResponse:
    properties:
      data:
        allOf:
        - $ref: '#/definitions/structs.User'
        description: 用户详情数据
    type: object
  service_api.KlineShadowAddRequest:
    properties:
      asks_price_max:
        description: 买跌浮动值 intuitive类型下才需要传递
        type: string
      asks_price_min:
        description: 买跌浮动值 intuitive类型下才需要传递
        type: string
      candlestick:
        description: 实时k线的close值 intuitive类型下才需要传递
        type: string
      fall_hit_max:
        description: 回落期的命中最大值 范围0.01-1之间 对应百分比1%到100%
        type: string
      fall_hit_min:
        description: 回落期的命中最小值 范围0.01-1之间 对应百分比1%到100%
        type: string
      fall_time:
        description: 回落时间(秒)
        type: integer
      fall_value_max:
        description: 回落期的命中后取值范围最大值 最小0.01 无上限
        type: string
      fall_value_min:
        description: 回落期的命中后取值范围最小值 最小0.01 无上限
        type: string
      lower_shadow_max:
        description: 下影线最大值
        type: string
      mode:
        description: '插针模式 模式1: 普通模式 模式2: 缩短K线模式'
        enum:
        - 1
        - 2
        type: integer
      peak_float:
        description: 峰值期浮动值, 比如当前峰值价格为10,这个值设定2,则峰值期的价格范围为8-12,每次随机
        type: string
      peak_hit_max:
        description: 峰值期的命中最大值 范围0.01-1之间 对应百分比1%到100%
        type: string
      peak_hit_min:
        description: 峰值期的命中最小值 范围0.01-1之间 对应百分比1%到100%
        type: string
      peak_time:
        description: 峰值时间(秒)
        type: integer
      peak_value_max:
        description: 峰值期的命中后取值范围最大值 最小0.01 无上限
        type: string
      peak_value_min:
        description: 峰值期的命中后取值范围最小值 最小0.01 无上限
        type: string
      rise_hit_max:
        description: 爬坡期的命中最大值 范围0.01-1之间 对应百分比1%到100%
        type: string
      rise_hit_min:
        description: 爬坡期的命中最小值 范围0.01-1之间 对应百分比1%到100%
        type: string
      rise_time:
        description: 爬坡时间(秒)
        type: integer
      rise_value_max:
        description: 爬坡期的命中后取值范围最大值 最小0.01 无上限
        type: string
      rise_value_min:
        description: 爬坡期的命中后取值范围最小值 最小0.01 无上限
        type: string
      spike_factor:
        description: 插针幅度 正数为上插 负数为下插 0为非法
        type: string
      start_time:
        description: 插针开始时间
        type: integer
      symbol:
        description: 名称
        type: string
      token:
        description: token
        type: string
      type:
        description: 类型 intuitive fluctuation
        type: string
      upper_shadow_max:
        description: 上影线最大值
        type: string
      user_id:
        description: 用户ID
        type: integer
    required:
    - fall_time
    - mode
    - peak_time
    - rise_time
    - spike_factor
    - start_time
    - symbol
    - token
    - type
    - user_id
    type: object
  service_api.KlineShadowIDRequest:
    properties:
      del_data:
        description: 是否同时删除插针的k线数据
        type: integer
      id:
        description: ID
        minimum: 1
        type: integer
      token:
        description: token
        type: string
      user_id:
        description: 用户ID
        type: integer
    required:
    - id
    - token
    - user_id
    type: object
  service_api.KlineShadowListResponse:
    properties:
      list:
        description: 列表
        items:
          $ref: '#/definitions/structs.UserShadow'
        type: array
      page:
        description: 返回当前页码
        type: integer
      page_size:
        description: 返回当前页码数量
        type: integer
      total:
        description: 总数量
        type: integer
    type: object
  service_api.KlineShadowSymbolRequest:
    properties:
      symbols:
        description: 要修复的产品 多个用,分开
        type: string
      token:
        description: token
        type: string
      user_id:
        description: 用户ID
        type: integer
    required:
    - symbols
    - token
    - user_id
    type: object
  service_common.CommonUriResponse:
    properties:
      uri:
        description: 图片或文件访问地址
        type: string
    type: object
  structs.Response:
    properties:
      code:
        description: "返回值\n\t\t200 成功\n\t\t520 通用错误代码\n\t\t550 参数错误\n\t\t551 需要登录\n\t\t552
          需要谷歌验证\n\t\t553 令牌生成失败\n\t\t555 密码错误\n\t\t556 权限不足\n\t\t557 设置缓存失败\n\t\t558
          取权限错误\n\t\t559 mysql错误"
        type: integer
      data:
        description: 返回数据
      error:
        description: 返回错误消息 如果为空参考返回消息
        type: string
      message:
        description: 返回消息
        type: string
    type: object
  structs.SiteConfigGroup:
    properties:
      configs:
        description: 参数
        items:
          $ref: '#/definitions/structs.SiteConfigTree'
        type: array
      group_name:
        description: 分组名称
        type: string
    type: object
  structs.SiteConfigTree:
    properties:
      config_desc:
        description: 描述
        type: string
      config_name:
        description: 配置名称
        type: string
      config_type:
        description: 类型 Input Textarea Radio Checkbox 复选框 Checkboxs 多选框 Select ImageUpload
        type: string
      config_value:
        description: 配置值
        type: string
      id:
        type: integer
      title:
        description: 标题
        type: string
      title_scope:
        description: 选择参数名称
        type: string
      value_scope:
        description: 选择参数
        type: string
    type: object
  structs.SysPermission:
    properties:
      description:
        type: string
      id:
        description: 主键ID
        type: integer
      name:
        type: string
      parent_id:
        description: 父级权限ID，0表示顶级
        type: integer
      resource:
        type: string
      sort_order:
        description: 排序字段
        type: integer
      type:
        description: menu 或 action
        type: string
    type: object
  structs.SysRole:
    properties:
      description:
        type: string
      id:
        description: 主键ID
        type: integer
      name:
        type: string
      sort_order:
        description: 排序字段
        type: integer
      sys_role_permission:
        description: 角色和权限多对多关系
        items:
          $ref: '#/definitions/structs.SysPermission'
        type: array
    type: object
  structs.SysUser:
    properties:
      created_at:
        allOf:
        - $ref: '#/definitions/structs.UTCDateTime'
        description: 自动创建时间
      email:
        description: 邮箱
        type: string
      google_secret:
        description: GoogleSecret
        type: string
      id:
        description: 主键ID
        type: integer
      phone:
        description: 手机号
        type: string
      status:
        description: 状态 0禁用 1启用
        type: integer
      sys_user_role:
        description: 用户和角色多对多关系
        items:
          $ref: '#/definitions/structs.SysRole'
        type: array
      token:
        description: token
        type: string
      updated_at:
        allOf:
        - $ref: '#/definitions/structs.UTCDateTime'
        description: 自动更新时间
      username:
        description: 账号
        type: string
    type: object
  structs.UTCDateTime:
    properties:
      time.Time:
        type: string
    type: object
  structs.User:
    properties:
      created_at:
        allOf:
        - $ref: '#/definitions/structs.UTCDateTime'
        description: 自动创建时间
      end_duration:
        type: integer
      id:
        description: 主键ID
        type: integer
      kline_query_number_1day:
        description: k线可查询最多数量
        type: integer
      kline_query_number_1hour:
        description: k线可查询最多数量
        type: integer
      kline_query_number_1m:
        description: k线可查询最多数量
        type: integer
      kline_query_number_1month:
        description: k线可查询最多数量
        type: integer
      kline_query_number_1week:
        description: k线可查询最多数量
        type: integer
      kline_query_number_2hour:
        description: k线可查询最多数量
        type: integer
      kline_query_number_4hour:
        description: k线可查询最多数量
        type: integer
      kline_query_number_5m:
        description: k线可查询最多数量
        type: integer
      kline_query_number_15m:
        description: k线可查询最多数量
        type: integer
      kline_query_number_30m:
        description: k线可查询最多数量
        type: integer
      password:
        description: 密码
        type: string
      remark:
        description: 备注
        type: string
      start_duration:
        type: integer
      status:
        description: 状态 0禁用 1启用
        type: integer
      symbols:
        description: 用户权限(可获取的数据资源ID)
        items:
          type: integer
        type: array
      token:
        description: token
        type: string
      updated_at:
        allOf:
        - $ref: '#/definitions/structs.UTCDateTime'
        description: 自动更新时间
      user_symbols:
        items:
          $ref: '#/definitions/structs.UserSymbol'
        type: array
      username:
        description: 账号
        type: string
    type: object
  structs.UserShadow:
    properties:
      asks_price_max:
        description: 买跌浮动值
        type: string
      asks_price_min:
        description: 买跌浮动值
        type: string
      candlestick:
        description: 实时k线的close值
        type: string
      created_at:
        allOf:
        - $ref: '#/definitions/structs.UTCDateTime'
        description: 自动创建时间
      end_time:
        description: 插针结束时间
        type: integer
      fall_candles_info:
        description: 回落期完整蜡烛图开始时间和是否需要改变
        type: object
      fall_hit_max:
        description: 回落期的命中最大值
        type: string
      fall_hit_min:
        description: 回落期的命中最小值
        type: string
      fall_time:
        description: 回落阶段时间（秒）
        type: integer
      fall_value_max:
        description: 回落期的命中后取值范围最大值
        type: string
      fall_value_min:
        description: 回落期的命中后取值范围最小值
        type: string
      id:
        description: 主键ID
        type: integer
      lower_shadow_max:
        description: 下影线最大值
        type: string
      peak_candles_info:
        description: 峰值期完整蜡烛图开始时间和是否需要改变
        type: object
      peak_float:
        description: 峰值期的浮动值
        type: string
      peak_hit_max:
        description: 峰值期的命中最大值
        type: string
      peak_hit_min:
        description: 峰值期的命中最小值
        type: string
      peak_time:
        description: 峰值阶段时间（秒）
        type: integer
      peak_value_max:
        description: 峰值期的命中后取值范围最大值
        type: string
      peak_value_min:
        description: 峰值期的命中后取值范围最小值
        type: string
      rise_candles_info:
        description: 爬坡期完整蜡烛图开始时间和是否需要改变
        type: object
      rise_hit_max:
        description: 爬坡期的命中最大值
        type: string
      rise_hit_min:
        description: 爬坡期的命中最小值
        type: string
      rise_time:
        description: 爬坡阶段时间（秒）
        type: integer
      rise_value_max:
        description: 爬坡期的命中后取值范围最大值
        type: string
      rise_value_min:
        description: 爬坡期的命中后取值范围最小值
        type: string
      shadow_end_time:
        description: 1m插针结束时间线
        type: integer
      shadow_start_time:
        description: 1m插针开始时间线
        type: integer
      spike_factor:
        description: 插针幅度
        type: string
      start_time:
        description: 插针开始时间
        type: integer
      status:
        description: 状态 0禁用 1启用
        type: integer
      symbol:
        description: 产品标识
        type: string
      type:
        description: 插针类型 intuitive fluctuation
        type: string
      updated_at:
        allOf:
        - $ref: '#/definitions/structs.UTCDateTime'
        description: 自动更新时间
      upper_shadow_max:
        description: 上影线最大值
        type: string
      user_id:
        description: 用户ID
        type: integer
    type: object
  structs.UserSymbol:
    properties:
      decimal_places:
        description: 小数位数
        type: integer
      platform:
        description: 产品平台
        type: string
      symbol:
        description: 产品标识
        type: string
      symbol_id:
        description: 产品ID
        type: integer
      type:
        description: 类型
        type: string
      user_id:
        description: 用户ID
        type: integer
    type: object
  user_model.PutUserRequest:
    properties:
      id:
        description: ID
        minimum: 1
        type: integer
      password:
        description: 密码
        type: string
      status:
        description: 状态 0禁用 1启用
        maximum: 1
        minimum: 0
        type: integer
      symbols:
        description: 用户权限(可获取的数据资源,每一种产品需要两个 Symbol 一个 kline, 一个 orderBook)
        items:
          $ref: '#/definitions/user_model.UserUpdateRequestSymbol'
        type: array
      token:
        description: token
        type: string
      username:
        description: 账号
        type: string
    required:
    - id
    - token
    - username
    type: object
  user_model.UserUpdateRequestSymbol:
    properties:
      decimal_places:
        description: 小数位数
        type: integer
      id:
        description: 产品ID
        type: integer
      type:
        description: 注意类型必须是 orderBook 或 kline
        enum:
        - orderBook
        - kline
        type: string
    required:
    - type
    type: object
info:
  contact: {}
paths:
  /admin/User/User:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service_admin.UserIDRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 删除用户
      tags:
      - 后台/用户管理
    post:
      consumes:
      - application/json
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service_admin.UserAddRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 添加用户
      tags:
      - 后台/用户管理
    put:
      consumes:
      - application/json
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/user_model.PutUserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 修改用户
      tags:
      - 后台/用户管理
  /admin/build_google_totp:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  $ref: '#/definitions/service_admin.GetBuildGoogleTotpResponse'
              type: object
      security:
      - BearerAuth: []
      summary: 生成谷歌令牌密钥
      tags:
      - 后台/个人操作
    post:
      consumes:
      - application/json
      parameters:
      - description: 验证请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service_admin.PostBuildGoogleTotpRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 提交谷歌令牌密钥
      tags:
      - 后台/个人操作
  /admin/login:
    post:
      consumes:
      - application/json
      description: 使用邮箱、用户名和密码登录。账号 (username) 和邮箱 (email) 必须至少填写其中一个。
      parameters:
      - description: 登录请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service_admin.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  $ref: '#/definitions/service_admin.LoginResponse'
              type: object
      summary: 登录
      tags:
      - 后台/个人操作
  /admin/logout:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 退出登录
      tags:
      - 后台/个人操作
  /admin/reset_password:
    post:
      consumes:
      - application/json
      parameters:
      - description: 验证请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service_admin.ResetPasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 修改密码
      tags:
      - 后台/个人操作
  /admin/system/perm:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service_admin.SysPermIDRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 删除权限
      tags:
      - 后台/权限管理
    get:
      consumes:
      - application/json
      description: 无搜索条件。
      parameters:
      - description: 页码 可空 默认为1
        in: query
        name: page
        type: integer
      - description: 每页显示数量 可空 默认值由项目启动参数决定
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  $ref: '#/definitions/service_admin.SysPermListResponse'
              type: object
      security:
      - BearerAuth: []
      summary: 权限列表
      tags:
      - 后台/权限管理
    post:
      consumes:
      - application/json
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service_admin.SysPermAddRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 添加权限
      tags:
      - 后台/权限管理
    put:
      consumes:
      - application/json
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service_admin.SysPermUpateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 修改权限
      tags:
      - 后台/权限管理
  /admin/system/role:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service_admin.SysRoleIDRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 删除角色
      tags:
      - 后台/角色管理
    get:
      consumes:
      - application/json
      description: 无搜索条件。
      parameters:
      - description: 页码 可空 默认为1
        in: query
        name: page
        type: integer
      - description: 每页显示数量 可空 默认值由项目启动参数决定
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  $ref: '#/definitions/service_admin.SysRoleListResponse'
              type: object
      security:
      - BearerAuth: []
      summary: 角色列表
      tags:
      - 后台/角色管理
    post:
      consumes:
      - application/json
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service_admin.SysRoleAddRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 添加角色
      tags:
      - 后台/角色管理
    put:
      consumes:
      - application/json
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service_admin.SysRoleUpateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 修改角色
      tags:
      - 后台/角色管理
  /admin/system/role/perm_list:
    get:
      consumes:
      - application/json
      parameters:
      - description: 角色ID
        in: query
        minimum: 1
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  $ref: '#/definitions/service_admin.SysRolePermListResponse'
              type: object
      security:
      - BearerAuth: []
      summary: 获取角色权限列表
      tags:
      - 后台/角色管理
  /admin/system/role/perm_set:
    put:
      consumes:
      - application/json
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service_admin.SysRolePermSetRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 修改角色权限
      tags:
      - 后台/角色管理
  /admin/system/siteconfig:
    get:
      consumes:
      - application/json
      description: 无搜索条件。
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  $ref: '#/definitions/service_admin.SiteConfigListResponse'
              type: object
      security:
      - BearerAuth: []
      summary: 站点配置列表
      tags:
      - 后台/站点管理
    put:
      consumes:
      - application/json
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service_admin.SiteConfigUpateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 修改站点配置
      tags:
      - 后台/站点管理
  /admin/system/user:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service_admin.SysUserIDRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 删除系统用户
      tags:
      - 后台/系统用户管理
    get:
      consumes:
      - application/json
      description: 无搜索条件。
      parameters:
      - description: 页码 可空 默认为1
        in: query
        name: page
        type: integer
      - description: 每页显示数量 可空 默认值由项目启动参数决定
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  $ref: '#/definitions/service_admin.SysUserListResponse'
              type: object
      security:
      - BearerAuth: []
      summary: 系统用户列表
      tags:
      - 后台/系统用户管理
    post:
      consumes:
      - application/json
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service_admin.SysUserAddRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 添加系统用户
      tags:
      - 后台/系统用户管理
    put:
      consumes:
      - application/json
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service_admin.SysUserUpateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 修改系统用户
      tags:
      - 后台/系统用户管理
  /admin/system/user/reset_gs:
    put:
      consumes:
      - application/json
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service_admin.SysUserIDRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 重置系统用户谷歌密钥
      tags:
      - 后台/系统用户管理
  /admin/system/user/role_list:
    get:
      consumes:
      - application/json
      parameters:
      - description: 后台用户ID
        in: query
        minimum: 1
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  $ref: '#/definitions/service_admin.SysUserRoleListResponse'
              type: object
      security:
      - BearerAuth: []
      summary: 获取系统用户角色列表
      tags:
      - 后台/系统用户管理
  /admin/system/user/role_set:
    put:
      consumes:
      - application/json
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service_admin.SysUserRoleSetRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 修改系统用户角色
      tags:
      - 后台/系统用户管理
  /admin/system/user/status:
    put:
      consumes:
      - application/json
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service_admin.SysUserStatusRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 更改系统用户状态
      tags:
      - 后台/系统用户管理
  /admin/tick/kline_history:
    get:
      consumes:
      - application/json
      description: 无搜索条件。
      parameters:
      - description: 产品标识 Symbol
        in: query
        name: code
        type: string
      - description: 数量
        in: query
        name: count
        type: integer
      - description: k线的最大时间
        in: query
        name: kline_timestamp_end
        type: integer
      - description: k线类型 1 三方数据 2 本方数据 3 本方数据+用户插针
        in: query
        name: kline_type
        type: integer
      - description: 时间分辨率
        in: query
        name: resolution
        type: string
      - description: 用户ID
        in: query
        name: user_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  $ref: '#/definitions/structs.Response'
              type: object
      security:
      - BearerAuth: []
      summary: 产品列表
      tags:
      - 后台/产品管理
  /admin/tick/tick:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service_admin.TickIDRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 删除产品
      tags:
      - 后台/产品管理
    get:
      consumes:
      - application/json
      description: 无搜索条件。
      parameters:
      - description: ID
        in: query
        name: id
        type: integer
      - description: 页码 可空 默认为1
        in: query
        name: page
        type: integer
      - description: 每页显示数量 可空 默认值由项目启动参数决定
        in: query
        name: page_size
        type: integer
      - description: 平台
        in: query
        name: platform
        type: string
      - description: 产品标识
        in: query
        name: symbol
        type: string
      - description: 类型
        in: query
        name: type
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  $ref: '#/definitions/structs.Response'
              type: object
      security:
      - BearerAuth: []
      summary: 产品列表
      tags:
      - 后台/产品管理
    post:
      consumes:
      - application/json
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service_admin.TickAddRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 添加产品
      tags:
      - 后台/产品管理
    put:
      consumes:
      - application/json
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service_admin.TickUpateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 修改产品
      tags:
      - 后台/产品管理
  /admin/tick/tick_log:
    get:
      consumes:
      - application/json
      description: 无搜索条件。
      parameters:
      - description: ID
        in: query
        name: id
        type: integer
      - description: 是否为一分钟的开始
        in: query
        name: is_open
        type: boolean
      - description: 类型 1 上下两条k线产生的误差超过设定范围 2 插针记录 上条为插针改变的 下条为原始未改变的
        in: query
        name: log_type
        type: integer
      - description: 页码 可空 默认为1
        in: query
        name: page
        type: integer
      - description: 每页显示数量 可空 默认值由项目启动参数决定
        in: query
        name: page_size
        type: integer
      - description: 平台
        in: query
        name: platform
        type: string
      - description: 时间分辨率
        in: query
        name: resolutions
        type: string
      - description: 插针ID
        in: query
        name: shadow_id
        type: integer
      - description: 产品标识
        in: query
        name: symbol
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  $ref: '#/definitions/structs.Response'
              type: object
      security:
      - BearerAuth: []
      summary: 产品列表
      tags:
      - 后台/产品管理
  /admin/tick/tick_reset:
    put:
      consumes:
      - application/json
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service_admin.TickResetRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 重置缓存
      tags:
      - 后台/产品管理
  /admin/tick/view:
    get:
      consumes:
      - application/json
      description: 无搜索条件。
      parameters:
      - description: 用户ID
        in: query
        minimum: 1
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  $ref: '#/definitions/structs.Response'
              type: object
      security:
      - BearerAuth: []
      summary: 产品详情
      tags:
      - 后台/产品管理
  /admin/upload_file:
    post:
      consumes:
      - multipart/form-data
      parameters:
      - description: 上传的文件
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  $ref: '#/definitions/service_common.CommonUriResponse'
              type: object
      security:
      - BearerAuth: []
      summary: 上传文件
      tags:
      - 后台/上传管理
  /admin/upload_image:
    post:
      consumes:
      - multipart/form-data
      parameters:
      - description: 上传的图片
        in: formData
        name: image
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  $ref: '#/definitions/service_common.CommonUriResponse'
              type: object
      security:
      - BearerAuth: []
      summary: 上传图片
      tags:
      - 后台/上传管理
  /admin/user/user:
    get:
      consumes:
      - application/json
      description: 无搜索条件。
      parameters:
      - description: ID
        in: query
        name: id
        type: integer
      - description: 页码 可空 默认为1
        in: query
        name: page
        type: integer
      - description: 每页显示数量 可空 默认值由项目启动参数决定
        in: query
        name: page_size
        type: integer
      - description: 用户名
        in: query
        name: username
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  $ref: '#/definitions/service_admin.UserListResponse'
              type: object
      security:
      - BearerAuth: []
      summary: 用户列表
      tags:
      - 后台/用户管理
  /admin/user/view:
    get:
      consumes:
      - application/json
      description: 无搜索条件。
      parameters:
      - description: 用户ID
        in: query
        minimum: 1
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  $ref: '#/definitions/service_admin.UserViewResponse'
              type: object
      security:
      - BearerAuth: []
      summary: 用户详情
      tags:
      - 后台/用户管理
  /admin/verify_google_totp:
    post:
      consumes:
      - application/json
      parameters:
      - description: 验证请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service_admin.PostVerifyGoogleTotpRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 重新验证谷歌令牌
      tags:
      - 后台/个人操作
  /api/connection_delivery:
    get:
      consumes:
      - application/json
      description: 无搜索条件。
      parameters:
      - description: token
        in: query
        name: token
        type: string
      - description: 用户ID
        in: query
        name: user_id
        type: integer
      - description: 你的服务器地址
        in: query
        name: your_sever
        type: string
      - description: 你的验证token
        in: query
        name: your_token
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 连接请求
      tags:
      - api/用户管理
  /api/kline_history:
    get:
      consumes:
      - application/json
      description: 无搜索条件。
      parameters:
      - description: 产品标识 Symbol
        in: query
        name: code
        type: string
      - description: 数量
        in: query
        name: count
        type: integer
      - description: 截止时间
        in: query
        name: kline_timestamp_end
        type: integer
      - description: 时间分辨率
        in: query
        name: resolution
        type: string
      - description: token
        in: query
        name: token
        type: string
      - description: 用户ID
        in: query
        name: user_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 批量获取历史k线
      tags:
      - api/历史k线
  /api/kline_shadow:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service_api.KlineShadowIDRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 删除k线插针
      tags:
      - api/k线插针
    get:
      consumes:
      - application/json
      description: 无搜索条件。
      parameters:
      - description: 页码 可空 默认为1
        in: query
        name: page
        type: integer
      - description: 每页显示数量 可空 默认值由项目启动参数决定
        in: query
        name: page_size
        type: integer
      - description: token
        in: query
        name: token
        type: string
      - description: 用户ID
        in: query
        name: user_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  $ref: '#/definitions/service_api.KlineShadowListResponse'
              type: object
      security:
      - BearerAuth: []
      summary: k线插针列表
      tags:
      - api/k线插针
    post:
      consumes:
      - application/json
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service_api.KlineShadowAddRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 添加k线插针
      tags:
      - api/k线插针
  /api/kline_shadow/fix:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service_api.KlineShadowSymbolRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回码看对照表
          schema:
            allOf:
            - $ref: '#/definitions/structs.Response'
            - properties:
                data:
                  type: object
              type: object
      security:
      - BearerAuth: []
      summary: 删除k线插针清空
      tags:
      - api/k线插针
swagger: "2.0"
