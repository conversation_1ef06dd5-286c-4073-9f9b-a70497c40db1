package validate

import (
	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/validator/v10"
	"github.com/shopspring/decimal"
	"log"
)

/*
decimal 类型的校验
*/
func RegisterDecimalValidators() {
	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		register := func(tag string, fn func(decimal.Decimal) bool) {
			err := v.RegisterValidation(tag, func(fl validator.FieldLevel) bool {
				val, ok := fl.Field().Interface().(decimal.Decimal)
				if !ok {
					return false
				}
				return fn(val)
			})
			if err != nil {
				log.Fatalf("failed to register validator '%s': %v", tag, err)
			}
		}

		register("decimal_eq0", func(d decimal.Decimal) bool { return d.Equal(decimal.Zero) })
		register("decimal_neq0", func(d decimal.Decimal) bool { return !d.Equal(decimal.Zero) })
		register("decimal_gt0", func(d decimal.Decimal) bool { return d.Greater<PERSON>han(decimal.Zero) })
		register("decimal_gte0", func(d decimal.Decimal) bool { return !d.LessThan(decimal.Zero) })
		register("decimal_lt0", func(d decimal.Decimal) bool { return d.LessThan(decimal.Zero) })
		register("decimal_lte0", func(d decimal.Decimal) bool { return !d.GreaterThan(decimal.Zero) })
	}
}
