package service

import (
	cmap "github.com/orcaman/concurrent-map/v2"
	"sync"
	"trading_tick_server/internal/data_transfer_station/model"
)

var (
	stationOnce sync.Once
	stationInst any
)

/*
GetStation 获取数据中转站实例
*/
func GetStation[T any]() *model.DataStation[T] {
	stationOnce.Do(func() {
		stationInst = &model.DataStation[T]{
			Hall: cmap.New[cmap.ConcurrentMap[string, *model.DataRoom[T]]](),
		}
	})
	return stationInst.(*model.DataStation[T])
}

/*
获取用户,产品房间
*/
func GetStationProductRoom[T any](userID, productID string) (*model.DataRoom[T], bool) {
	rooms, ok := GetStation[T]().GetUserHub(userID)
	if !ok {
		return nil, false
	}
	room, ok := rooms.Get(productID)
	if !ok {
		/*
		 创建房间
		*/
		room = model.NewRoom[T](userID, productID, 1)
		rooms.Set(productID, room)
	}
	return room, true
}
