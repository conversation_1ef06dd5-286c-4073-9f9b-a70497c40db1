package model

import (
	"context"
	"fmt"
	"sync/atomic"
	"trading_tick_server/lib/logger"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"
)

/*
数据中转站
采用中转通道 + 状态指针 + 控制开关，当 B 启动时，A 的数据不直接进指针，而是进通道，由 B 修改后放入指针，C 从指针读取。
数据流示意图
A ➜ [modifyChan] ➜ B(修改) ➜ atomic.Pointer ➜ C

	│
	└──（未启用 B 时）──────→ 直接写入 atomic.Pointer
*/
type DataRoom[T any] struct {
	userID          string      // 用户ID(这个数据房间的所有者)
	product         string      // 产品名(这个数据房间的产品)
	ActiveModifying atomic.Bool // 激活修改(是否处于控制状态)
	isRunning       atomic.Bool // 修改携程是否在运行中

	modifyChan chan T  // 修改通道
	dataChan   chan *T // 数据通道,所有数据最终都是存在着里面

	modifier      func(userID string, data *T) *T  // 修改器
	writeSelector func(userID string, data T) bool // 写入选择器

	/*
		为了给盘口数据信息,临时加这里,有时间了重新来设计
		房间是不应该包含设计这些数据的
	*/

	AsksPriceMin          decimal.Decimal // 买跌浮动最小值
	AsksPriceMax          decimal.Decimal // 买跌浮动最大值
	OrderBookDiffMin      decimal.Decimal // 盘口价差最小值
	OrderBookDiffMax      decimal.Decimal // 盘口价差最大值
	ControlPrice          decimal.Decimal // 控盘价格
	OriginalDecimalPlaces int             // 原始小数位精度
}

// NewRoom 创建一个新的 DataRoom
func NewRoom[T any](userID string, product string, chanBuffer int) *DataRoom[T] {
	return &DataRoom[T]{
		userID:     userID,
		product:    product,
		modifyChan: make(chan T, chanBuffer),
		dataChan:   make(chan *T, chanBuffer),
	}
}

// Write 写入数据（调用方控制写入路径）
func (dr *DataRoom[T]) Write(data T) {
	if dr.ActiveModifying.Load() {
		// 处于控制状态下，才根据 selector 判断是否进入 modifyChan
		writeToChan := true
		if dr.writeSelector != nil {
			writeToChan = dr.writeSelector(dr.userID, data) // 根据 selector 判断是否写入通道
		}

		if writeToChan {
			select {
			case dr.modifyChan <- data:
			default:
				// 通道满，丢弃
				fmt.Printf("🔧房间(用户ID)[%s]启用了控制,数据写入通道失败(修改通道已满), 数据: %v\n", dr.userID, data)
			}
			return
		}
	}

	// 非控制状态，直接写指针
	dr.dataChan <- &data
}

// Read 读取当前可见的数据（已经被 B 修改后的结果）
func (dr *DataRoom[T]) Read() *T {
	data := <-dr.dataChan
	return data
}

// EnableModification 启用修改流程
func (dr *DataRoom[T]) EnableModification(modifier func(userID string, data *T) *T, selector func(userID string, data T) bool) error {
	/*
		始终更新修改器和选择器
	*/
	if modifier == nil {
		return fmt.Errorf("❌修改器函数不能为空")
	}
	if selector == nil {
		return fmt.Errorf("❌选择器函数不能为空")
	}
	dr.modifier = modifier
	dr.writeSelector = selector

	if dr.ActiveModifying.CompareAndSwap(false, true) {
		if dr.isRunning.CompareAndSwap(false, true) {
			go func() {
				ctx := context.Background()
				logger.InfoCtx(ctx, "数据房间修改协程启动",
					zap.String("用户ID", dr.userID),
					zap.String("产品", dr.product))

				for {
					raw := <-dr.modifyChan
					modified := dr.modifier(dr.userID, &raw)
					dr.dataChan <- modified

					// 根据原来的打印信息添加对应的日志字段
					logger.DataFlow("携程流程成功已写入PTR",
						zap.String("用户ID", dr.userID),
						zap.String("产品", dr.product),
						zap.Any("处理后数据", *modified))
				}
			}()
		}
	}
	return nil
}

// DisableModification 停止修改流程（可选）
func (dr *DataRoom[T]) DisableModification() {
	dr.ActiveModifying.Store(false)
}
