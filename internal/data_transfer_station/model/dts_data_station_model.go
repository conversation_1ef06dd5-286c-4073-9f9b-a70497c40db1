package model

import (
	"context"
	"fmt"
	cmap "github.com/orcaman/concurrent-map/v2"
	"go.uber.org/zap"
	"trading_tick_server/lib/logger"
)

// DataStation 统一管理所有用户的房间（每个房间为一个 DataRoom[T]）
type DataStation[T any] struct {
	Hall cmap.ConcurrentMap[string, cmap.ConcurrentMap[string, *DataRoom[T]]] // 用户 产品 房间
}

// NewUserHub 创建一个新的用户区域（房间集合）
/*
⚠️注意: 目前是可以重复创建房间的,新的房间会代替旧的房间,需要外部做判断
如果以后确定不需要重复创建房间,可以在这里加判断
*/
func (ds *DataStation[T]) NewUserHub(userID string, chanBuffer int) cmap.ConcurrentMap[string, cmap.ConcurrentMap[string, *DataRoom[T]]] {
	ctx := context.Background()
	ds.Hall.Set(userID, cmap.New[*DataRoom[T]]())
	logger.InfoCtx(ctx, "创建用户区域",
		zap.String("用户ID", userID),
		zap.Int("通道缓冲区大小", chanBuffer),
		zap.String("区域地址", fmt.Sprintf("%p", &ds.Hall)))
	return ds.Hall
}

// GetUserHub 仅获取已有房间（不存在时返回 nil）
func (ds *DataStation[T]) GetUserHub(userID string) (cmap.ConcurrentMap[string, *DataRoom[T]], bool) {
	rooms, ok := ds.Hall.Get(userID)
	return rooms, ok
}
