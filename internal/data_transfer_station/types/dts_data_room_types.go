package types

import (
	"github.com/shopspring/decimal"
	"trading_tick_server/lib/structs"
)

type ExtraDataPkg struct {
	OriginalData *[]structs.KlineRetData
	ExtraData    *ExtraData
}

type ExtraData struct {
	Platform              string          // 平台
	Symbol                string          // 产品名
	TotalOffset           decimal.Decimal // 币种,总偏移量
	CurrentOffset         decimal.Decimal // 币种,当前偏移量
	RealPrice             decimal.Decimal // 币种,真实价格
	ControlPrice          decimal.Decimal // 币种,控盘价格
	AsksPriceMin          decimal.Decimal `json:"asks_price_min,omitempty"`      // 买跌浮动最小值
	AsksPriceMax          decimal.Decimal `json:"asks_price_max,omitempty"`      // 买涨浮动最大值
	OrderBookDiffMin      decimal.Decimal `json:"order_book_diff_min,omitempty"` // 盘口价差最小值
	OrderBookDiffMax      decimal.Decimal `json:"order_book_diff_max,omitempty"` // 盘口价差最大值
	OriginalDecimalPlaces int32           `json:"original_decimal_places"`       // 原始小数位精度
	TimeZone              string          // 时区
}
