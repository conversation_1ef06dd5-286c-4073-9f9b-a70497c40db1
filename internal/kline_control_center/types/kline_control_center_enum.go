package types

/*
控制类型
*/

type ENUM_KLINE_CONTROL_TYPE string

const (
	// 智能模式
	ENUM_KLINE_CONTROL_TYPE_SMART ENUM_KLINE_CONTROL_TYPE = "SMART"
	// 立即模式
	ENUM_KLINE_CONTROL_TYPE_IMMEDIATE ENUM_KLINE_CONTROL_TYPE = "IMMEDIATE"
	// 渐变模式（原来的线性模式）
	ENUM_KLINE_CONTROL_TYPE_GRADIENT ENUM_KLINE_CONTROL_TYPE = "GRADIENT"
	// 线性模式（新加的持续渐变模式）
	ENUM_KLINE_CONTROL_TYPE_LINEAR ENUM_KLINE_CONTROL_TYPE = "LINEAR"
)
