package service

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"
	"trading_tick_server/common/gmysql"
	"trading_tick_server/common/utils"
	"trading_tick_server/internal/kline_control_center/dto"
	"trading_tick_server/internal/kline_control_center/model"
	"trading_tick_server/lib/decouplingfunction"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/mysql"
	"trading_tick_server/lib/redis"

	goredislib "github.com/redis/go-redis/v9"
	"github.com/sknun/cf/cast"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

/*
K线控盘,控制中心
所有K线控盘的协调工作都在这里
*/
// 用于保护每个 symbol 的读写锁，避免全局锁带来的性能瓶颈
var symbolLockMap = sync.Map{} // map[string]*sync.RWMutex

func getSymbolLock(symbol string) *sync.RWMutex {
	val, _ := symbolLockMap.LoadOrStore(symbol, &sync.RWMutex{})
	return val.(*sync.RWMutex)
}

/*
查询某个产品是否在控盘中
*/
func SymbolIsInControl(symbol string, db *gorm.DB) bool {
	lock := getSymbolLock(symbol)
	lock.RLock()
	defer lock.RUnlock()

	var count int64
	err := db.Model(&model.KlineControlRecordDB{}).
		Where("symbol = ? AND is_end = 0", symbol).
		Count(&count).Error

	if err != nil {
		fmt.Printf("ymbol is in control, err: %s\n", err.Error())
		return false
	}

	return count > 0
}

/*
设定某个产品为结束控盘
*/
func SymbolControlEnd(userID string, symbol string, db *gorm.DB) error {
	lock := getSymbolLock(symbol)
	lock.Lock()
	defer lock.Unlock()

	// 获取当前时间戳作为结束时间
	now := time.Now().Unix()

	// 更新未结束的控盘记录：设置 is_end = true，end_time = now
	return db.Model(&model.KlineControlRecordDB{}).
		Where("user_id = ? AND symbol = ? AND is_end = 0", userID, symbol).
		Updates(map[string]interface{}{
			"is_end": true,
			//"is_in_fall": false,
			"end_time": now,
		}).Error
}

/*
设定某个产品为回落期
*/
/*func SymbolControlInFall(symbol string, db *gorm.DB) error {
	lock := getSymbolLock(symbol)
	lock.Lock()
	defer lock.Unlock()

	// 更新未结束的控盘记录：设置 is_end = true，end_time = now
	return db.Model(&model.KlineControlRecordDB{}).
		Where("symbol = ? AND is_end = 0", symbol).
		Updates(map[string]interface{}{
			"is_in_fall": true,
		}).Error
}*/

/*
获取当前产品的控盘记录(加DB锁)
*/
func GetControlRecordWithLock(userID string, symbol string, tx *gorm.DB) (*model.KlineControlRecordDB, error) {
	lock := getSymbolLock(symbol)
	lock.RLock()
	defer lock.RUnlock()

	var record model.KlineControlRecordDB
	err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("user_id = ? AND symbol = ? AND is_end = 0", userID, symbol).
		Take(&record).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // 没有控盘记录是正常情况
		}
		fmt.Printf("GetActiveControlRecord error: %s\n", err.Error())
		return nil, err // 正确地返回错误
	}
	// 如果没有找到记录，返回 nil
	return &record, nil
}

/*
创建一条控盘记录
*/
func CreateControlSafelyFromStruct(record *model.KlineControlRecordDB, db *gorm.DB) error {
	if record == nil {
		return fmt.Errorf("record is nil")
	}
	if record.Symbol == "" {
		return fmt.Errorf("symbol is required")
	}

	lock := getSymbolLock(record.Symbol)
	lock.Lock()
	defer lock.Unlock()

	// 检查是否已有未结束的控盘
	var count int64
	if err := db.Model(&model.KlineControlRecordDB{}).
		Where("user_id = ? AND symbol = ? AND is_end = 0", record.UserID, record.Symbol).
		Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return fmt.Errorf("symbol %s already has an active control", record.Symbol)
	}

	// 插入新控盘记录
	return db.Create(record).Error
}

/*
将所有没结束的控盘全部设置为结束
*/
func SetAllControlEnd() error {
	// 获取当前时间戳作为结束时间
	now := time.Now().Unix()

	// 更新未结束的控盘记录：设置 is_end = true，end_time = now
	return mysql.M.Model(&model.KlineControlRecordDB{}).
		Where("is_end = 0").
		Updates(map[string]interface{}{
			"is_end":   true,
			"end_time": now,
		}).Error
}

/*
获取偏移量控制(立即模式)历史记录
*/
func GetControlHistory(ctx context.Context, req dto.GetKlineOffsetHistoryRequest) (*gmysql.PageResult[model.KlineControlRecordDB], error) {
	// 默认分页参数处理（防止前端没传）
	if req.PageNum < 1 {
		req.PageNum = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	// 限制最大 100 条
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	return gmysql.QueryPage[model.KlineControlRecordDB](
		ctx,
		mysql.M,
		req.PageNum,
		req.PageSize,
		map[string]any{
			"user_id": req.UserID,
			"symbol":  req.Symbol,
		},
		"id DESC", // 按照插入顺序倒序
	)
}

/*
删除单个偏移量控制(立即模式)历史记录
*/
func DelControlHistorySingle(ctx context.Context, req dto.DelKlineOffsetHistorySingleRequest) error {
	/*
		删除数据库
		正在从盘中的不能删除
	*/
	tx := mysql.M.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
	}()

	var records []model.KlineControlRecordDB

	// 查询时加锁
	err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("id = ?", req.ControlID).
		Find(&records).Error

	if err != nil {
		tx.Rollback()
		return fmt.Errorf("查询失败: %w", err)
	}

	if len(records) == 0 {

		tx.Commit()
		return fmt.Errorf("没有找到控盘记录, ID: %s", req.ControlID)
	}

	for _, r := range records {
		if !r.IsEnd {
			tx.Rollback()
			return fmt.Errorf("❌ 当前产品仍在控盘中，无法删除")
		}
	}

	// 执行删除
	err = tx.Where("id = ?", req.ControlID).
		Delete(&model.KlineControlRecordDB{}).Error

	if err != nil {
		tx.Rollback()
		return fmt.Errorf("删除失败: %w", err)
	}

	// 先尝试删除Redis数据，如果失败则回滚事务
	err = func() error {
		defer func() {
			if r := recover(); r != nil {
				global.Lg.Error("删除Redis控盘历史数据发生panic",
					zap.String("userID", req.UserID),
					zap.String("symbol", records[0].Symbol),
					zap.Any("panic", r))
			}
		}()
		TempDelRedisKlineShowerHistory(req.UserID, "alltick", records[0].Symbol, "utc", records[0].StartTime, records[0].EndTime)
		return nil
	}()

	if err != nil {
		tx.Rollback()
		return fmt.Errorf("删除Redis控盘历史失败: %w", err)
	}

	// Redis删除成功后，提交数据库事务
	if err = tx.Commit().Error; err != nil {
		// 数据库提交失败，但Redis已经删除，记录警告日志
		global.Lg.Error("数据库事务提交失败，但Redis数据已删除，可能存在数据不一致",
			zap.String("userID", req.UserID),
			zap.String("symbol", records[0].Symbol),
			zap.Error(err))
		return fmt.Errorf("提交事务失败: %w", err)
	}

	return nil
}

func TempDelRedisKlineShowerHistory(userID, platform, symbol, timeZone string, startTime, endTime int64) {
	ctx := context.Background()
	tables := decouplingfunction.GetAllRedisTable(userID, platform, symbol, timeZone)

	global.Lg.Info("开始删除Redis K线历史数据",
		zap.String("用户ID", userID),
		zap.String("平台", platform),
		zap.String("交易对", symbol),
		zap.Int64("开始时间", startTime),
		zap.Int64("结束时间", endTime))

	for _, table := range tables {
		// 统一使用分钟级时间戳
		minScore := utils.GetMinuteStartTimestamp(startTime)
		maxScore := utils.GetMinuteStartTimestamp(endTime)

		// 先查询要删除的数据
		results, err := redis.RDB.ZRangeByScore(ctx, table, &goredislib.ZRangeBy{
			Min: cast.ToString(minScore),
			Max: cast.ToString(maxScore),
		}).Result()

		if err != nil {
			global.Lg.Error("查询Redis数据失败", zap.String("表名", table), zap.Error(err))
			continue
		}

		if len(results) == 0 {
			continue
		}

		// 批量删除
		pipe := redis.RDB.Pipeline()
		for _, result := range results {
			pipe.ZRem(ctx, table, result)
			pipe.Del(ctx, table+":"+result)
		}

		_, err = pipe.Exec(ctx)
		if err != nil {
			global.Lg.Error("删除Redis数据失败",
				zap.String("表名", table),
				zap.Int("数据条数", len(results)),
				zap.Error(err))
		} else {
			global.Lg.Info("删除Redis数据成功",
				zap.String("表名", table),
				zap.Int("数据条数", len(results)))
		}
	}
}

/*
删除单个产品的所有偏移量控制历史记录
*/
func DelControlHistoryAll(ctx context.Context, userID string, symbol string) error {
	tx := mysql.M.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
	}()

	var records []model.KlineControlRecordDB

	// 查询时加锁
	err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("user_id = ? AND symbol = ?", userID, symbol).
		Find(&records).Error

	if err != nil {
		tx.Rollback()
		return fmt.Errorf("查询失败: %w", err)
	}

	if len(records) == 0 {
		/*tx.Commit()
		return fmt.Errorf("没有找到控盘记录")*/
		/*
			由于现在 立即模式和渐变模式没有和老控盘融合,这里如果没有记录什么都不做,让后续逻辑可以删除老控盘
		*/
	}

	for _, r := range records {
		if !r.IsEnd {
			tx.Rollback()
			return fmt.Errorf("❌ 当前产品仍在控盘中，无法删除")
		}
	}

	// 执行删除
	err = tx.Where("user_id = ? AND symbol = ?", userID, symbol).
		Delete(&model.KlineControlRecordDB{}).Error

	if err != nil {
		tx.Rollback()
		return fmt.Errorf("删除失败: %w", err)
	}

	if err = tx.Commit().Error; err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}
	return nil
}
