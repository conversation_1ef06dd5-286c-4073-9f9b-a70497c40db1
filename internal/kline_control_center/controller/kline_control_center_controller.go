package controller

import (
	"context"
	"github.com/gin-gonic/gin"
	"net/http"
	"trading_tick_server/internal/kline_control_center/dto"
	"trading_tick_server/internal/kline_control_center/service"
	"trading_tick_server/lib/structs"
	serviceCommon "trading_tick_server/web/service/common"
)

func GetKlineOffsetHistory(c *gin.Context) {
	var request dto.GetKlineOffsetHistoryRequest
	if err := c.Bind(&request); err == nil {
		// 获取服务实例
		// 从内存中获取所有偏移数据
		allOffsets, err := service.GetControlHistory(context.Background(), request)
		if err != nil {
			c.JSON(http.StatusOK, structs.Response{Code: serviceCommon.CodeCommonError, Message: "获取偏移量失败" + err.Error(), Error: err.Error()})
			return
		}
		c.JSON(http.StatusOK, structs.Response{Code: serviceCommon.CodeSuccess, Message: "获取成功", Data: allOffsets})
		return
	} else {
		c.<PERSON><PERSON><PERSON>(http.StatusBadRequest, serviceCommon.ParamError(err))
	}
}

/*
删除单个控盘历史数据
*/
func DelKlineOffsetHistorySingle(c *gin.Context) {
	var req dto.DelKlineOffsetHistorySingleRequest
	if err := c.Bind(&req); err == nil {
		// 从内存中获取所有偏移数据
		err := service.DelControlHistorySingle(context.Background(), req)
		if err != nil {
			c.JSON(http.StatusOK, structs.Response{Code: serviceCommon.CodeCommonError, Message: "删除偏移量(立即模式)失败" + err.Error(), Error: err.Error()})
			return
		}
		c.JSON(http.StatusOK, structs.Response{Code: serviceCommon.CodeSuccess, Message: "删除成功"})
		return
	} else {
		c.JSON(http.StatusBadRequest, serviceCommon.ParamError(err))
	}
}
