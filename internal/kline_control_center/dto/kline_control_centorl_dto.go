package dto

type GetKlineOffsetHistoryRequest struct {
	// 用户ID
	UserID string `form:"user_id" binding:"required"`
	// token
	Token string `form:"token" binding:"required"`
	// 名称
	Symbol string `form:"symbol" binding:"required"`
	// 页码
	PageNum int `form:"page_num" binding:"omitempty"`
	// 每页多少条
	PageSize int `form:"page_size" binding:"omitempty"`
}

type DelKlineOffsetHistorySingleRequest struct {
	// 用户ID
	UserID string `json:"user_id" binding:"required"`
	// token
	Token string `json:"token" binding:"required"`
	// 名称
	ControlID string `json:"control_id" binding:"required"`
}
