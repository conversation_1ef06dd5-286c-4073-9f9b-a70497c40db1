package model

import (
	"trading_tick_server/common/gmysql"
)

/*
所有K线控制的记录,
这个表也用于判断某个产品是否在控盘中
*/
type KlineControlRecordDB struct {
	gmysql.GormModel
	// 用户ID
	UserID string `gorm:"column:user_id;type:int(11);not null;default:0;comment:用户ID" json:"user_id"`
	// 产品
	Symbol string `gorm:"column:symbol;type:varchar(32);not null;default:'';comment:产品" json:"symbol"`
	// 控盘模式 智能模式(Smart), 立即模式(immediate), 渐变模式(gradient), 线性模式(linear)
	ControlMode string `gorm:"column:control_type;type:varchar(15);not null;default:'';comment:控盘类型 智能模式(Smart), 立即模式(immediate), 渐变模式(gradient), 线性模式(linear)" json:"control_type"`
	// 开始时间
	StartTime int64 `gorm:"column:start_time;type:int(11);not null;default:0;comment:开始时间" json:"start_time"`
	// 结束时间
	EndTime int64 `gorm:"column:end_time;type:int(11);not null;default:0;comment:结束时间" json:"end_time"`
	// 控盘是否结束
	IsEnd bool `gorm:"column:is_end;type:tinyint(1);not null;default:0;comment:是否结束" json:"is_end"`
	// 是否在回落期
	//IsInFall bool `gorm:"column:is_in_fall;type:tinyint(1);not null;default:0;comment:是否在回落期" json:"is_in_fall"`
}

// TableName 设置表名
func (KlineControlRecordDB) TableName() string {
	return "kline_control_record"
}
