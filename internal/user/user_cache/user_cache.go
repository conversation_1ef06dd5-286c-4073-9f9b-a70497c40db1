package user_cache

import (
	"context"
	"fmt"
	"go.uber.org/zap"
	"sync"
	"sync/atomic"
	"time"
	"trading_tick_server/lib/function"
	"trading_tick_server/lib/global"
	"trading_tick_server/lib/mysql"
	"trading_tick_server/lib/redis"
	"trading_tick_server/lib/structs"
)

// key是用户ID
var cacheUser sync.Map

var (
	userFunc   atomic.Value // 使用 atomic.Value 存储只读副本
	userFuncMu sync.Mutex   // 写入时使用互斥锁
)

// 将键值对存储到 sync.Map 中
func cacheUserSetOne(key string, value structs.UserCache) {
	cacheUser.Store(key, value)
}

// 根据键获取值，如果键不存在，返回空数据 非nil
func CacheUserGetOne(key string) structs.UserCache {
	value, _ := cacheUser.Load(key)
	if value == nil {
		return structs.UserCache{}
	}
	return value.(structs.UserCache)
}

// 检查键是否存在
func CacheUserExist(key string) bool {
	_, exists := cacheUser.Load(key)
	return exists
}

// 读取操作，无需加锁
func CacheUserSliceGet() []structs.UserCache {
	return userFunc.Load().([]structs.UserCache)
}

// 写入操作，需要加锁
/*
将用户数据缓存到内存中
*/
func CacheUserSliceSet() {
	userFuncMu.Lock()
	defer userFuncMu.Unlock()
	if userFunc.Load() == nil {
		userFunc.Store([]structs.UserCache{})
	}

	var list []structs.User
	exists := make(map[string]bool)
	err := mysql.M.Model(&structs.User{}).Where("status = 1").Preload("UserSymbols").Find(&list).Error
	if len(list) < 1 {
		global.Lg.Info(
			"用户数据为空，或读取失败",
			zap.String("component", "CacheTickSliceSet"),
			zap.Error(err),
		)
		return
	}
	list2 := make([]structs.UserCache, 0, len(list))
	for _, v := range list {
		vTmp := structs.UserCache{
			ID:          v.ID,
			Username:    v.Username,
			Password:    v.Password,
			Token:       v.Token,
			KlineSymbol: function.GetUserSymbol(v.UserSymbols, "kline"),
			ObSymbol:    function.GetUserSymbol(v.UserSymbols, "orderBook"),
			UserSymbols: v.UserSymbols,
		}
		list2 = append(list2, vTmp)
		cacheUserSetOne(fmt.Sprintf("%v", v.ID), vTmp)
		exists[fmt.Sprintf("%v", v.ID)] = true
	}
	userFunc.Store(list2)
	var toDelete []string
	// 遍历sync.Map 删除不存在的数据
	cacheUser.Range(func(key, value interface{}) bool {
		k, ok := key.(string)
		if ok && !exists[k] {
			toDelete = append(toDelete, k)
		}
		return true
	})
	for _, key := range toDelete {
		cacheUser.Delete(key)
	}
}

/*
缓存-用户模块
*/
func SetUserToken(ctx context.Context, userId string, token string, expiration time.Duration) error {
	key := redis.RDBPrefix + ":users:" + userId
	_, err := redis.RDB.Set(ctx, key, token, expiration).Result()
	if err != nil {
		return fmt.Errorf("redis SET failed: %w", err)
	}
	return nil
}

func GetUserToken(ctx context.Context, userId string) (string, error) {
	return redis.RDB.Get(ctx, redis.RDBPrefix+":users:"+userId).Result()
}
