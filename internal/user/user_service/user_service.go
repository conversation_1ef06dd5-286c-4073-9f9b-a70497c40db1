package user_service

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"
	"trading_tick_server/internal/user/user_cache"
	"trading_tick_server/internal/user/user_model"
	"trading_tick_server/lib/function"
	"trading_tick_server/lib/mysql"
	"trading_tick_server/lib/structs"
	service_common "trading_tick_server/web/service/common"

	goredis "github.com/redis/go-redis/v9"
	"github.com/sknun/cf/cast"
	"gorm.io/gorm/clause"
)

// ⚠️注意 服务器启动时应该清空用户缓存,以免数据库有变动,然后缓存中还是老的 token 导致用户无法操作
// 完成功能来写

type userServer struct {
}

var (
	userInstance *userServer
	userOnce     sync.Once
)

func NewUserServerInstance() *userServer {
	userOnce.Do(func() {
		userInstance = &userServer{}
	})
	return userInstance
}

// 设置用户Tokend到缓存
func (u *userServer) SetTokenToCache(ctx context.Context, userId int64, token string, expiration time.Duration) error {
	// 设置用户Token到缓存
	err := user_cache.SetUserToken(ctx, cast.ToString(userId), token, expiration)
	if err != nil {
		return fmt.Errorf("failed to set user token: %w", err)
	}
	return nil
}

// 设置用户Token到数据库
func (u *userServer) SetTokenToDB(ctx context.Context, userId int64, token string) error {
	// 更新用户Token到数据库
	dbResult := mysql.M.Model(&user_model.User{}).Where("id = ?", userId).Update("token", token)
	if dbResult.Error != nil {
		return fmt.Errorf("failed to update user token: %w", dbResult.Error)
	}
	if dbResult.RowsAffected == 0 {
		return errors.New("user not found")
	}
	return nil
}

// 获取用户Token
/*
1. 先从缓存中获取用户Token
2. 如果缓存中不存在，则从数据库中获取
3. 如果数据库中也不存在，则返回错误
4. 如果获取成功，则将Token存入缓存
*/
func (u *userServer) GetToken(ctx context.Context, userId int64) (string, error) {
	// 用户ID 和 token,在redis中查询, 如果没有再去数据库中查询
	token, err := user_cache.GetUserToken(ctx, cast.ToString(userId))
	if errors.Is(err, goredis.Nil) { // key不存在
		// 缓存中未找到, 需要去数据库中查询
		dbResult := mysql.M.Model(&user_model.User{}).Where("id = ?", userId).Select("token").Scan(&token)
		if dbResult.Error != nil {
			// 查询发生错误
			return "", fmt.Errorf("failed to get user token: %w", dbResult.Error)
		}

		if dbResult.RowsAffected == 0 {
			// 用户不存在或没有 token
			return "", errors.New("user not found")
		}
		// 将查询到的 token 存入缓存
		err = user_cache.SetUserToken(ctx, cast.ToString(userId), token, 0)
		if err != nil {
			fmt.Printf("failed to cache user token: %v", err)
		}
		// 返回查询到的 token
		return token, nil
	} else if err != nil {
		return "", err // 其他错误
	} else {
		return token, nil // 成功
	}
}

/*
设置用户可订阅产品信息
*/
func (u *userServer) SetUserSymbols(userId uint, symbols []user_model.UserUpdateRequestSymbol) error {
	/*
		找出所有产品ID
	*/
	var symbolsID []uint
	for _, symbol := range symbols {
		symbolsID = append(symbolsID, symbol.ID)
	}
	/*
		1. 根据 symbols 获取到产品信息
		2. 获取现有的用户产品配置
		3. 组装成 UserSymbols (存在的话DecimalPlaces, FloatRange 优先用数据库的值)
		4. 更新到数据库
		5. 更新到缓存
	*/
	// 1. 根据 symbols 获取到产品信息
	var ticks []structs.Tick
	err := mysql.M.Model(&structs.Tick{}).Where("id IN ?", symbolsID).Find(&ticks).Error
	if err != nil {
		return fmt.Errorf("failed to get ticks data: %w", err)
	}

	// 2. 获取现有的用户产品配置
	var existingUserSymbols []structs.UserSymbol
	err = mysql.M.Model(&structs.UserSymbol{}).Where("user_id = ?", userId).Find(&existingUserSymbols).Error
	if err != nil {
		return fmt.Errorf("failed to get existing user symbols: %w", err)
	}

	// 创建现有配置的映射，便于快速查找
	existingMap := make(map[string]structs.UserSymbol)
	for _, existing := range existingUserSymbols {
		key := fmt.Sprintf("%d_%s_%s_%s", existing.SymbolID, existing.Type, existing.Platform, existing.Symbol)
		existingMap[key] = existing
	}

	/*
		3. 组装成 UserSymbols
	*/
	var userSymbols []structs.UserSymbol
	for _, symbol := range symbols {
		for _, tick := range ticks {
			if symbol.ID == tick.ID {
				key := fmt.Sprintf("%d_%s_%s_%s", symbol.ID, symbol.Type, tick.Platform, tick.Symbol)

				// 检查是否已存在相同的配置
				if existing, exists := existingMap[key]; exists {
					// 存在的话，DecimalPlaces 和 FloatRange 优先用数据库的值
					userSymbols = append(userSymbols, structs.UserSymbol{
						UserID:        userId,
						SymbolID:      symbol.ID,
						Type:          symbol.Type,
						Platform:      tick.Platform,
						Symbol:        tick.Symbol,
						DecimalPlaces: existing.DecimalPlaces, // 优先使用数据库的值
						FloatRange:    existing.FloatRange,    // 优先使用数据库的值
					})
				} else {
					// 不存在则使用新传入的值
					userSymbols = append(userSymbols, structs.UserSymbol{
						UserID:        userId,
						SymbolID:      symbol.ID,
						Type:          symbol.Type,
						Platform:      tick.Platform,
						Symbol:        tick.Symbol,
						DecimalPlaces: symbol.DecimalPlaces,
						FloatRange:    symbol.FloatRange,
					})
				}
			}
		}
	}
	/*
		先删除这个用户所有的 user_symbol 数据
	*/
	if err = mysql.M.Where("user_id = ?", userId).Delete(&structs.UserSymbol{}).Error; err != nil {
		return err
	}
	/*
		再插入新的 user_symbol 数据
	*/
	if len(userSymbols) == 0 {
		return nil // 如果没有要插入的数据,直接返回
	}
	err = mysql.M.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "user_id"}, {Name: "symbol"}, {Name: "type"}, {Name: "platform"}},
		DoNothing: true,
	}).Create(&userSymbols).Error

	if err != nil {
		return fmt.Errorf("failed to create user symbols: %w", err)
	}
	return nil
}

// 获取用户的symbol列表
func (u *userServer) GetUserSymbols(userId uint) ([]structs.UserSymbol, error) {
	var userSymbols []structs.UserSymbol
	err := mysql.M.Model(&structs.UserSymbol{}).Where("user_id = ?", userId).Find(&userSymbols).Error
	if err != nil {
		return nil, err
	}
	return userSymbols, nil
}

/*
更新会员信息
*/
func (u *userServer) PutUserUpdate(req *user_model.PutUserRequest) structs.Response {
	var param structs.User
	if err := mysql.M.First(&param, req.ID).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: "无法找到用户"}
	}
	param.Username = req.Username
	if req.Password != "" {
		param.Password = function.HashPasswordArgon2(req.Password)
	}
	param.Token = req.Token
	param.Status = req.Status
	if req.StartDuration > 0 {
		param.StartDuration = req.StartDuration
	}
	if req.EndDuration > 0 {
		param.EndDuration = req.EndDuration
	}
	if req.Remark != "" {
		param.Remark = req.Remark
	}
	if err := mysql.M.Save(&param).Error; err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: "修改失败"}
	}

	err := u.SetUserSymbols(req.ID, req.Symbols)
	if err != nil {
		return structs.Response{Code: service_common.CodeDBError, Message: "修改失败"}
	}
	/*
		缓存用户信息
	*/
	user_cache.CacheUserSliceSet()
	return structs.Response{Code: service_common.CodeSuccess, Message: "ok"}
}
