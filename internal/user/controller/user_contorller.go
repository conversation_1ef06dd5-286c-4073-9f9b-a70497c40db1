package user_controller

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"trading_tick_server/internal/user/user_model"
	"trading_tick_server/internal/user/user_service"
	service_common "trading_tick_server/web/service/common"
)

// @Summary 修改用户
// @Description
// @Tags 后台/用户管理
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param   request  body  user_model.PutUserRequest  true  "参数"
// @Success 200 {object} structs.Response{data=object} "返回码看对照表"
// @Router /admin/User/User [PUT]
func PutUser(c *gin.Context) {
	var request user_model.PutUserRequest
	if err := c.Bind(&request); err == nil {
		res := user_service.NewUserServerInstance().PutUserUpdate(&request)
		c.JSON(http.StatusOK, res)
	} else {
		c.JSON(http.StatusOK, service_common.ParamError(err))
	}
}
