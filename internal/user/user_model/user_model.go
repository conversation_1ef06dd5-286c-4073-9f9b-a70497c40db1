package user_model

import (
	"trading_tick_server/common/gmysql"
)

type User struct {
	gmysql.GormModel
	Username               string       `gorm:"uniqueIndex;type:varchar(100);not null;default:'';" json:"username"`                    // 账号
	Password               string       `gorm:"type:varchar(100);not null;default:'';" json:"password"`                                // 密码
	Token                  string       `gorm:"type:varchar(255);not null;default:'';" json:"token"`                                   // token
	Status                 uint8        `gorm:"index;not null;default:1;" json:"status"`                                               // 状态 0禁用 1启用
	KlineQueryNumber1m     int64        `gorm:"column:kline_query_number_1m;not null;default:0;" json:"kline_query_number_1m"`         // k线可查询最多数量
	KlineQueryNumber5m     int64        `gorm:"column:kline_query_number_5m;not null;default:0;" json:"kline_query_number_5m"`         // k线可查询最多数量
	KlineQueryNumber15m    int64        `gorm:"column:kline_query_number_15m;not null;default:0;" json:"kline_query_number_15m"`       // k线可查询最多数量
	KlineQueryNumber30m    int64        `gorm:"column:kline_query_number_30m;not null;default:0;" json:"kline_query_number_30m"`       // k线可查询最多数量
	KlineQueryNumber1hour  int64        `gorm:"column:kline_query_number_1hour;not null;default:0;" json:"kline_query_number_1hour"`   // k线可查询最多数量
	KlineQueryNumber2hour  int64        `gorm:"column:kline_query_number_2hour;not null;default:0;" json:"kline_query_number_2hour"`   // k线可查询最多数量
	KlineQueryNumber4hour  int64        `gorm:"column:kline_query_number_4hour;not null;default:0;" json:"kline_query_number_4hour"`   // k线可查询最多数量
	KlineQueryNumber1day   int64        `gorm:"column:kline_query_number_1day;not null;default:0;" json:"kline_query_number_1day"`     // k线可查询最多数量
	KlineQueryNumber1week  int64        `gorm:"column:kline_query_number_1week;not null;default:0;" json:"kline_query_number_1week"`   // k线可查询最多数量
	KlineQueryNumber1month int64        `gorm:"column:kline_query_number_1month;not null;default:0;" json:"kline_query_number_1month"` // k线可查询最多数量
	Remark                 string       `gorm:"type:varchar(100);not null;default:'';" json:"remark"`                                  // 备注
	UserSymbols            []UserSymbol `gorm:"foreignKey:UserID" json:"user_symbols"`
}

/*
用户可用的产品表
Type kline 获取k线 orderbook 盘口
*/
type UserSymbol struct {
	UserID        uint   `gorm:"index;not null;default:0;" json:"user_id"`              // 用户ID
	Type          string `gorm:"type:varchar(30);not null;default:'';" json:"type"`     // 类型
	Platform      string `gorm:"type:varchar(30);not null;default:'';" json:"platform"` // 产品平台
	Symbol        string `gorm:"type:varchar(30);not null;default:'';" json:"symbol"`   // 产品标识
	DecimalPlaces int32  `gorm:"not null;default:0;" json:"decimal_places"`             // 小数位数
}
