package user_model

type PutUserRequest struct {
	// ID
	ID uint `json:"id" binding:"required,min=1"`
	// 账号
	Username string `json:"username" binding:"required"`
	// 密码
	Password string `json:"password,omitempty"`
	// token
	Token string `json:"token" binding:"required"`
	// 状态 0禁用 1启用
	Status uint8 `json:"status" binding:"min=0,max=1"`
	// 用户权限(可获取的数据资源,每一种产品需要两个 Symbol 一个 kline, 一个 orderBook)
	Symbols       []UserUpdateRequestSymbol `json:"symbols"` // 用户可订阅的产品信息
	StartDuration int32                     `json:"start_duration" gorm:"column:start_duration;default:60;comment:开始控盘偏移时间(秒)"`
	EndDuration   int32                     `json:"end_duration" gorm:"column:end_duration;default:60;comment:结束控盘偏移时间(秒)"`
	Remark        string                    `gorm:"type:varchar(100);not null;default:'';" json:"remark"`
}

type UserUpdateRequestSymbol struct {
	ID            uint    `json:"id"`                                            // 产品ID
	Type          string  `json:"type" binding:"required,oneof=orderBook kline"` // 注意类型必须是 orderBook 或 kline
	DecimalPlaces uint    `json:"decimal_places"`                                // 小数位数
	FloatRange    float64 `json:"float_range" binding:"min=0"`                   // 浮动绝对值范围，默认0.2
}
