package service

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"
	"trading_tick_server/common/utils"
	kcache "trading_tick_server/internal/kline_offset_linear/cache"
	kol_cache "trading_tick_server/internal/kline_offset_linear/cache"
	"trading_tick_server/internal/kline_offset_linear/dto"
	"trading_tick_server/internal/kline_offset_linear/model"
	"trading_tick_server/internal/kline_offset_linear/repository"
	"trading_tick_server/internal/user/use_iface"
	"trading_tick_server/lib/cache"
	"trading_tick_server/lib/mysql"
	"trading_tick_server/lib/symbol_price"

	cmap "github.com/orcaman/concurrent-map/v2"
	"github.com/shopspring/decimal"
	"github.com/sknun/cf/cast"
)

/*
1. 服务器启动时创建一个 KlineOffsetlinear 实例
2. 去缓存查询是否有未完成的偏移量控制
*/

type klineOffsetLinear struct {
	UserService  use_iface.UserService                                                                  // 注意 user 服务 和 User 模块不是一个概念
	OffsetInfo   cmap.ConcurrentMap[string, cmap.ConcurrentMap[string, decimal.Decimal]]                //[用户ID]map[产品]总偏移量(所有用户,所有产品),这个是控制信息
	ControlInfos cmap.ConcurrentMap[string, cmap.ConcurrentMap[string, *model.SingleSymbolControlInfo]] //[用户ID]map[产品]控盘信息,这个是控制之后的数据
}

var (
	klineOffsetLinearInstance *klineOffsetLinear
	klineOffsetLinearOnce     sync.Once
)

// 单例构造函数
func NewKlineOffsetLinear(user use_iface.UserService) *klineOffsetLinear {
	klineOffsetLinearOnce.Do(func() {
		klineOffsetLinearInstance = &klineOffsetLinear{
			UserService:  user,
			OffsetInfo:   cmap.New[cmap.ConcurrentMap[string, decimal.Decimal]](),
			ControlInfos: cmap.New[cmap.ConcurrentMap[string, *model.SingleSymbolControlInfo]](),
		}

		ctx := context.Background()

		// 1. 从缓存中获取所有的偏移量控制(线性模式)数据
		err := kcache.GetAllKlineOffsetLinearFromCache(ctx, klineOffsetLinearInstance.OffsetInfo)
		if err != nil {
			fmt.Printf("❌从缓存初始化偏移量控制(线性模式)失败, err: %v\n", err)
		}

		// 2. 从数据库恢复未结束的控盘记录，确保数据一致性
		err = klineOffsetLinearInstance.recoverUnfinishedControlFromDB(ctx)
		if err != nil {
			fmt.Printf("❌从数据库恢复未结束控盘记录失败, err: %v\n", err)
		}

		// 3. 打印所有的偏移量控制(线性模式)数据
		if klineOffsetLinearInstance.OffsetInfo.Count() > 0 {
			fmt.Printf("🔧初始化偏移量控制(线性模式)模块\n")
			for userID, symbolMap := range klineOffsetLinearInstance.OffsetInfo.Items() {
				fmt.Printf("用户ID : %s\n", userID)
				for symbol, offset := range symbolMap.Items() {
					fmt.Printf("  产品: %s, OffsetInfo: %s\n", symbol, offset.String())
				}
			}
			fmt.Printf("\n")
		}
	})

	return klineOffsetLinearInstance
}

// validateOffsetAgainstCoinValue 检查偏移量是否超出币种价值
func (k *klineOffsetLinear) validateOffsetAgainstCoinValue(req dto.PostKlineOffsetLinearRequest) error {
	// 检查向下偏移量是否超出币种价值
	if req.Direction == "down" {
		offsetValue := req.Offset.InexactFloat64()
		// 获取当前币种价格
		currentPrice, err := k.getCurrentCoinPrice(req.UserID, req.Symbol)
		if err != nil {
			return fmt.Errorf("获取当前币种价格失败: %v", err)
		}
		if offsetValue >= currentPrice {
			return fmt.Errorf("偏移量过大，超出本币价值，请重新设置")
		}
	}

	return nil
}

// getCurrentCoinPrice 获取当前币种价格
func (k *klineOffsetLinear) getCurrentCoinPrice(userID, symbol string) (float64, error) {
	// 1. 检查是否已有控盘，如果有则使用控盘后的当前价格
	if inner, exists := k.OffsetInfo.Get(userID); exists {
		if _, symbolExists := inner.Get(symbol); symbolExists {
			// 已有控盘，获取控盘后的当前价格
			controlPrice, err := k.getControlledCurrentPrice(userID, symbol)
			if err == nil {
				return controlPrice, nil
			}
			// 如果获取控盘价格失败，继续使用市场价格
		}
	}

	// 2. 无控盘或获取控盘价格失败，使用当前市场行情价
	marketPrice, err := k.getMarketCurrentPrice(symbol)
	if err != nil {
		return 0, fmt.Errorf("获取市场价格失败: %v", err)
	}
	return marketPrice, nil
}

// getControlledCurrentPrice 获取控盘后的当前价格
func (k *klineOffsetLinear) getControlledCurrentPrice(userID, symbol string) (float64, error) {
	// 获取市场价格
	marketPrice := symbol_price.Get("alltick", symbol)
	if marketPrice <= 0 {
		return 0, fmt.Errorf("无法获取 %s 的市场价格", symbol)
	}

	// 获取当前的总偏移量
	userOffsetInfo, exists := k.OffsetInfo.Get(userID)
	if !exists {
		return marketPrice, nil // 没有偏移量，返回市场价格
	}

	totalOffset, exists := userOffsetInfo.Get(symbol)
	if !exists {
		return marketPrice, nil // 没有偏移量，返回市场价格
	}

	// 返回市场价格 + 偏移量
	controlledPrice := marketPrice + totalOffset.InexactFloat64()
	return controlledPrice, nil
}

// getMarketCurrentPrice 获取当前市场行情价
func (k *klineOffsetLinear) getMarketCurrentPrice(symbol string) (float64, error) {
	// 使用 symbol_price 库获取最新市场价格
	currentPrice := symbol_price.Get("alltick", symbol)
	if currentPrice <= 0 {
		return 0, fmt.Errorf("无法获取 %s 的市场价格", symbol)
	}

	return currentPrice, nil
}

// recoverUnfinishedControlFromDB 从数据库恢复未结束的控盘记录
func (k *klineOffsetLinear) recoverUnfinishedControlFromDB(ctx context.Context) error {
	// 查询数据库中未结束的线性模式控盘记录
	unfinishedRecords, err := repository.GetUnfinishedLinearControlRecords(ctx)
	if err != nil {
		return fmt.Errorf("查询未结束线性控盘记录失败: %v", err)
	}

	if len(unfinishedRecords) == 0 {
		return nil
	}

	fmt.Printf("🔄 发现 %d 个未结束的线性控盘记录，开始恢复...\n", len(unfinishedRecords))

	for _, record := range unfinishedRecords {
		userID := cast.ToString(record.UserID)
		symbol := record.Symbol

		// 检查内存中是否已有该控盘信息
		if inner, exists := k.OffsetInfo.Get(userID); exists {
			if _, symbolExists := inner.Get(symbol); symbolExists {
				fmt.Printf("✅ 线性控盘记录已存在于内存中: UserID=%s, Symbol=%s\n", userID, symbol)
				continue
			}
		}

		// 从数据库恢复偏移量信息
		offsetRecord, err := repository.GetKlineOffsetLinearByUserAndSymbol(ctx, record.UserID, symbol)
		if err != nil {
			fmt.Printf("❌ 获取线性偏移量记录失败: UserID=%s, Symbol=%s, err=%v\n", userID, symbol, err)
			continue
		}

		if offsetRecord == nil {
			fmt.Printf("⚠️  未找到对应的线性偏移量记录: UserID=%s, Symbol=%s\n", userID, symbol)
			continue
		}

		// 恢复到 Redis 缓存
		offsetLinear := &model.KlineOffsetLinearRedis{
			UserID:      userID,
			Symbol:      symbol,
			ControlTime: offsetRecord.ControlTime, // 使用数据库中的控制时间
			TotalOffset: offsetRecord.TotalOffset,
		}

		err = kcache.SetKlineOffsetLinearToCache(ctx, record.UserID, symbol, *offsetLinear, 0)
		if err != nil {
			fmt.Printf("❌ 恢复线性Redis缓存失败: UserID=%s, Symbol=%s, err=%v\n", userID, symbol, err)
			continue
		}

		// 恢复到内存
		inner, _ := k.OffsetInfo.Get(userID)
		inner.Set(symbol, offsetRecord.TotalOffset)
		k.OffsetInfo.Set(userID, inner)

		fmt.Printf("✅ 成功恢复线性控盘记录: UserID=%s, Symbol=%s, Offset=%s\n",
			userID, symbol, offsetRecord.TotalOffset.String())
	}
	return nil
}

// 开始执行K线偏移量控制
func (k *klineOffsetLinear) StartControl(ctx context.Context, req dto.PostKlineOffsetLinearRequest) error {
	// 打印请求参数
	fmt.Printf("🚀 StartControl 请求参数: UserID=%s, Symbol=%s, Offset=%v, Direction=%s, Type=%s, AsksPriceMin=%v, AsksPriceMax=%v, OrderBookDiffMin=%v, OrderBookDiffMax=%v\n",
		req.UserID, req.Symbol, req.Offset, req.Direction, req.Type, req.AsksPriceMin, req.AsksPriceMax, req.OrderBookDiffMin, req.OrderBookDiffMax)

	/*
		这里可以优化为需要加一个启动控盘检查,如果在启动时发现有已存在控盘,就要自动建立房间,并开启控盘
		房间的创建还是应该放在这里,等忙完了来,模块之间的责任应该划分清楚
	*/
	// 1. 参数校验
	if err := k.PostValidate(ctx, req); err != nil {
		return err
	}

	// 检查偏移量是否超出币种价值
	if err := k.validateOffsetAgainstCoinValue(req); err != nil {
		return err
	}

	// 2. 开启事务
	tx := mysql.M.Begin()
	committed := false
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
		if !committed {
			tx.Rollback()
		}
	}()

	/*
		3. 控盘状态检查（含事务锁）
		⚠️注意: 注意参数 isCanControl 和 isInControl 的区别
		isCanControl: 是否可以控盘,是否可以进行这次操作,可能这个产品已经有其他的控盘模式
		isInControl: 是否在控盘中,在一个控盘周期中
	*/
	isCanControl, isInControl, err := k.IsCanIsInControl(req.UserID, req.Symbol, req, tx)
	if err != nil || !isCanControl {
		return err
	}

	// 4. 设置缓存并计算偏移量
	offsetLinear, err := k.setupOffsetLinearCache(ctx, req)
	if err != nil {
		return err
	}

	// 5. 持久化数据库记录（控盘记录和偏移记录）
	err = k.saveControlData(ctx, req, offsetLinear, isInControl, tx)
	if err != nil {
		return err
	}

	// 6. 内存中的控制信息更新或初始化,注意参数为是否控盘中
	err = k.updateControlInfoMemory(req, offsetLinear.TotalOffset, isInControl)
	if err != nil {
		return err
	}

	// 7. 启动控盘逻辑
	err = k.DataControl(req.UserID, req.Symbol)
	if err != nil {
		return fmt.Errorf("❌偏移量(立即模式) Start Control 失败, err: %v", err)
	}

	// 8. 提交事务
	committed = true
	return tx.Commit().Error
}

// 停止执行K线偏移量控制
func (k *klineOffsetLinear) StopControl(ctx context.Context, req dto.DelKlineOffsetLinearRequest) error {
	// 1. 参数校验
	if err := k.StopValidate(ctx, req); err != nil {
		return err
	}
	// 1. 尝试获取缓存中的控制信息
	offsetCache, err := kcache.GetKlineOffsetLinearFromCache(ctx, req.UserID, req.Symbol)
	if err != nil {
		return fmt.Errorf("❌获取偏移量控制(线性模式)缓存记录失败,err : %v", err)
	} else if offsetCache.UserID == "" {
		return fmt.Errorf("❌控盘正在停止中 或 没有偏移量控制")
	}

	// 2. 删除缓存记录
	err = kcache.DeleteKlineOffsetLinearFromCache(ctx, req.UserID, req.Symbol)
	if err != nil {
		return fmt.Errorf("❌删除偏移量控制(线性模式)缓存记录失败, err : %v", err)
	}

	m := &model.KlineOffsetLinearDB{
		UserID:       req.UserID,
		Symbol:       req.Symbol,
		Offset:       decimal.Zero,
		Direction:    "over", // 停止
		TotalOffset:  decimal.Zero,
		ControlTime:  time.Now().Unix(),
		ControlPrice: decimal.Zero,
		IsOffset:     true,
	}
	err = repository.SaveKlineOffsetLinear(ctx, mysql.M, m)
	if err != nil {
		return fmt.Errorf("❌停止偏移, 保存偏移量控制(线性模式)记录失败, err : %v", err)
	}

	// ================================== 停止逻辑 ================================
	/*
		1. 记录数据,产品停止控盘时间
		在偏移量(线性模式)中,真正的停止控盘是将结束时间设置为当前时间+60秒,应为有60秒的回落期
	*/
	controlInfo, err := k.GetSingleSymbolControlInfo(req.UserID, req.Symbol)
	if err != nil {
		return fmt.Errorf("❌获取偏移量控制(线性模式)内存控盘信息失败, err : %v", err)
	}
	// 计算正确的结束时间：controlInfo.EndDuration //60秒回落期 + 等待K线结束
	nowUnix := time.Now().Unix()
	endTime := utils.GetControlEndTimeUnix(nowUnix, int64(controlInfo.EndDuration))
	controlInfo.EndTime = cast.ToString(endTime)
	fmt.Printf("🕐 计算回落期结束时间: 当前时间=%d, EndDuration=%d, 计算结果=%d, 回落期长度=%d秒\n",
		nowUnix, controlInfo.EndDuration, endTime, endTime-nowUnix)

	err = k.OverControl(req.UserID, req.Symbol)
	if err != nil {
		return fmt.Errorf("❌偏移量(线性模式) Over Control 失败, err: %v", err)
	}
	fmt.Println("🐶停止偏移量(线性模式)控盘,用户ID: ", req.UserID, "产品: ", req.Symbol)

	return nil
}

// cleanupMemoryInfo 清理内存中的控盘信息
func (k *klineOffsetLinear) cleanupMemoryInfo(userID, symbol string) {
	// 清理 OffsetInfo
	if inner, exists := k.OffsetInfo.Get(userID); exists {
		inner.Remove(symbol)
		if inner.Count() == 0 {
			k.OffsetInfo.Remove(userID)
		}
	}

	// 清理 ControlInfos
	if inner, exists := k.ControlInfos.Get(userID); exists {
		inner.Remove(symbol)
		if inner.Count() == 0 {
			k.ControlInfos.Remove(userID)
		}
	}

	fmt.Printf("✅ 已清理内存控盘信息: UserID=%s, Symbol=%s\n", userID, symbol)
}

// 获取偏移量控制(线性模式)列表
func (k *klineOffsetLinear) GetControlList(ctx context.Context, req dto.GetKlineOffsetLinearListRequest) ([]model.KlineOffsetLinearRedis, error) {
	// 1. 参数校验
	if err := k.GetValidate(ctx, req); err != nil {
		return nil, err
	}
	offsetList := make([]model.KlineOffsetLinearRedis, 0)

	// 填充 k.OffsetInfo（线程安全 map）从 Redis 中
	err := kcache.GetAllKlineOffsetLinearFromCache(ctx, k.OffsetInfo)
	if err != nil {
		return nil, fmt.Errorf("❌获取偏移量控制(线性模式)列表失败, err : %v", err)
	}

	// 遍历外层：userID -> innerMap[symbol]decimal
	for userID, innerMap := range k.OffsetInfo.Items() {
		if userID == req.UserID {
			for symbol, offset := range innerMap.Items() {
				offsetList = append(offsetList, model.KlineOffsetLinearRedis{
					UserID:      userID,
					Symbol:      symbol,
					TotalOffset: offset,
				})
			}
			break
		}
	}

	// 打印一下所有信息
	for userID, symbolMap := range klineOffsetLinearInstance.OffsetInfo.Items() {
		fmt.Printf("UserID: %s\n", userID)
		for symbol, offset := range symbolMap.Items() {
			fmt.Printf("ymbol: %s, OffsetInfo: %s\n", symbol, offset.String())
		}
	}

	return offsetList, nil
}

/*
	恢复(服务器启动时调用,现在暂时先在启动时删除没有完成的控制,等空了再来做真正的启动恢复)

出错直接 panic
*/
func OffsetLinearRecover() {
	ctx := context.Background()
	// 1. 尝试获取缓存中的控制信息
	offsetLinearCache, err := kol_cache.GetAllKlineOffsetLinear(ctx)
	if err != nil {
		panic(fmt.Errorf("❌获取偏移量控制(线性模式)缓存记录失败,err : %v\n", err))
	}
	// 2. 遍历所有的偏移量控制(线性模式)数据,依次删除
	for _, offset := range offsetLinearCache {
		// 删除缓存
		err = kol_cache.DeleteKlineOffsetLinearFromCache(ctx, offset.UserID, offset.Symbol)
		if err != nil {
			panic(fmt.Errorf("❌删除偏移量控制(线性模式)缓存记录失败, err : %v", err))
		}
		m := &model.KlineOffsetLinearDB{
			UserID:       offset.UserID,
			Symbol:       offset.Symbol,
			Offset:       decimal.Zero,
			Direction:    "over", // 停止
			TotalOffset:  decimal.Zero,
			ControlTime:  time.Now().Unix(),
			ControlPrice: decimal.Zero,
			IsOffset:     true,
		}
		err = repository.SaveKlineOffsetLinear(ctx, mysql.M, m)
		if err != nil {
			panic(fmt.Errorf("❌恢复偏移量控制(线性模式)数据失败, err : %v", err))
		}
	}
}

/*
===========================================================================================
*/
// Post参数校验
func (k *klineOffsetLinear) PostValidate(ctx context.Context, req dto.PostKlineOffsetLinearRequest) error {
	// 1. 用户以及token
	token, err := k.UserService.GetToken(ctx, cast.ToInt64(req.UserID))
	if err != nil {
		return err
	}

	if token != req.Token {
		fmt.Printf("token 错误, 用户ID: %v, 请求Token : %v, 缓存 Token : %v", req.UserID, req.Token, token)
		return fmt.Errorf("token 错误, 用户ID: %v", req.UserID)
	}

	// 2. 产品是否存在
	var existSymbol bool
	// 查询哪个平台有当前请求的这个产品的数据
	/*	for _, v := range cache.CacheTickSliceGet() {
		fmt.Printf("🤔这个ChacheTickSliceGet 里面是啥?%v\n", v)
	}*/

	for _, v := range cache.CacheTickSliceGet() {
		if v.Symbol == req.Symbol {
			existSymbol = true
			break
		}
	}
	if !existSymbol {
		return errors.New("产品不存在")
	}

	return nil
}

// Stop参数校验
func (k *klineOffsetLinear) StopValidate(ctx context.Context, req dto.DelKlineOffsetLinearRequest) error {
	// 1. 用户以及token
	token, err := k.UserService.GetToken(ctx, cast.ToInt64(req.UserID))
	if err != nil {
		return err
	}

	if token != req.Token {
		return errors.New("token 错误")
	}

	// 2. 产品是否存在
	var existSymbol bool
	// 查询哪个平台有当前请求的这个产品的数据
	/*	for _, v := range cache.CacheTickSliceGet() {
		fmt.Printf("🤔这个ChacheTickSliceGet 里面是啥?%v\n", v)
	}*/

	for _, v := range cache.CacheTickSliceGet() {
		if v.Symbol == req.Symbol {
			existSymbol = true
			break
		}
	}
	if !existSymbol {
		return errors.New("产品不存在")
	}

	return nil
}

// Get 参数校验
func (k *klineOffsetLinear) GetValidate(ctx context.Context, req dto.GetKlineOffsetLinearListRequest) error {
	// 1. 用户以及token
	token, err := k.UserService.GetToken(ctx, cast.ToInt64(req.UserID))
	if err != nil {
		return err
	}

	if token != req.Token {
		return errors.New("token 错误")
	}
	return nil
}
