package service

import (
	"context"
	"fmt"
	"time"
	"trading_tick_server/common/utils"
	"trading_tick_server/internal/data_transfer_station/service"
	"trading_tick_server/internal/data_transfer_station/types"
	kcc_model "trading_tick_server/internal/kline_control_center/model"
	kcc_service "trading_tick_server/internal/kline_control_center/service"
	kcc_types "trading_tick_server/internal/kline_control_center/types"
	kol_cache "trading_tick_server/internal/kline_offset_linear/cache"
	"trading_tick_server/internal/kline_offset_linear/dto"
	"trading_tick_server/internal/kline_offset_linear/model"
	"trading_tick_server/internal/kline_offset_linear/repository"
	"trading_tick_server/lib/mysql"
	"trading_tick_server/lib/structs"

	cmap "github.com/orcaman/concurrent-map/v2"
	"github.com/shopspring/decimal"
	"github.com/sknun/cf/cast"
	"gorm.io/gorm"
)

/*
IsCanIsInControl 检查这次操作是否可以进行控制
true 代表可以进行控制或调整
*/
func (k *klineOffsetLinear) IsCanIsInControl(userID string, symbol string, req dto.PostKlineOffsetLinearRequest, tx *gorm.DB) (bool, bool, error) {
	record, err := kcc_service.GetControlRecordWithLock(userID, symbol, tx)
	if err != nil {
		return false, false, fmt.Errorf("❌获取控盘记录失败: %v", err)
	}
	if record == nil {
		/*
			没有控盘记录, 说明没有控盘,返回可以进行控盘
		*/
		return true, false, nil
	}
	// 检查控盘类型互斥
	if req.Type == "linear" {
		// 线性模式只能在线性控盘中进行
		if record.ControlMode != string(kcc_types.ENUM_KLINE_CONTROL_TYPE_LINEAR) {
			if record.ControlMode == string(kcc_types.ENUM_KLINE_CONTROL_TYPE_GRADIENT) {
				return false, false, fmt.Errorf("❌%s 正在渐变控盘，请在控盘结束后重试。", symbol)
			} else if record.ControlMode == string(kcc_types.ENUM_KLINE_CONTROL_TYPE_IMMEDIATE) {
				return false, false, fmt.Errorf("❌%s 正在瞬变控盘，请在控盘结束后重试。", symbol)
			} else {
				return false, false, fmt.Errorf("❌%s 正在其他类型控盘，请在控盘结束后重试。", symbol)
			}
		}
	} else if req.Type == "" || req.Type == "gradient" {
		// 渐变模式只能在渐变控盘中进行
		if record.ControlMode != string(kcc_types.ENUM_KLINE_CONTROL_TYPE_GRADIENT) {
			if record.ControlMode == string(kcc_types.ENUM_KLINE_CONTROL_TYPE_LINEAR) {
				return false, false, fmt.Errorf("❌%s 正在线形控盘，请在控盘结束后重试。", symbol)
			} else if record.ControlMode == string(kcc_types.ENUM_KLINE_CONTROL_TYPE_IMMEDIATE) {
				return false, false, fmt.Errorf("❌%s 正在瞬变控盘，请在控盘结束后重试。", symbol)
			} else {
				return false, false, fmt.Errorf("❌%s 正在其他类型控盘，请在控盘结束后重试。", symbol)
			}
		}
	} else {
		// 未知类型
		return false, false, fmt.Errorf("❌%s 正在其他类型控盘，请在控盘结束后重试。", symbol)
	}
	// 检查是否在回落期
	if singleSymbolControlInfo, err := k.GetSingleSymbolControlInfo(userID, symbol); err == nil {
		if singleSymbolControlInfo.EndTime != "" && singleSymbolControlInfo.EndTime != "0" {
			// 检查是否还在60秒回落期内
			endTime := cast.ToInt64(singleSymbolControlInfo.EndTime)
			if time.Now().Unix() <= endTime {
				return false, false, fmt.Errorf("❌%s 正在线形控盘，请在控盘结束后重试。", symbol)
			}
		}
	}
	return true, true, nil
}

// setupOffsetLinearCache 设置并写入偏移量缓存，返回缓存结构体（含总偏移量）
func (k *klineOffsetLinear) setupOffsetLinearCache(ctx context.Context, req dto.PostKlineOffsetLinearRequest) (*model.KlineOffsetLinearRedis, error) {
	offset, err := kol_cache.KlineOffsetCacheSet(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("❌设置缓存失败: %v", err)
	}
	return offset, nil
}

// saveControlData 写入偏移控制记录和控盘记录到数据库（事务内操作）
func (k *klineOffsetLinear) saveControlData(ctx context.Context, req dto.PostKlineOffsetLinearRequest, offset *model.KlineOffsetLinearRedis, isInControl bool, tx *gorm.DB) error {
	// 插入偏移控制记录
	dbRecord := &model.KlineOffsetLinearDB{
		UserID:       req.UserID,
		Symbol:       req.Symbol,
		Offset:       req.Offset,
		Direction:    req.Direction,
		TotalOffset:  offset.TotalOffset,
		ControlTime:  time.Now().Unix(),
		ControlPrice: decimal.Zero,
		IsOffset:     isInControl,
	}
	if err := repository.SaveKlineOffsetLinear(ctx, tx, dbRecord); err != nil {
		_ = kol_cache.DeleteKlineOffsetLinearFromCache(ctx, req.UserID, req.Symbol)
		return fmt.Errorf("❌保存控盘记录失败: %v", err)
	}

	// 如果不是已控盘状态，需要插入新的控盘记录
	if !isInControl {
		// 根据控盘类型设置对应的枚举值
		var controlMode string
		if req.Type == "linear" {
			controlMode = string(kcc_types.ENUM_KLINE_CONTROL_TYPE_LINEAR)
		} else {
			controlMode = string(kcc_types.ENUM_KLINE_CONTROL_TYPE_GRADIENT)
		}

		dbControl := &kcc_model.KlineControlRecordDB{
			UserID:      req.UserID,
			Symbol:      req.Symbol,
			ControlMode: controlMode,
			StartTime:   time.Now().Unix(),
			EndTime:     0,
			IsEnd:       false,
		}
		if err := kcc_service.CreateControlSafelyFromStruct(dbControl, tx); err != nil {
			return fmt.Errorf("❌创建控盘记录失败: %v", err)
		}
	}

	return nil
}

// updateControlInfoMemory 更新或初始化内存中的控盘控制结构（用户 -> 产品 -> 控盘信息）
func (k *klineOffsetLinear) updateControlInfoMemory(req dto.PostKlineOffsetLinearRequest, totalOffset decimal.Decimal, isInControl bool) error {
	if !isInControl { // 不在控盘周期中
		// 新的控盘周期开始，先确保房间状态干净
		if productRoom, ok := service.GetStationProductRoom[types.ExtraDataPkg](req.UserID, req.Symbol); ok {
			productRoom.DisableModification()
			fmt.Printf("✅ 新控盘周期，重置房间状态, userID: %s, symbol: %s\n", req.UserID, req.Symbol)
		}

		// 获取用户信息，读取控盘时间配置
		var user structs.User
		mysql.M.Where("id = ?", req.UserID).First(&user)
		if user.ID < 1 {
			return fmt.Errorf("❌获取用户信息失败, userID: %s", req.UserID)
		}

		// 初始化或重置控盘信息
		symbolMap, ok := k.ControlInfos.Get(req.UserID)
		if !ok {
			symbolMap = cmap.New[*model.SingleSymbolControlInfo]()
			k.ControlInfos.Set(req.UserID, symbolMap)
		}

		if info, exists := symbolMap.Get(req.Symbol); exists {
			// 获取当前市场价格作为新控盘周期的基准价格
			currentPrice, err := k.getMarketCurrentPrice(req.Symbol)
			if err != nil {
				fmt.Printf("⚠️ 新控盘周期获取市场价格失败，保持原有LastControlPrice: %v\n", err)
			} else {
				info.LastControlPrice = decimal.NewFromFloat(currentPrice)
				fmt.Printf("✅ 新控盘周期重置LastControlPrice=%v\n", info.LastControlPrice)
			}

			// 更新字段（共通逻辑）
			info.StartTime = cast.ToString(time.Now().Unix())
			info.TotalOffset = totalOffset
			info.AdjustTime = cast.ToString(time.Now().Unix())
			info.AsksPriceMin = req.AsksPriceMin
			info.AsksPriceMax = req.AsksPriceMax
			info.OrderBookDiffMin = req.OrderBookDiffMin
			info.OrderBookDiffMax = req.OrderBookDiffMax
			// 添加控盘时间配置
			info.StartDuration = user.StartDuration
			info.EndDuration = user.EndDuration
			// 🔧 新控盘周期开始时重置偏移量相关字段，避免使用旧周期的MaxOffsetPerSecond导致瞬间控盘
			info.AppliedOffset = decimal.Zero
			info.PreviousReachedTotalOffset = decimal.Zero
			info.IsReachedMaxOffset = false
			// 🔧 清除EndTime，避免新控盘周期被误判为回落期
			info.EndTime = ""
			// 🔧 清除回落期标识，允许新控盘周期正常调整MaxOffsetPerSecond
			info.IsInFallback = false
			// 🔧 重新计算MaxOffsetPerSecond，基于当前的totalOffset和StartDuration
			info.MaxOffsetPerSecond = totalOffset.Div(decimal.NewFromInt32(user.StartDuration))
			fmt.Printf("🔧 新控盘周期重置: AppliedOffset=0, EndTime清空, IsInFallback=false, MaxOffsetPerSecond=%v (totalOffset=%v/StartDuration=%v)\n",
				info.MaxOffsetPerSecond, totalOffset, user.StartDuration)
			// 若允许清空历史，则清空
			if info.IsCanReset {
				info.ControlKlineHistory = cmap.New[cmap.ConcurrentMap[string, *model.SingleSymbolTimeframeControlKline]]()
				info.IsCanReset = false
			}
		} else {
			// 获取当前市场价格作为初始控盘价格
			currentPrice, err := k.getMarketCurrentPrice(req.Symbol)
			if err != nil {
				fmt.Printf("⚠️ 获取市场价格失败，使用默认价格: %v\n", err)
			}

			// 初始化控盘信息
			controlInfo := &model.SingleSymbolControlInfo{
				Symbol:              req.Symbol,
				StartTime:           cast.ToString(time.Now().Unix()),
				TotalOffset:         totalOffset,
				AsksPriceMin:        req.AsksPriceMin,
				AsksPriceMax:        req.AsksPriceMax,
				OrderBookDiffMin:    req.OrderBookDiffMin,
				OrderBookDiffMax:    req.OrderBookDiffMax,
				StartDuration:       user.StartDuration, // 添加开始控盘偏移时间
				EndDuration:         user.EndDuration,   // 添加结束控盘偏移时间
				IsCanReset:          false,
				ControlKlineHistory: cmap.New[cmap.ConcurrentMap[string, *model.SingleSymbolTimeframeControlKline]](),
				Direction:           req.Direction,
				ControlType:         req.Type,
				LastControlPrice:    decimal.NewFromFloat(currentPrice), // 初始化控盘基准价格
			}

			fmt.Printf("✅ 初始化控盘信息: Symbol=%s, LastControlPrice=%v\n", req.Symbol, controlInfo.LastControlPrice)

			// 线性模式的特殊字段
			if req.Type == "linear" {
				// 从user_symbol获取FloatRange和DecimalPlaces
				var userSymbol structs.UserSymbol
				mysql.M.Where("user_id = ? AND symbol = ? AND type = ?", req.UserID, req.Symbol, "kline").First(&userSymbol)
				controlInfo.FloatRange = userSymbol.FloatRange
				controlInfo.DecimalPlaces = int32(userSymbol.DecimalPlaces)
				fmt.Printf("初始化线性模式: FloatRange=%v, DecimalPlaces=%v\n", controlInfo.FloatRange, controlInfo.DecimalPlaces)
			}

			symbolMap.Set(req.Symbol, controlInfo)
		}

		// 初始化偏移量信息
		userOffsetInfo, ok := k.OffsetInfo.Get(req.UserID)
		if !ok {
			userOffsetInfo = cmap.New[decimal.Decimal]()
			userOffsetInfo.Set(req.Symbol, totalOffset)
			k.OffsetInfo.Set(req.UserID, userOffsetInfo)
		} else {
			userOffsetInfo.Set(req.Symbol, totalOffset)
		}
	} else {
		err := kol_cache.GetAllKlineOffsetLinearFromCache(context.Background(), k.OffsetInfo)
		if err != nil {
			return fmt.Errorf("❌ updateControlInfoMemory r3 偏移量(线性模式),Start Control 刷新缓存失败, 没有在缓存中,找到控盘信息, err : %v", err)
		}
		// 控盘中：更新控盘信息结构体
		symbolMap, ok := k.ControlInfos.Get(req.UserID)
		if !ok {
			// 内存中没有控盘信息，重新初始化（容错处理）
			fmt.Printf("⚠️  内存中缺少控盘信息，重新初始化: UserID=%s, Symbol=%s\n", req.UserID, req.Symbol)
			symbolMap = cmap.New[*model.SingleSymbolControlInfo]()
			k.ControlInfos.Set(req.UserID, symbolMap)
		}

		controlInfo, have := symbolMap.Get(req.Symbol)
		if !have {
			// 产品控盘信息不存在，重新创建（容错处理）
			fmt.Printf("⚠️  产品控盘信息不存在，重新创建: UserID=%s, Symbol=%s\n", req.UserID, req.Symbol)

			// 获取用户信息，读取控盘时间配置
			var user structs.User
			mysql.M.Where("id = ?", req.UserID).First(&user)
			if user.ID < 1 {
				return fmt.Errorf("❌容错处理：获取用户信息失败, userID: %s", req.UserID)
			}

			controlInfo = &model.SingleSymbolControlInfo{
				StartTime:                  cast.ToString(time.Now().Unix()),
				AdjustTime:                 cast.ToString(time.Now().Unix()),
				EndTime:                    cast.ToString(utils.GetControlEndTimeUnix(time.Now().Unix(), int64(user.EndDuration))),
				TotalOffset:                totalOffset,
				AsksPriceMin:               req.AsksPriceMin,
				AsksPriceMax:               req.AsksPriceMax,
				OrderBookDiffMin:           req.OrderBookDiffMin,
				OrderBookDiffMax:           req.OrderBookDiffMax,
				PreviousReachedTotalOffset: decimal.Zero,
				AppliedOffset:              decimal.Zero,
				AdjustOffset:               decimal.Zero,
				MaxOffsetPerSecond:         decimal.Zero,
				StartDuration:              user.StartDuration,
				EndDuration:                user.EndDuration,
				IsReachedMaxOffset:         false,
				IsCanReset:                 false,
				ControlKlineHistory:        cmap.New[cmap.ConcurrentMap[string, *model.SingleSymbolTimeframeControlKline]](),
				Direction:                  req.Direction,
			}
			symbolMap.Set(req.Symbol, controlInfo)
		} else {
			// 更新现有控盘信息
			controlInfo.TotalOffset = totalOffset
			controlInfo.AsksPriceMin = req.AsksPriceMin
			controlInfo.AsksPriceMax = req.AsksPriceMax
			controlInfo.OrderBookDiffMin = req.OrderBookDiffMin
			controlInfo.OrderBookDiffMax = req.OrderBookDiffMax
		}
	}
	return nil
}
