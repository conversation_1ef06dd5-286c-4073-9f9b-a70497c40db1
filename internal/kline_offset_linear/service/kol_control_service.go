package service

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand/v2"
	"sort"
	"sync"
	"time"
	"trading_tick_server/common/utils"
	"trading_tick_server/internal/data_transfer_station/service"
	"trading_tick_server/internal/data_transfer_station/types"
	kcc_service "trading_tick_server/internal/kline_control_center/service"
	kcache "trading_tick_server/internal/kline_offset_linear/cache"
	"trading_tick_server/internal/kline_offset_linear/model"
	"trading_tick_server/lib/function"
	"trading_tick_server/lib/mysql"
	"trading_tick_server/lib/redis"
	"trading_tick_server/lib/structs"

	cmap "github.com/orcaman/concurrent-map/v2"
	"github.com/shopspring/decimal"
	"github.com/sknun/cf/cast"
)

// 🔧 全局锁映射，用于解决ControlKlineHistory的并发问题
var controlKlineHistoryMutex = cmap.New[*sync.Mutex]()

// 获取用户+symbol的专用锁
func getControlKlineHistoryMutex(userID, symbol string) *sync.Mutex {
	key := userID + "_" + symbol
	if mutex, ok := controlKlineHistoryMutex.Get(key); ok {
		return mutex
	}
	// 创建新锁
	newMutex := &sync.Mutex{}
	controlKlineHistoryMutex.Set(key, newMutex)
	return newMutex
}

/*
数据控制(进行)
*/
func (k *klineOffsetLinear) DataControl(userID string, symbol string) error {
	/*
		准备数据,这一次开始控盘或控盘调整时所要计算变化的数据在这里
	*/
	nowTimeUnixString := cast.ToString(time.Now().Unix())
	controlInfo, err := k.GetSingleSymbolControlInfo(userID, symbol)
	if err != nil {
		return fmt.Errorf("data Control 获取控盘信息失败, err: %v", err)
	}

	/*
		计算调整偏移量
		调整偏移量 = 已偏移量 - 总偏移量
	*/
	controlInfo.AdjustOffset = (controlInfo.TotalOffset).Sub(controlInfo.AppliedOffset)
	fmt.Printf("📊 DataControl计算: TotalOffset=%v, AppliedOffset=%v, AdjustOffset=%v\n",
		controlInfo.TotalOffset, controlInfo.AppliedOffset, controlInfo.AdjustOffset)
	/*
		调整上一次到达的偏移量,在未达到目标偏移量的一个周期内调整目标偏移量,视作是一次到达偏移量
	*/
	controlInfo.PreviousReachedTotalOffset = controlInfo.AppliedOffset
	/*
		计算最大偏移量
		当前秒偏移量 = 调整偏移量 / controlInfo.StartDuration //60(默认)
		如果当前秒偏移量大于最大偏移量,则覆盖最大偏移量和调整时间
	*/
	currentOffsetPerSecond := controlInfo.AdjustOffset.Div(decimal.NewFromInt32(controlInfo.StartDuration))
	fmt.Printf("📊 当前每秒偏移量: %v, 最大每秒偏移量: %v, StartDuration: %v\n",
		currentOffsetPerSecond, controlInfo.MaxOffsetPerSecond, controlInfo.StartDuration)
	/*
		如果计算出来的当前每秒偏移量和最大每秒偏移量是相反数,那么要反转最大偏移量的符号
	*/
	// 避免回落期被新控盘覆盖
	if controlInfo.IsInFallback {
		// 回落期中，禁止修改MaxOffsetPerSecond，保持回落速度
	} else {
		if currentOffsetPerSecond.Abs().GreaterThan(controlInfo.MaxOffsetPerSecond.Abs()) { // 当前偏移量大于最大偏移量(绝对值)
			fmt.Printf(" 更新MaxOffsetPerSecond: %v -> %v (当前偏移量绝对值更大)\n", controlInfo.MaxOffsetPerSecond, currentOffsetPerSecond)
			controlInfo.MaxOffsetPerSecond = currentOffsetPerSecond
		} else if currentOffsetPerSecond.Mul(controlInfo.MaxOffsetPerSecond).LessThan(decimal.Zero) { // 如果当前偏移量和最大偏移量是相反数,那么要反转最大偏移量的符号
			fmt.Printf("反转MaxOffsetPerSecond符号: %v -> %v (方向相反, 乘积=%v<0)\n",
				controlInfo.MaxOffsetPerSecond, controlInfo.MaxOffsetPerSecond.Neg(),
				currentOffsetPerSecond.Mul(controlInfo.MaxOffsetPerSecond))
			controlInfo.MaxOffsetPerSecond = controlInfo.MaxOffsetPerSecond.Neg()
		}
	}

	// 达到之前总偏移量后新的周期需要重设
	if controlInfo.IsReachedMaxOffset && !controlInfo.IsInFallback {
		// 新周期重设MaxOffsetPerSecond
		controlInfo.IsReachedMaxOffset = false
		controlInfo.MaxOffsetPerSecond = currentOffsetPerSecond
	} else if controlInfo.IsReachedMaxOffset && controlInfo.IsInFallback {
		// 回落期中，禁止新周期重设MaxOffsetPerSecond，保持回落速度
	}

	// 只要是一次控盘操作,就需要调整时间
	controlInfo.AdjustTime = nowTimeUnixString
	/*
		获取用户房间
	*/
	productRoom, ok := service.GetStationProductRoom[types.ExtraDataPkg](userID, symbol)
	if !ok {
		return fmt.Errorf("数据传输中心,用户房间不存在, userID: %s", userID)
	}
	/*
		激活房间的修改功能(数据控制)
	*/
	err = productRoom.EnableModification(k.EnableModifier, k.Selector)
	if err != nil {
		return fmt.Errorf("DataControl 数据控制功能失败, err: %v", err)
	}
	return nil
}

/*
数据控制(停止)
*/
func (k *klineOffsetLinear) OverControl(userID string, symbol string) error {
	/*
		60 秒回落期数据准备
	*/
	nowTimeUnixString := cast.ToString(time.Now().Unix())
	controlInfo, err := k.GetSingleSymbolControlInfo(userID, symbol)
	if err != nil {
		return fmt.Errorf("data Control 获取控盘信息失败, err: %v", err)
	}
	fmt.Printf("🛑 开始结束控盘: userID=%s, symbol=%s, 当前AppliedOffset=%v, EndDuration=%v\n",
		userID, symbol, controlInfo.AppliedOffset, controlInfo.EndDuration)

	/*
		结束控盘相当于,将目标偏移量设置为0
	*/
	controlInfo.TotalOffset = decimal.Zero
	/*
		计算回落期每秒偏移量(最大偏移量) = (已偏移量)当前偏移量 / controlInfo.EndDuration //60
		确保EndDuration不为0，避免除零错误
	*/
	if controlInfo.EndDuration <= 0 {
		controlInfo.EndDuration = 60
	}

	controlInfo.MaxOffsetPerSecond = controlInfo.AppliedOffset.Div(decimal.NewFromInt32(controlInfo.EndDuration))
	// 设置回落期标识，防止DataControl覆盖回落期的MaxOffsetPerSecond
	controlInfo.IsInFallback = true

	// 线性模式：在回落期开始时，将LastControlPrice设置为当前的实际控盘价格
	if controlInfo.ControlType == "linear" {
		// 当前实际控盘价格 = 基准价格 + 当前偏移量
		currentControlPrice := controlInfo.LastControlPrice.Add(controlInfo.AppliedOffset)
		controlInfo.LastControlPrice = currentControlPrice
	}

	/*
		调整控盘时间(结束信号来时,也视作是一次调整)
	*/
	controlInfo.AdjustTime = nowTimeUnixString
	/*
		设置调整偏移量,结束的调整偏移量就相当于是已到达的偏移量(当前偏移量)
	*/
	controlInfo.AdjustOffset = controlInfo.AppliedOffset
	/*
		设置上次到达的偏移量,结束也算作一次调整
	*/
	controlInfo.PreviousReachedTotalOffset = controlInfo.AppliedOffset
	/*
		获取用户房间
	*/
	productRoom, ok := service.GetStationProductRoom[types.ExtraDataPkg](userID, symbol)
	if !ok {
		return fmt.Errorf("数据传输中心,用户房间不存在, userID: %s", userID)
	}

	/*
		⚠️注意: 这里是重设房间的修改功能(数据控制)
	*/
	err = productRoom.EnableModification(k.DisableModifier, k.Selector)
	if err != nil {
		return fmt.Errorf("OverControl 数据控制功能失败, err: %v", err)
	}
	return nil
}

/*
数据选择器
选择需要控制的数据
当前只需要选择该用户需要控制的对应币种
*/
func (k *klineOffsetLinear) Selector(userID string, data types.ExtraDataPkg) bool {
	originalData := *data.OriginalData
	if len(originalData) == 0 {
		return false
	}

	if userControlMap, ok := k.OffsetInfo.Get(userID); ok {
		if _, exists := userControlMap.Get(originalData[0].Code); exists {
			return true
		}
	}

	return false
}

/*
数据修改器(激活时期)
*/
func (k *klineOffsetLinear) EnableModifier(userID string, data *types.ExtraDataPkg) *types.ExtraDataPkg {
	// 数据拷贝，避免修改原数据
	dataCopy := append([]structs.KlineRetData(nil), *data.OriginalData...)
	symbol := dataCopy[0].Code

	// 获取控盘信息
	controlInfo, err := k.getControlInfoWithLock(userID, symbol)
	if err != nil {
		return data // 无控盘信息，直接返回原数据
	}

	// 计算当前偏移量
	currentOffset, err := k.CalculateCurrentOffset(controlInfo)
	if err != nil {
		return data
	}

	// 处理K线数据
	k.processKlineData(dataCopy, controlInfo, currentOffset, userID, symbol)

	// 存储K线历史数据
	k.storeKlineHistory(dataCopy, controlInfo, symbol)

	// 存储K线数据到Redis供历史接口使用
	k.storeEnableModifierKlinesToRedis(dataCopy, userID, symbol, currentOffset, controlInfo)

	// 更新ExtraData的动态字段
	data.ExtraData = &types.ExtraData{
		Symbol:           symbol,
		TotalOffset:      controlInfo.TotalOffset,
		CurrentOffset:    controlInfo.AppliedOffset,
		RealPrice:        decimal.NewFromFloat((*data.OriginalData)[0].Close),
		ControlPrice:     decimal.NewFromFloat(dataCopy[0].Close),
		AsksPriceMin:     controlInfo.AsksPriceMin,
		AsksPriceMax:     controlInfo.AsksPriceMax,
		OrderBookDiffMin: controlInfo.OrderBookDiffMin,
		OrderBookDiffMax: controlInfo.OrderBookDiffMax,
	}

	data.OriginalData = &dataCopy
	return data
}

// 获取控盘信息并加锁
func (k *klineOffsetLinear) getControlInfoWithLock(userID, symbol string) (*model.SingleSymbolControlInfo, error) {
	mutex := getControlKlineHistoryMutex(userID, symbol)
	mutex.Lock()
	defer mutex.Unlock()

	symbolMap, ok := k.OffsetInfo.Get(userID)
	if !ok {
		fmt.Printf("❌ 获取控盘信息失败: userID=%s, 偏移量控制数据不存在\n", userID)
		return nil, fmt.Errorf("用户ID: %s, 偏移量控制数据不存在", userID)
	}

	_, exist := symbolMap.Get(symbol)
	if !exist {
		fmt.Printf("❌ 获取控盘信息失败: userID=%s, symbol=%s, 产品偏移量控制数据不存在\n", userID, symbol)
		return nil, fmt.Errorf("用户ID: %s, 产品: %s, 偏移量控制数据不存在", userID, symbol)
	}

	controlInfo, err := k.GetSingleSymbolControlInfo(userID, symbol)
	if err != nil {
		fmt.Printf("❌ 获取单个产品控盘信息失败: userID=%s, symbol=%s, err=%v\n", userID, symbol, err)
		return nil, err
	}

	fmt.Printf("✅ 获取控盘信息成功: userID=%s, symbol=%s, controlType=%s, totalOffset=%v, isReachedMax=%v\n",
		userID, symbol, controlInfo.ControlType, controlInfo.TotalOffset.InexactFloat64(), controlInfo.IsReachedMaxOffset)

	return controlInfo, nil
}

// 处理K线数据
func (k *klineOffsetLinear) processKlineData(dataCopy []structs.KlineRetData, controlInfo *model.SingleSymbolControlInfo, currentOffset decimal.Decimal, userID, symbol string) {
	controlStartTime := cast.ToInt64(controlInfo.StartTime)

	for i := 0; i < len(dataCopy); i++ {
		// 判断是否是控盘期间的K线
		isControlPeriod := dataCopy[i].Time >= controlStartTime

		if !isControlPeriod {
			continue
		}

		// 通过检查历史控盘数据来判断是否是第一根控盘K线
		isFirstKline := k.isFirstControlKlineByHistory(controlInfo, dataCopy[i].Interval, dataCopy[i].Time, controlStartTime)

		if isFirstKline {
			fmt.Printf("🔍 第一根K线判断: symbol=%s, interval=%s, currentTime=%d, controlStart=%d, isFirstKline=%v\n",
				symbol, dataCopy[i].Interval, dataCopy[i].Time, controlStartTime, isFirstKline)
		}

		// 设置开盘价连续性
		k.setOpenPriceContinuity(&dataCopy[i], controlInfo, isFirstKline, userID, symbol)

		// 计算收盘价、最高价、最低价
		k.calculateKlinePrices(&dataCopy[i], controlInfo, currentOffset, isFirstKline)

		// 历史K线比较，确保价格连续性
		k.compareWithHistoryKline(&dataCopy[i], controlInfo, isFirstKline, symbol)
	}
}

// 🔧 通过检查历史控盘数据来判断是否是第一根控盘K线
func (k *klineOffsetLinear) isFirstControlKlineByHistory(controlInfo *model.SingleSymbolControlInfo, interval string, currentTime, controlStartTime int64) bool {
	// 检查当前时间间隔是否已经有控盘历史数据
	timeKey := cast.ToString(currentTime)
	if existingGroup, ok := controlInfo.ControlKlineHistory.Get(timeKey); ok {
		if _, hasInterval := existingGroup.Get(interval); hasInterval {
			// 如果已经有这个时间间隔的控盘数据，说明不是第一根
			fmt.Printf("🔍 历史数据检查: 已存在控盘数据，不是第一根K线 (interval=%s, time=%d)\n", interval, currentTime)
			return false
		}
	}

	// 检查是否有任何该时间间隔的控盘历史数据
	hasAnyHistoryForInterval := false
	controlInfo.ControlKlineHistory.IterCb(func(timeStr string, group cmap.ConcurrentMap[string, *model.SingleSymbolTimeframeControlKline]) {
		if _, hasInterval := group.Get(interval); hasInterval {
			timeValue := cast.ToInt64(timeStr)
			if timeValue >= controlStartTime && timeValue < currentTime {
				hasAnyHistoryForInterval = true
			}
		}
	})

	if hasAnyHistoryForInterval {
		fmt.Printf("🔍 历史数据检查: 存在该时间间隔的历史控盘数据，不是第一根K线 (interval=%s, time=%d)\n", interval, currentTime)
		return false
	}

	fmt.Printf("🔍 历史数据检查: 没有该时间间隔的历史控盘数据，是第一根K线 (interval=%s, time=%d)\n", interval, currentTime)
	return true
}

// 设置开盘价连续性
func (k *klineOffsetLinear) setOpenPriceContinuity(kline *structs.KlineRetData, controlInfo *model.SingleSymbolControlInfo, isFirstKline bool, userID, symbol string) {
	var prevClosePrice decimal.Decimal
	if isFirstKline {
		prevClosePrice = decimal.NewFromFloat(kline.Open)
		fmt.Printf("首根K线开盘价: symbol=%s, interval=%s, time=%d, 使用LastControlPrice=%v\n",
			symbol, kline.Interval, kline.Time, prevClosePrice.InexactFloat64())
		return
	}
	if controlInfo.ControlType == "linear" {
		// 优先查控盘历史
		var err error
		prevClosePrice, err = k.GetPreviousIntervalControlKlineClosePrice(userID, symbol, cast.ToString(kline.Time), kline.Interval)
		if err != nil {
			// 🔧 统一的开盘价兜底逻辑：优先使用历史收盘价，避免价格断层
			lastStoredPrice, err := k.GetLastStoredControlKlineClosePrice(userID, symbol, kline.Time, kline.Interval)
			if err == nil {
				prevClosePrice = lastStoredPrice
				fmt.Printf("🔧 线性模式开盘价兜底: 使用历史收盘价=%v，避免断层\n", lastStoredPrice.InexactFloat64())
			} else if controlInfo.IsReachedMaxOffset {
				// 峰值波动期：如果连历史数据都没有，才使用目标价格
				basePrice := controlInfo.LastControlPrice
				targetPrice := basePrice.Add(controlInfo.TotalOffset)
				prevClosePrice = targetPrice
				fmt.Printf("🔧 峰值波动期开盘价兜底: 无历史数据，使用目标价格=%v（可能断层）\n", targetPrice.InexactFloat64())
			} else {
				// 上升期：如果连历史数据都没有，使用基准价格
				prevClosePrice = controlInfo.LastControlPrice
				fmt.Printf("🔧 上升期开盘价兜底: 无历史数据，使用基准价格=%v\n", controlInfo.LastControlPrice.InexactFloat64())
			}
		} else {
			fmt.Printf("✅ 线性模式开盘价连续性: symbol=%s, interval=%s, time=%d, 获取上一根收盘价=%v\n",
				symbol, kline.Interval, kline.Time, prevClosePrice.InexactFloat64())
		}
	} else {
		// 渐变控盘逻辑
		if price, err := k.GetPreviousMinuteControlKlineClosePrice(userID, symbol, cast.ToString(kline.Time)); err == nil {
			prevClosePrice = price
			fmt.Printf("✅ 渐变模式开盘价连续性: symbol=%s, interval=%s, time=%d, 获取上一分钟收盘价=%v\n",
				symbol, kline.Interval, kline.Time, prevClosePrice.InexactFloat64())
		} else {
			prevClosePrice = decimal.NewFromFloat(kline.Open)
			fmt.Printf("🔧 渐变模式开盘价兜底: symbol=%s, interval=%s, time=%d, 获取失败(err=%v)，使用原始开盘价=%v\n",
				symbol, kline.Interval, kline.Time, err, prevClosePrice.InexactFloat64())
		}
	}

	kline.Open = prevClosePrice.InexactFloat64()
	fmt.Printf("📍 开盘价设置完成: symbol=%s, interval=%s, time=%d, openPrice=%v\n",
		symbol, kline.Interval, kline.Time, kline.Open)
}

// 计算K线价格（收盘价、最高价、最低价）
func (k *klineOffsetLinear) calculateKlinePrices(kline *structs.KlineRetData, controlInfo *model.SingleSymbolControlInfo, currentOffset decimal.Decimal, isFirstKline bool) {
	if controlInfo.ControlType == "linear" {
		// 线性模式：基于基准价格计算目标价格
		basePrice := controlInfo.LastControlPrice
		targetPrice := basePrice.Add(currentOffset)

		// 峰值波动期处理
		if controlInfo.IsReachedMaxOffset && controlInfo.FloatRange > 0 && !controlInfo.IsInFallback {
			k.calculateFloatPrices(kline, targetPrice, controlInfo.FloatRange, controlInfo, isFirstKline)
		} else {
			// 正常控盘期间
			kline.Close = targetPrice.InexactFloat64()
			kline.High = targetPrice.InexactFloat64()
			kline.Low = targetPrice.InexactFloat64()
		}

		// 🔧 基本影线修复：非第一根K线需要处理影线
		if !isFirstKline {
			k.fixKlineShadows(kline, controlInfo)
		}
	} else {
		// 渐变控盘模式：使用大盘价格 + 偏移量
		kline.Close = currentOffset.Add(decimal.NewFromFloat(kline.Close)).InexactFloat64()
		kline.High = currentOffset.Add(decimal.NewFromFloat(kline.High)).InexactFloat64()
		kline.Low = currentOffset.Add(decimal.NewFromFloat(kline.Low)).InexactFloat64()
	}
}

// 计算峰值波动期价格
func (k *klineOffsetLinear) calculateFloatPrices(kline *structs.KlineRetData, targetPrice decimal.Decimal, floatRange float64, controlInfo *model.SingleSymbolControlInfo, isFirstKline bool) {
	// 🔍 判断是否是峰值波动期的第一根K线
	// 峰值波动期的第一根K线允许价格自然跳跃到目标价格附近，不进行保护
	var isFirstKlineInPeakPeriod bool
	if controlInfo.IsReachedMaxOffset && controlInfo.FloatRange > 0 {
		// 检查当前K线是否是AdjustTime之后的第一个该间隔K线
		adjustTime := cast.ToInt64(controlInfo.AdjustTime)
		currentTime := kline.Time
		intervalSeconds := k.getIntervalSeconds(kline.Interval)

		if adjustTime > 0 {
			// 检查adjustTime是否在当前K线的时间周期内
			if adjustTime >= currentTime && adjustTime < currentTime+intervalSeconds {
				isFirstKlineInPeakPeriod = true
				fmt.Printf("🎯 检测到峰值波动期第一根K线: interval=%s, adjustTime=%d, 当前K线周期=[%d, %d)\n",
					kline.Interval, adjustTime, currentTime, currentTime+intervalSeconds)
			}
		}
	}

	// 在目标价格附近进行随机浮动
	randomFloat := (rand.Float64() - 0.5) * 2 * floatRange
	floatOffset := decimal.NewFromFloat(randomFloat)
	actualPrice := targetPrice.Add(floatOffset)

	// 价格范围保护
	maxPrice := targetPrice.Add(decimal.NewFromFloat(floatRange))
	minPrice := targetPrice.Sub(decimal.NewFromFloat(floatRange))

	if isFirstKlineInPeakPeriod {
		// 峰值波动期的第一根K线：允许价格自然跳跃，不进行保护
		fmt.Printf("🎯 峰值波动期第一根K线: 允许价格自然跳跃，actualPrice=%v, targetPrice=%v\n",
			actualPrice.InexactFloat64(), targetPrice.InexactFloat64())
	} else {
		// 非第一根K线：进行价格保护
		if actualPrice.GreaterThan(maxPrice) {
			actualPrice = maxPrice
			fmt.Printf("🛡️ 峰值价格保护: 价格超出上限，调整为maxPrice=%v\n", maxPrice.InexactFloat64())
		} else if actualPrice.LessThan(minPrice) {
			actualPrice = minPrice
			fmt.Printf("🛡️ 峰值价格保护: 价格低于下限，调整为minPrice=%v\n", minPrice.InexactFloat64())
		}
	}

	kline.Close = actualPrice.InexactFloat64()
	kline.High = actualPrice.InexactFloat64()
	kline.Low = actualPrice.InexactFloat64()
}

// 获取时间间隔的秒数
func (k *klineOffsetLinear) getIntervalSeconds(interval string) int64 {
	switch interval {
	case "1m":
		return 60
	case "3m":
		return 180
	case "5m":
		return 300
	case "15m":
		return 900
	case "30m":
		return 1800
	case "1h":
		return 3600
	case "2h":
		return 7200
	case "4h":
		return 14400
	case "6h":
		return 21600
	case "8h":
		return 28800
	case "12h":
		return 43200
	case "1d":
		return 86400
	case "3d":
		return 259200
	case "1w":
		return 604800
	case "1M":
		return 2592000
	default:
		return 60 // 默认1分钟
	}
}

// 存储K线历史数据
func (k *klineOffsetLinear) storeKlineHistory(dataCopy []structs.KlineRetData, controlInfo *model.SingleSymbolControlInfo, _ string) {
	if len(dataCopy) == 0 {
		return
	}

	// 按时间分组存储，避免不同时间的K线数据混乱
	for i := 0; i < len(dataCopy); i++ {
		timeKey := cast.ToString(dataCopy[i].Time)

		// 获取或创建该时间的klineTimeframe
		var klineTimeframe cmap.ConcurrentMap[string, *model.SingleSymbolTimeframeControlKline]
		if existing, ok := controlInfo.ControlKlineHistory.Get(timeKey); ok {
			klineTimeframe = existing
		} else {
			klineTimeframe = cmap.New[*model.SingleSymbolTimeframeControlKline]()
		}

		// 存储该K线数据
		klineTimeframe.Set(dataCopy[i].Interval, &model.SingleSymbolTimeframeControlKline{
			Open:     decimal.NewFromFloat(dataCopy[i].Open),
			Close:    decimal.NewFromFloat(dataCopy[i].Close),
			High:     decimal.NewFromFloat(dataCopy[i].High),
			Low:      decimal.NewFromFloat(dataCopy[i].Low),
			Volume:   decimal.NewFromFloat(dataCopy[i].Volume),
			Turnover: decimal.NewFromFloat(dataCopy[i].Turnover),
		})

		// 存储到控盘历史
		controlInfo.ControlKlineHistory.Set(timeKey, klineTimeframe)

		fmt.Printf("✅ 存储控盘K线历史: time=%d, interval=%s, close=%v\n",
			dataCopy[i].Time, dataCopy[i].Interval, dataCopy[i].Close)
	}
}

/*
计算当前偏移量(这个时刻的偏移量)
*/
func (k *klineOffsetLinear) CalculateCurrentOffset(singleSymbolControlInfo *model.SingleSymbolControlInfo) (decimal.Decimal, error) {
	if !singleSymbolControlInfo.IsReachedMaxOffset { // 如果没有到达最大偏移量,才计算
		nowTime := time.Now().UnixMilli()
		PassTime := decimal.NewFromInt(nowTime - cast.ToInt64(singleSymbolControlInfo.AdjustTime)*1000) // 已经过的时间(毫秒)
		/*
			如果总偏移量小于上次到达的总偏移量,说明是在一个周期中被调整为了相反的方向,
			这时 maxOffsetPerSecond 需要取反
		*/
		currentOffset := singleSymbolControlInfo.MaxOffsetPerSecond.Mul(PassTime.Div(decimal.NewFromInt(1000))) // 当前偏移量
		currentOffset = currentOffset.Add(singleSymbolControlInfo.PreviousReachedTotalOffset)

		if singleSymbolControlInfo.TotalOffset.GreaterThanOrEqual(singleSymbolControlInfo.PreviousReachedTotalOffset) { // 正向偏移
			// 到达最大偏移量
			if currentOffset.GreaterThanOrEqual(singleSymbolControlInfo.TotalOffset) {

				currentOffset = singleSymbolControlInfo.TotalOffset                // 如果当前偏移量大于等于总偏移量,说明已经完成了控盘
				singleSymbolControlInfo.AdjustTime = cast.ToString(nowTime / 1000) // 更新调整时间
				singleSymbolControlInfo.PreviousReachedTotalOffset = singleSymbolControlInfo.TotalOffset
				singleSymbolControlInfo.IsReachedMaxOffset = true
			}
			singleSymbolControlInfo.AppliedOffset = currentOffset
			return currentOffset, nil
		} else { // 反向偏移
			if currentOffset.LessThan(singleSymbolControlInfo.TotalOffset) {
				currentOffset = singleSymbolControlInfo.TotalOffset                // 如果当前偏移量大于等于总偏移量,说明已经完成了控盘
				singleSymbolControlInfo.AdjustTime = cast.ToString(nowTime / 1000) // 更新调整时间
				singleSymbolControlInfo.PreviousReachedTotalOffset = singleSymbolControlInfo.TotalOffset
				singleSymbolControlInfo.MaxOffsetPerSecond = decimal.Zero
				singleSymbolControlInfo.IsReachedMaxOffset = true
			}
			singleSymbolControlInfo.AppliedOffset = currentOffset
			return currentOffset, nil
		}

	}
	return singleSymbolControlInfo.TotalOffset, nil
}

/*
线性模式的渐进追平计算
*/
func (k *klineOffsetLinear) calculateContinuousGradient(marketPrice decimal.Decimal, lastControlPrice decimal.Decimal, originalDecimalPlaces int32, endDuration int64, fallbackStartTime int64) (close, high, low float64, newLastControlPrice decimal.Decimal) {
	currentTime := time.Now().UnixMilli()
	fallStartTime := fallbackStartTime         // 直接使用秒时间戳
	fallEndTime := fallStartTime + endDuration // 配置的追平时间（秒）

	currentPrice, _ := function.CalFallValue(fallStartTime, fallEndTime, currentTime, marketPrice, lastControlPrice)
	currentPriceDecimal := decimal.NewFromFloat(currentPrice)

	// 计算K线价格
	if originalDecimalPlaces >= 0 {
		// 正常情况：使用Truncate
		close = currentPriceDecimal.Truncate(originalDecimalPlaces).InexactFloat64()
		high = currentPriceDecimal.Truncate(originalDecimalPlaces).InexactFloat64()
		low = currentPriceDecimal.Truncate(originalDecimalPlaces).InexactFloat64()
	} else {
		// DisableModifier情况：不使用Truncate
		close = currentPriceDecimal.InexactFloat64()
		high = currentPriceDecimal.InexactFloat64()
		low = currentPriceDecimal.InexactFloat64()
	}

	// 返回更新后的LastControlPrice
	newLastControlPrice = currentPriceDecimal

	return
}

/*
数据修改器(关闭时期)
*/
func (k *klineOffsetLinear) DisableModifier(userID string, data *types.ExtraDataPkg) *types.ExtraDataPkg {
	originalData := data.OriginalData
	dataCopy := append([]structs.KlineRetData(nil), *data.OriginalData...)
	symbol := dataCopy[0].Code

	// 🔧 获取用户+symbol的专用锁，防止ControlKlineHistory并发问题
	mutex := getControlKlineHistoryMutex(userID, symbol)
	mutex.Lock()
	defer mutex.Unlock()
	singleSymbolControlInfo, err := k.GetSingleSymbolControlInfo(userID, symbol)
	if err != nil {
		fmt.Printf("线性模式, DisableModifier, 用户ID: %s, 产品: %s, 偏移量控制数据不存在\n", userID, symbol)
		return data
	}

	/*
		获取产品房间 - 使用统一的方式
	*/
	symbolRoom, ok := service.GetStationProductRoom[types.ExtraDataPkg](userID, symbol)
	if !ok {
		fmt.Printf("❌ 线性模式, DisableModifier, 获取产品房间失败, userID: %s, symbol: %s\n", userID, symbol)
		return data
	}

	// 获取控盘结束分钟对齐时间戳(秒级)
	endMinuteAlignedTime := utils.GetMinuteStartTimestamp(cast.ToInt64(singleSymbolControlInfo.EndTime))
	if dataCopy[0].Time >= endMinuteAlignedTime {
		// 说明当前数据已经超过了控盘结束时间,所以不需要进行控盘,真正的结束
		symbolRoom.DisableModification()

		// 清理所有内存中的控盘信息（包括OffsetInfo和ControlInfos）
		k.cleanupMemoryInfo(userID, symbol)

		/*
			清空缓存
		*/
		err = kcache.DeleteKlineOffsetLinearFromCache(context.Background(), userID, symbol)
		if err != nil {
			fmt.Printf("❌删除偏移量控制(线性模式)缓存记录失败, err : %v", err)
		}
		err = kcc_service.SymbolControlEnd(userID, symbol, mysql.M)
		if err != nil {
			fmt.Printf("❌ 偏移量控制(线性模式)K线控制中心,控制结束修改错误, 用户ID: %v, 产品: %v", userID, symbol)
		}
		// 临时给予外界信息
		data.ExtraData = &types.ExtraData{
			Symbol:           symbol,
			TotalOffset:      decimal.Zero,
			CurrentOffset:    decimal.Zero,
			RealPrice:        decimal.NewFromFloat((*originalData)[0].Close),
			ControlPrice:     decimal.NewFromFloat(dataCopy[0].Close),
			OrderBookDiffMin: decimal.Zero,
			OrderBookDiffMax: decimal.Zero,
		}
		return data
	} else {
		/*
			当前结束信号在已控盘的数据中,所以即使结束信号来了并不是真正的结束控盘,
			而是开始60秒的回落期,当回落期结束,却还没有开始新的一根K线时,
			这时依然需要改变数据,但是只改变开盘 open 数据,收盘 close 数据就开始使用真实值,
		*/
		/*
			计算当前偏移量
		*/
		// 回落期处理逻辑
		k.processDisableModifierData(dataCopy, singleSymbolControlInfo, userID, symbol, originalData)

		// 更新ExtraData的动态字段
		data.ExtraData = &types.ExtraData{
			Symbol:           symbol,
			TotalOffset:      singleSymbolControlInfo.TotalOffset,
			CurrentOffset:    singleSymbolControlInfo.AppliedOffset,
			RealPrice:        decimal.NewFromFloat((*originalData)[0].Close),
			ControlPrice:     decimal.NewFromFloat(dataCopy[0].Close),
			AsksPriceMin:     singleSymbolControlInfo.AsksPriceMin,
			AsksPriceMax:     singleSymbolControlInfo.AsksPriceMax,
			OrderBookDiffMin: singleSymbolControlInfo.OrderBookDiffMin,
			OrderBookDiffMax: singleSymbolControlInfo.OrderBookDiffMax,
		}

		data.OriginalData = &dataCopy
		return data
	}
}

// 处理DisableModifier的数据
func (k *klineOffsetLinear) processDisableModifierData(dataCopy []structs.KlineRetData, controlInfo *model.SingleSymbolControlInfo, userID, symbol string, originalData *[]structs.KlineRetData) {
	fmt.Printf("🔧 DisableModifier开始处理: symbol=%s, controlType=%s, isInFallback=%v\n",
		symbol, controlInfo.ControlType, controlInfo.IsInFallback)

	// 计算当前偏移量
	nowTime := time.Now().UnixMilli()
	PassTime := decimal.NewFromInt(nowTime - cast.ToInt64(controlInfo.AdjustTime)*1000) // 已经过的时间(毫秒)
	intervalOffset := controlInfo.MaxOffsetPerSecond.Mul(PassTime.Div(decimal.NewFromInt(1000)))
	currentOffset := controlInfo.PreviousReachedTotalOffset.Sub(intervalOffset) // 当前偏移量

	fmt.Printf("🔍 DisableModifier偏移量计算: symbol=%s, passTime=%v秒, intervalOffset=%v, currentOffset=%v\n",
		symbol, PassTime.Div(decimal.NewFromInt(1000)).InexactFloat64(),
		intervalOffset.InexactFloat64(), currentOffset.InexactFloat64())

	if intervalOffset.GreaterThanOrEqual(decimal.Zero) && currentOffset.LessThanOrEqual(decimal.Zero) {
		currentOffset = decimal.Zero
		controlInfo.IsInFallback = false
		fmt.Printf("✅ DisableModifier回落完成: symbol=%s, 偏移量归零，退出回落期\n", symbol)
	} else if intervalOffset.LessThan(decimal.Zero) && currentOffset.GreaterThanOrEqual(decimal.Zero) {
		currentOffset = decimal.Zero
		controlInfo.IsInFallback = false
		fmt.Printf("✅ DisableModifier回落完成: symbol=%s, 反向偏移归零，退出回落期\n", symbol)
	}
	controlInfo.AppliedOffset = currentOffset

	// 获取控制信息,默认这里肯定有控制信息
	controlInfoStartMinuteAlignedTimestamp := utils.GetMinuteStartTimestamp(cast.ToInt64(controlInfo.StartTime))

	// K线数据 - 时间颗粒组
	klineTimeframe := cmap.New[*model.SingleSymbolTimeframeControlKline]()

	for i := 0; i < len(dataCopy); i++ {
		// 处理开盘价连续性
		fmt.Printf("🔍 DisableModifier-处理开盘价: symbol=%s, interval=%s, klineTime=%d, controlStart=%d, timeDiff=%d\n",
			symbol, dataCopy[i].Interval, dataCopy[i].Time, controlInfoStartMinuteAlignedTimestamp, dataCopy[i].Time-controlInfoStartMinuteAlignedTimestamp)

		var prevClosePrice decimal.Decimal
		var err error

		// 使用GetPreviousIntervalControlKlineClosePrice获取上一根K线收盘价
		prevClosePrice, err = k.GetPreviousIntervalControlKlineClosePrice(userID, symbol, cast.ToString(dataCopy[i].Time), dataCopy[i].Interval)
		if err != nil {
			lastStoredPrice, err := k.GetLastStoredControlKlineClosePrice(userID, symbol, dataCopy[i].Time, dataCopy[i].Interval)
			if err == nil {
				prevClosePrice = lastStoredPrice
				fmt.Printf("🔧 回落期开盘价兜底: 使用历史收盘价=%v，避免断层 (interval=%s)\n", lastStoredPrice.InexactFloat64(), dataCopy[i].Interval)
			} else {
				prevClosePrice = controlInfo.LastControlPrice.Add(controlInfo.TotalOffset)
				fmt.Printf("� 回落期开盘价最终兜底: 无法获取上一根K线收盘价，使用LastControlPrice=%v (interval=%s, err=%v)\n",
					controlInfo.LastControlPrice.InexactFloat64(), dataCopy[i].Interval, err)
			}
		} else {
			fmt.Printf("✅ 回落期开盘价: 成功获取上一根K线收盘价=%v (interval=%s)\n",
				prevClosePrice.InexactFloat64(), dataCopy[i].Interval)
		}
		// 设置开盘价保持连续性
		dataCopy[i].Open = prevClosePrice.InexactFloat64()
		fmt.Printf("� 开盘价设置-DisableModifier: symbol=%s, interval=%s, time=%d, openPrice=%v\n",
			symbol, dataCopy[i].Interval, dataCopy[i].Time, prevClosePrice.InexactFloat64())

		// 处理收盘价、最高价、最低价
		if controlInfo.ControlType == "linear" {
			basePrice := controlInfo.LastControlPrice
			if controlInfo.TotalOffset.IsZero() && controlInfo.EndTime != "" {
				// 检查是否还在60秒回落期内
				fallbackStartTime := cast.ToInt64(controlInfo.AdjustTime)             // 回落期开始时间
				fallbackEndTime := fallbackStartTime + int64(controlInfo.EndDuration) // 回落期结束时间
				currentTime := time.Now().Unix()

				if currentTime <= fallbackEndTime {
					// 还在回落期内：从当前控盘价格逐渐追平到市场价格
					currentControlPrice := controlInfo.LastControlPrice
					// 使用原始市场价格作为追平目标
					marketPrice := decimal.NewFromFloat((*originalData)[i].Close) // 目标市场价格

					close, high, low, _ := k.calculateContinuousGradient(marketPrice, currentControlPrice, -1, int64(controlInfo.EndDuration), fallbackStartTime)

					dataCopy[i].Close = close
					dataCopy[i].High = high
					dataCopy[i].Low = low

					// 回落期追平后，更新内存中存储的K线数据
					timeKey3 := cast.ToString(dataCopy[i].Time)
					if existingGroup, ok := controlInfo.ControlKlineHistory.Get(timeKey3); ok {
						if existingKline, hasInterval := existingGroup.Get(dataCopy[i].Interval); hasInterval {
							// 更新存储的收盘价为回落期处理后的价格
							existingKline.Close = decimal.NewFromFloat(dataCopy[i].Close)
							existingKline.High = decimal.NewFromFloat(dataCopy[i].High)
							existingKline.Low = decimal.NewFromFloat(dataCopy[i].Low)

							fmt.Printf("🔧 DisableModifier-回落期追平更新存储: symbol=%s, interval=%s, time=%d, 新收盘价=%v\n",
								symbol, dataCopy[i].Interval, dataCopy[i].Time, dataCopy[i].Close)
						}
					}

					fmt.Printf("🔄 回落期追平: symbol=%s, interval=%s, time=%d, 市场价=%v, 控盘价=%v, 追平后=%v\n",
						symbol, dataCopy[i].Interval, dataCopy[i].Time, marketPrice.InexactFloat64(),
						currentControlPrice.InexactFloat64(), close)
				}
			} else {
				// 控盘期间：保证价格连续性
				targetPrice := basePrice.Add(currentOffset)
				dataCopy[i].Close = targetPrice.InexactFloat64()
				dataCopy[i].High = targetPrice.InexactFloat64()
				dataCopy[i].Low = targetPrice.InexactFloat64()
			}
		} else {
			// 渐变模式：原有逻辑，使用大盘价格 + 偏移量
			dataCopy[i].Close = currentOffset.Add(decimal.NewFromFloat(dataCopy[i].Close)).InexactFloat64()
			dataCopy[i].High = currentOffset.Add(decimal.NewFromFloat(dataCopy[i].High)).InexactFloat64()
			dataCopy[i].Low = currentOffset.Add(decimal.NewFromFloat(dataCopy[i].Low)).InexactFloat64()
		}

		// 数据有效性检查
		if dataCopy[i].High < 0 {
			dataCopy[i].High = 0
		}
		if dataCopy[i].Low < 0 {
			dataCopy[i].Low = 0
		}
		if dataCopy[i].Close < 0 {
			dataCopy[i].Close = 0
		}
		if dataCopy[i].Open < 0 {
			dataCopy[i].Open = 0
		}

		// 存储K线数据到时间颗粒组
		klineTimeframe.Set(dataCopy[i].Interval, &model.SingleSymbolTimeframeControlKline{
			Open:     decimal.NewFromFloat(dataCopy[i].Open),
			Close:    decimal.NewFromFloat(dataCopy[i].Close),
			High:     decimal.NewFromFloat(dataCopy[i].High),
			Low:      decimal.NewFromFloat(dataCopy[i].Low),
			Volume:   decimal.NewFromFloat(dataCopy[i].Volume),
			Turnover: decimal.NewFromFloat(dataCopy[i].Turnover),
		})

		// Redis存储条件检查
		if currentOffset.IsZero() {
			// 如果偏移量为0,说明是回落期,不需要存储
			continue
		}

		// 存储到Redis供历史接口使用
		k.storeKlineToRedis(userID, symbol, &dataCopy[i], currentOffset, controlInfo)
	}

	// 存储到控盘历史
	if len(dataCopy) > 0 {
		controlInfo.ControlKlineHistory.Set(cast.ToString(dataCopy[0].Time), klineTimeframe)
	}
}

/*
获取一个用户对应产品的控盘信息
*/
func (k *klineOffsetLinear) GetSingleSymbolControlInfo(userID string, symbol string) (*model.SingleSymbolControlInfo, error) {
	symbolMap, ok := k.ControlInfos.Get(userID)
	if ok {
		controlInfo, have := symbolMap.Get(symbol)
		if have {
			return controlInfo, nil
		}
		return nil, fmt.Errorf("用户ID: %s, 产品: %s, 控盘信息不存在", userID, symbol)
	}
	return nil, fmt.Errorf("用户ID: %s, 偏移量控制数据不存在", userID)
}

/*
获取上一分钟控盘K线收盘价格
*/
func (k *klineOffsetLinear) GetPreviousMinuteControlKlineClosePrice(userID string, symbol string, currentTime string) (decimal.Decimal, error) {
	// 获取用户ID对应的偏移量控制数据
	symbolMap, ok := k.ControlInfos.Get(userID)
	if ok {
		// 确认当前数据的产品是否在偏移量控制列表中
		allTimeControlInfo, exist := symbolMap.Get(symbol)
		if exist {
			// 获取上一分钟开始时间戳
			prevMinuteStartTimestamp := cast.ToString(utils.GetPreviousMinuteStartTimestamp(cast.ToInt64(currentTime)))
			singleSymbolTimeIntervalControlInfo, err := allTimeControlInfo.GetTimeframeKline(prevMinuteStartTimestamp, "1m")
			if err != nil {
				return decimal.Zero, fmt.Errorf("获取上一分钟控盘K线收盘价格失败, err : %v", err)
			}
			return singleSymbolTimeIntervalControlInfo.Close, nil
		}
		return decimal.Zero, fmt.Errorf("用户ID: %s, 产品: %s, 控盘信息不存在", userID, symbol)
	}
	return decimal.Zero, fmt.Errorf("用户ID: %s, 偏移量控制数据不存在", userID)
}

/*
获取上一个指定间隔的控盘K线收盘价格
*/
func (k *klineOffsetLinear) GetPreviousIntervalControlKlineClosePrice(userID string, symbol string, currentTime string, interval string) (decimal.Decimal, error) {
	// 获取用户ID对应的偏移量控制数据
	symbolMap, ok := k.ControlInfos.Get(userID)
	if ok {
		// 确认当前数据的产品是否在偏移量控制列表中
		allTimeControlInfo, exist := symbolMap.Get(symbol)
		if exist {
			// 🔧 修复：直接基于当前K线时间计算上一根K线时间
			currentTimestamp := cast.ToInt64(currentTime)
			intervalSeconds := function.NextKlineTimestamp(currentTimestamp, "", interval) - currentTimestamp
			prevTimestamp := currentTimestamp - intervalSeconds

			// 获取上一根K线的数据
			prevTimeKey := cast.ToString(prevTimestamp)
			singleSymbolTimeIntervalControlInfo, err := allTimeControlInfo.GetTimeframeKline(prevTimeKey, interval)
			if err != nil {
				return decimal.Zero, fmt.Errorf("获取上一个%s间隔控盘K线收盘价格失败, err : %v", interval, err)
			}
			return singleSymbolTimeIntervalControlInfo.Close, nil
		}
		return decimal.Zero, fmt.Errorf("用户ID: %s, 产品: %s, 控盘信息不存在", userID, symbol)
	}
	return decimal.Zero, fmt.Errorf("用户ID: %s, 偏移量控制数据不存在", userID)
}

/*
获取最后存储的控盘K线收盘价格
*/
func (k *klineOffsetLinear) GetLastStoredControlKlineClosePrice(userID string, symbol string, currentTime int64, interval string) (decimal.Decimal, error) {
	// 获取用户ID对应的偏移量控制数据
	symbolMap, ok := k.ControlInfos.Get(userID)
	if ok {
		// 确认当前数据的产品是否在偏移量控制列表中
		allTimeControlInfo, exist := symbolMap.Get(symbol)
		if exist {
			// 获取所有存储的时间戳
			var timestamps []int64
			allTimeControlInfo.ControlKlineHistory.IterCb(func(key string, v cmap.ConcurrentMap[string, *model.SingleSymbolTimeframeControlKline]) {
				ts := cast.ToInt64(key)
				if ts < currentTime { // 只获取早于当前时间的数据
					timestamps = append(timestamps, ts)
				}
			})

			// 如果没有历史数据
			if len(timestamps) == 0 {
				return decimal.Decimal{}, fmt.Errorf("没有历史控盘K线数据")
			}

			// 按时间戳降序排序，获取最新的
			sort.Slice(timestamps, func(i, j int) bool {
				return timestamps[i] > timestamps[j]
			})

			// 从最新的时间戳开始查找
			for _, ts := range timestamps {
				timeKey := cast.ToString(ts)
				if timeframeKline, err := allTimeControlInfo.GetTimeframeKline(timeKey, interval); err == nil {
					return timeframeKline.Close, nil
				}
			}

			return decimal.Decimal{}, fmt.Errorf("未找到对应时间间隔的历史K线数据")
		}
		return decimal.Decimal{}, fmt.Errorf("用户ID: %s, 产品: %s, 控盘信息不存在", userID, symbol)
	}
	return decimal.Decimal{}, fmt.Errorf("用户ID: %s, 偏移量控制数据不存在", userID)
}

// 设置DisableModifier的开盘价连续性
func (k *klineOffsetLinear) setDisableModifierOpenPrice(kline *structs.KlineRetData, controlInfo *model.SingleSymbolControlInfo, userID, symbol string) {
	var prevClosePrice decimal.Decimal

	// 🔧 统一的开盘价兜底逻辑：优先使用历史收盘价，避免价格断层
	lastStoredPrice, err := k.GetLastStoredControlKlineClosePrice(userID, symbol, kline.Time, kline.Interval)
	if err == nil {
		prevClosePrice = lastStoredPrice
		fmt.Printf("🔧 DisableModifier开盘价兜底: 使用历史收盘价=%v，避免断层\n", lastStoredPrice.InexactFloat64())
	} else if controlInfo.IsReachedMaxOffset &&
		controlInfo.FloatRange > 0 &&
		!controlInfo.IsInFallback {
		// 峰值波动期：如果连历史数据都没有，才使用目标价格
		basePrice := controlInfo.LastControlPrice
		targetPrice := basePrice.Add(controlInfo.TotalOffset)
		prevClosePrice = targetPrice
		fmt.Printf("🔧 DisableModifier峰值波动期开盘价兜底: 无历史数据，使用目标价格=%v（可能断层）\n", targetPrice.InexactFloat64())
	} else {
		// 回落期：如果连历史数据都没有，使用LastControlPrice
		prevClosePrice = controlInfo.LastControlPrice
		fmt.Printf("🔧 DisableModifier回落期开盘价兜底: 无历史数据，使用LastControlPrice=%v\n", controlInfo.LastControlPrice.InexactFloat64())
	}

	kline.Open = prevClosePrice.InexactFloat64()
	fmt.Printf("📍 DisableModifier开盘价设置: symbol=%s, interval=%s, time=%d, openPrice=%v\n",
		symbol, kline.Interval, kline.Time, kline.Open)
}

// 存储K线数据到Redis
func (k *klineOffsetLinear) storeKlineToRedis(userID, symbol string, kline *structs.KlineRetData, currentOffset decimal.Decimal, controlInfo *model.SingleSymbolControlInfo) {
	direction := "up" // 偏移方向,默认向上偏移
	if currentOffset.IsNegative() {
		direction = "down" // 如果偏移量是负数,说明是向下偏移
	}

	// 计算UseOpen和UseClose的逻辑
	var UseOpen, UseClose bool
	nextTime := function.NextKlineTimestamp(kline.Time, "", kline.Interval)
	currentKlineEnd := nextTime

	// 回落期的时间范围
	fallbackStartTime := cast.ToInt64(controlInfo.AdjustTime)
	fallbackEndTime := fallbackStartTime + int64(controlInfo.EndDuration)

	// UseOpen 的判断：如果回落开始时间在当前K线范围内或之前，就使用开盘价格
	if fallbackStartTime <= currentKlineEnd {
		// 回落在当前K线结束时或之前开始，说明这根K线开盘时还是控盘状态
		UseOpen = true
	} else {
		// 回落在当前K线结束后才开始，这根K线完全不涉及回落
		UseOpen = false
	}

	// UseClose 的判断：回落结束时间 >= 当前K线结束时间，就使用控盘收盘价格
	if fallbackEndTime >= currentKlineEnd {
		// 回落在当前K线结束后才结束，还在回落过程中，使用回落计算的收盘价格
		UseClose = true
	} else {
		// 回落在当前K线内结束，不使用控盘的收盘价格，使用市场价格
		UseClose = false
	}

	// 构造Redis存储数据
	klineData := structs.KlineDataShadow{
		UseOpen:   UseOpen,
		UseClose:  UseClose,
		Time:      kline.Time,
		Open:      kline.Open,
		Close:     kline.Close,
		High:      kline.High,
		Low:       kline.Low,
		Volume:    kline.Volume,
		Turnover:  kline.Turnover,
		Direction: direction,
	}

	// 存储到Redis
	if jsonData, err := json.Marshal(klineData); err == nil {
		redis.KlineShadowStoreData(userID, "alltick", symbol, "utc", kline.Interval, kline.Time, string(jsonData))
		fmt.Printf("✅ DisableModifier Redis存储: symbol=%s, interval=%s, time=%d, UseOpen=%v, UseClose=%v, direction=%s\n",
			symbol, kline.Interval, kline.Time, UseOpen, UseClose, direction)
	} else {
		fmt.Printf("❌ DisableModifier Redis存储失败: symbol=%s, interval=%s, time=%d, err=%v\n",
			symbol, kline.Interval, kline.Time, err)
	}
}

// 存储EnableModifier的K线数据到Redis
func (k *klineOffsetLinear) storeEnableModifierKlinesToRedis(dataCopy []structs.KlineRetData, userID, symbol string, currentOffset decimal.Decimal, controlInfo *model.SingleSymbolControlInfo) {
	direction := "up" // 偏移方向,默认向上偏移
	if controlInfo.TotalOffset.IsNegative() {
		direction = "down" // 如果总偏移量是负数,说明是向下偏移
	}

	// 计算控盘开始时间（对齐到分钟）
	controlStartTime := cast.ToInt64(controlInfo.StartTime)
	controlInfoStartMinuteAlignedTimestamp := utils.GetMinuteStartTimestamp(controlStartTime)

	for i := 0; i < len(dataCopy); i++ {
		var UseOpen, UseClose bool
		nextTime := function.NextKlineTimestamp(dataCopy[i].Time, "", dataCopy[i].Interval)

		// 判断控盘是否在当前K线时间范围内
		currentKlineStart := dataCopy[i].Time
		currentKlineEnd := nextTime

		// 控盘开始时间是否在当前K线之前
		controlStartsBeforeCurrentKline := controlInfoStartMinuteAlignedTimestamp < currentKlineStart
		// 如果控盘开始时间 <= 当前K线结束时间，就使用控盘收盘价
		UseClose = controlInfoStartMinuteAlignedTimestamp <= currentKlineEnd

		if controlInfoStartMinuteAlignedTimestamp >= currentKlineStart && controlInfoStartMinuteAlignedTimestamp < currentKlineEnd {
			UseOpen = false // 控盘在K线内开始，不使用开盘价格
		} else {
			if controlStartsBeforeCurrentKline {
				UseOpen = true // 控盘在当前K线开始前就开始了，使用开盘价格
			}
		}

		// 构造Redis存储数据
		klineData := structs.KlineDataShadow{
			UseOpen:   UseOpen,
			UseClose:  UseClose,
			Time:      dataCopy[i].Time,
			Open:      dataCopy[i].Open,
			Close:     dataCopy[i].Close,
			High:      dataCopy[i].High,
			Low:       dataCopy[i].Low,
			Volume:    dataCopy[i].Volume,
			Turnover:  dataCopy[i].Turnover,
			Direction: direction,
		}

		// 序列化并存储到Redis
		if jsonData, err := json.Marshal(klineData); err == nil {
			redis.KlineShadowStoreData(userID, "alltick", symbol, "utc", dataCopy[i].Interval, dataCopy[i].Time, string(jsonData))
			fmt.Printf("✅ EnableModifier Redis存储: symbol=%s, interval=%s, time=%d, UseOpen=%v, UseClose=%v, direction=%s\n",
				symbol, dataCopy[i].Interval, dataCopy[i].Time, UseOpen, UseClose, direction)
		} else {
			fmt.Printf("❌ EnableModifier Redis存储失败: symbol=%s, interval=%s, time=%d, err=%v\n",
				symbol, dataCopy[i].Interval, dataCopy[i].Time, err)
		}
	}
}

// 与历史K线比较，确保价格连续性
func (k *klineOffsetLinear) compareWithHistoryKline(kline *structs.KlineRetData, controlInfo *model.SingleSymbolControlInfo, isFirstKline bool, symbol string) {
	// 获取历史K线数据进行比较
	currentKlineHistory, err := controlInfo.GetTimeframeKline(cast.ToString(kline.Time), kline.Interval)
	if err != nil {
		// 没有历史数据，跳过比较
		return
	}

	historyHigh := currentKlineHistory.High.InexactFloat64()
	historyLow := currentKlineHistory.Low.InexactFloat64()

	// 🔍 判断是否是峰值波动期第一根K线
	var isFirstKlineInPeakPeriod bool
	if controlInfo.IsReachedMaxOffset && controlInfo.FloatRange > 0 {
		adjustTime := cast.ToInt64(controlInfo.AdjustTime)
		currentTime := kline.Time
		intervalSeconds := k.getIntervalSeconds(kline.Interval)
		if adjustTime > 0 && adjustTime >= currentTime && adjustTime < currentTime+intervalSeconds {
			isFirstKlineInPeakPeriod = true
		}
	}

	if isFirstKlineInPeakPeriod {
		fmt.Printf("🎯 峰值波动期第一根K线: 跳过历史价格保护，historyHigh=%v, historyLow=%v\n", historyHigh, historyLow)
		return
	}

	// 最高价：取历史和当前的较高值
	if kline.High < historyHigh {
		kline.High = historyHigh
		fmt.Printf("🔧 历史最高价比较: symbol=%s, interval=%s, time=%d, 采用历史最高价=%v\n",
			symbol, kline.Interval, kline.Time, historyHigh)
	}

	// 最低价处理：线性模式上升期需要特殊处理影线
	if controlInfo.ControlType == "linear" && !controlInfo.IsReachedMaxOffset && !isFirstKline {
		// 上升期且非第一根K线：检查是否是上升趋势
		currentOpenPrice := decimal.NewFromFloat(kline.Open)
		currentClosePrice := decimal.NewFromFloat(kline.Close)
		isRising := currentClosePrice.GreaterThanOrEqual(currentOpenPrice)

		if isRising {
			// 📈 上升期：强制设置最低价=开盘价，消除下影线
			kline.Low = kline.Open
			fmt.Printf("🔧 上升期影线修复: symbol=%s, interval=%s, 强制设置最低价=开盘价=%v，历史最低价=%v被忽略\n",
				symbol, kline.Interval, kline.Open, historyLow)
		} else if kline.Low > historyLow {
			// 📉 下降期：使用历史最低价
			kline.Low = historyLow
			fmt.Printf("🔧 下降期历史最低价: symbol=%s, interval=%s, 采用历史最低价=%v\n",
				symbol, kline.Interval, historyLow)
		}
	} else if kline.Low > historyLow {
		// 其他情况：直接使用历史最低价
		kline.Low = historyLow
		fmt.Printf("🔧 历史最低价比较: symbol=%s, interval=%s, time=%d, 采用历史最低价=%v\n",
			symbol, kline.Interval, kline.Time, historyLow)
	}
}

// 修复K线影线
func (k *klineOffsetLinear) fixKlineShadows(kline *structs.KlineRetData, controlInfo *model.SingleSymbolControlInfo) {
	// 只在线性模式的上升期进行影线修复
	if controlInfo.ControlType != "linear" || controlInfo.IsReachedMaxOffset {
		return
	}

	openPrice := decimal.NewFromFloat(kline.Open)
	closePrice := decimal.NewFromFloat(kline.Close)
	isRising := closePrice.GreaterThanOrEqual(openPrice)

	if isRising {
		// 📈 上升期：最低价 = 开盘价，消除下影线
		kline.Low = kline.Open
	} else {
		// 📉 下降期：最低价 = 收盘价，与价格保持一致
		kline.Low = kline.Close
	}
}
