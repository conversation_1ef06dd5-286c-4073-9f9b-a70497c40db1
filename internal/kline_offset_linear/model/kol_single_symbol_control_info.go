package model

import (
	"fmt"

	cmap "github.com/orcaman/concurrent-map/v2"
	"github.com/shopspring/decimal"
)

/*
单个产品的控制信息
*/
type SingleSymbolControlInfo struct {
	Symbol                     string          `json:"symbol"`                // 产品名称
	StartTime                  string          `json:"start_time"`            // 控制开始时间(秒)
	AdjustTime                 string          `json:"adjust_time"`           // 再次调整时间(秒)
	EndTime                    string          `json:"end_time"`              // 控制结束时间(秒)
	TotalOffset                decimal.Decimal `json:"total_offset"`          // 总偏移量(目标偏移量)
	AsksPriceMin               decimal.Decimal `json:"asks_price_min"`        // 买跌浮动最小值
	AsksPriceMax               decimal.Decimal `json:"asks_price_max"`        // 买涨浮动最大值
	OrderBookDiffMin           decimal.Decimal `json:"order_book_diff_min"`   // 盘口价差最小值
	OrderBookDiffMax           decimal.Decimal `json:"order_book_diff_max"`   // 盘口价差最大值
	PreviousReachedTotalOffset decimal.Decimal `json:"last_total_offset"`     // 上一次到达的偏移量
	AppliedOffset              decimal.Decimal `json:"applied_offset"`        // 已偏移量(当前偏移量)
	AdjustOffset               decimal.Decimal `json:"adjust_offset"`         // 调整偏移量(真正每次调整后所需增加的偏移量)
	MaxOffsetPerSecond         decimal.Decimal `json:"max_offset_per_second"` // 最大每秒偏移量
	StartDuration              int32           `json:"start_duration"`        // 开始控盘偏移时间(秒)
	EndDuration                int32           `json:"end_duration"`          // 结束控盘偏移时间(秒)
	// 是否到达最大偏移量
	IsReachedMaxOffset  bool                                                                                       `json:"is_reached_max_offset"` // 是否到达最大偏移量
	IsCanReset          bool                                                                                       `json:"is_can_reset"`          // 是否可以重置
	IsInFallback        bool                                                                                       `json:"is_in_fallback"`        // 是否在回落期
	ControlKlineHistory cmap.ConcurrentMap[string, cmap.ConcurrentMap[string, *SingleSymbolTimeframeControlKline]] `json:"control_kline_history"` // 这一次控盘期间所有的控盘K线信息(控制后的数据) map[时间]map[k线颗粒]控盘k线信息
	Direction           string                                                                                     `json:"direction"`             // 偏移方向
	// 线性模式专用字段
	ControlType      string          `json:"control_type"`       // 控盘类型: gradient/linear
	FloatRange       float64         `json:"float_range"`        // 浮动绝对值范围
	LastControlPrice decimal.Decimal `json:"last_control_price"` // 上次控盘后的价格（线性模式使用）
	DecimalPlaces    int32           `json:"decimal_places"`     // 用户配置的小数位数
}

/*
获取一个用户对应产品的控盘信息中的控盘K线信息(对其时间颗粒)
*/
func (k *SingleSymbolControlInfo) GetTimeframeKline(klineTime string, interval string) (*SingleSymbolTimeframeControlKline, error) {
	intervalKline, ok := k.ControlKlineHistory.Get(klineTime)
	if ok {
		kline, ok := intervalKline.Get(interval)
		if ok {
			return kline, nil
		} else {
			return nil, fmt.Errorf("❌获取控盘K线信息失败,没有这个时间颗粒的控盘K线信息, klineTime: %s, interval: %s", klineTime, interval)
		}
	} else {
		return nil, fmt.Errorf("❌获取控盘K线信息失败,没有这个时间的控盘K线信息, klineTime: %s", klineTime)
	}
}
