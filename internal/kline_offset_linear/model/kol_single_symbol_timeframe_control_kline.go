package model

import "github.com/shopspring/decimal"

/*
偏移量模式,被控制的K线的情况
一个时间周期的数据
*/
type SingleSymbolTimeframeControlKline struct {
	Open     decimal.Decimal `json:"open"`     // 开盘价
	Close    decimal.Decimal `json:"close"`    // 收盘价
	High     decimal.Decimal `json:"high"`     // 最高价
	Low      decimal.Decimal `json:"low"`      // 最低价
	Volume   decimal.Decimal `json:"volume"`   // 成交量
	Turnover decimal.Decimal `json:"turnover"` // 成交额
}
