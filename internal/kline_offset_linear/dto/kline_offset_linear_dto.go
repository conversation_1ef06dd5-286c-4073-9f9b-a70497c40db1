package dto

import "github.com/shopspring/decimal"

/*
KlineOffsetLinearRequest 请求结构体
*/
type PostKlineOffsetLinearRequest struct {
	// 用户ID
	UserID string `json:"user_id" binding:"required"`
	// token
	Token string `json:"token" binding:"required"`
	// 名称
	Symbol string `json:"symbol" binding:"required"`
	// 偏移值
	Offset decimal.Decimal `json:"offset" binding:"required,decimal_gt0"`
	// 偏移方向 1: up(涨) 2: down(跌), 3: over (结束)
	Direction string `json:"direction" binding:"required,oneof=up down"`
	// 偏移时间 (本次操作的偏移持续时间,目前让meta-trader-server 写死 60 秒传过来)
	Duration int64 `json:"duration" binding:"required"`
	// 买跌浮动最小值
	AsksPriceMin decimal.Decimal `json:"asks_price_min,omitempty"`
	// 买涨浮动最大值
	AsksPriceMax decimal.Decimal `json:"asks_price_max,omitempty"`
	// 盘口价差最小值
	OrderBookDiffMin decimal.Decimal `json:"order_book_diff_min,omitempty"`
	// 盘口价差最大值
	OrderBookDiffMax decimal.Decimal `json:"order_book_diff_max,omitempty"`
	// 浮动几率(是否反向拉的几率)
	FloatingRate decimal.Decimal `json:"floating_rate" binding:"required"`
	// 最小浮动值
	MinFloating decimal.Decimal `json:"min_floating" binding:"required"`
	// 最大浮动值
	MaxFloating decimal.Decimal `json:"max_floating" binding:"required"`
	// 控盘类型: gradient(渐变)/linear(线性)，默认为gradient
	Type string `json:"type" binding:"omitempty,oneof=gradient linear"`
}

type DelKlineOffsetLinearRequest struct {
	// 用户ID
	UserID string `json:"user_id" binding:"required"`
	// token
	Token string `json:"token" binding:"required"`
	// 名称
	Symbol string `json:"symbol" binding:"required"`
}

type GetKlineOffsetLinearListRequest struct {
	// 用户ID
	UserID string `form:"user_id" binding:"required"`
	// token
	Token string `form:"token" binding:"required"`
}

type GetKlineOffsetLinearHistoryRequest struct {
	// 用户ID
	UserID string `json:"user_id" binding:"required"`
	// token
	Token string `json:"token" binding:"required"`
	// 名称
	Symbol string `json:"symbol" binding:"required"`
}
