package repository

import (
	"context"
	"errors"
	"fmt"
	"gorm.io/gorm"
	kcc_model "trading_tick_server/internal/kline_control_center/model"
	kcc_types "trading_tick_server/internal/kline_control_center/types"
	"trading_tick_server/internal/kline_offset_linear/model"
	"trading_tick_server/lib/mysql"
)

/*
数据访问层（数据库和 Redis 操作）
*/
func SaveKlineOffsetLinear(ctx context.Context, tx *gorm.DB, m *model.KlineOffsetLinearDB) error {
	result := tx.WithContext(ctx).Create(m)
	if result.Error != nil {
		return fmt.Errorf("当前用户ID: %v, 产品: %v, 偏移量控制(线性模式)设置失败, 错误信息: %v\n", m.UserID, m.Symbol, result.Error)
	}
	if result.RowsAffected == 0 {
		return errors.New("create failed: no rows affected")
	}

	return nil
}

// GetUnfinishedLinearControlRecords 查询所有未结束的线性模式控盘记录
func GetUnfinishedLinearControlRecords(ctx context.Context) ([]kcc_model.KlineControlRecordDB, error) {
	var records []kcc_model.KlineControlRecordDB
	err := mysql.M.WithContext(ctx).
		Where("is_end = 0 AND control_type = ?", string(kcc_types.ENUM_KLINE_CONTROL_TYPE_LINEAR)).
		Find(&records).Error

	if err != nil {
		return nil, fmt.Errorf("查询未结束线性控盘记录失败: %v", err)
	}

	return records, nil
}

// GetKlineOffsetLinearByUserAndSymbol 根据用户ID和产品查询线性偏移量记录
func GetKlineOffsetLinearByUserAndSymbol(ctx context.Context, userID string, symbol string) (*model.KlineOffsetLinearDB, error) {
	var record model.KlineOffsetLinearDB
	err := mysql.M.WithContext(ctx).
		Where("user_id = ? AND symbol = ?", userID, symbol).
		Order("id DESC").
		First(&record).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // 没找到记录是正常情况
		}
		return nil, fmt.Errorf("查询线性偏移量记录失败: %v", err)
	}

	return &record, nil
}
