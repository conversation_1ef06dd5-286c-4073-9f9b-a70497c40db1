package controller

import (
	"context"
	"github.com/gin-gonic/gin"
	"net/http"
	"trading_tick_server/internal/kline_offset_linear/dto"
	"trading_tick_server/internal/kline_offset_linear/service"
	"trading_tick_server/internal/user/user_service"
	"trading_tick_server/lib/structs"
	serviceCommon "trading_tick_server/web/service/common"
)

/*
K线控制
偏移量(线性模式)
*/
func PostKlineOffsetLinear(c *gin.Context) {
	var request dto.PostKlineOffsetLinearRequest
	if err := c.Bind(&request); err == nil {
		k := service.NewKlineOffsetLinear(user_service.NewUserServerInstance())
		request.Type = "linear" // 设置类型为线性模式
		if err = k.<PERSON>ontrol(context.Background(), request); err != nil {
			c.JSON(http.StatusOK, structs.Response{Code: serviceCommon.CodeCommonError, Message: err.<PERSON>r(), Error: err.Error()})
			return
		}
		// 返回成功
		if request.Type == "linear" {
			c.JSON(http.StatusOK, structs.Response{Code: serviceCommon.CodeSuccess, Message: "K线偏移量(线形模式)控制成功"})
		} else if request.Type == "" || request.Type == "gradient" {
			c.JSON(http.StatusOK, structs.Response{Code: serviceCommon.CodeSuccess, Message: "K线偏移量(渐变模式)控制成功"})
		} else {
			c.JSON(http.StatusOK, structs.Response{Code: serviceCommon.CodeSuccess, Message: "K线偏移量(其他模式)控制成功"})
		}
	} else {
		c.JSON(http.StatusBadRequest, serviceCommon.ParamError(err))
	}
}

func DelKlineOffsetLinear(c *gin.Context) {
	var request dto.DelKlineOffsetLinearRequest
	if err := c.Bind(&request); err == nil {
		// 实例化对应服务
		k := service.NewKlineOffsetLinear(user_service.NewUserServerInstance())
		if err = k.StopControl(context.Background(), request); err != nil {
			c.JSON(http.StatusOK, structs.Response{Code: serviceCommon.CodeCommonError, Message: err.Error(), Error: err.Error()})
			return
		}
		// 返回成功
		c.JSON(http.StatusOK, structs.Response{Code: serviceCommon.CodeSuccess, Message: "K线偏移量控制删除成功"})
	} else {
		c.JSON(http.StatusBadRequest, serviceCommon.ParamError(err))
	}
}

func GetKlineOffsetLinearList(c *gin.Context) {
	var req dto.GetKlineOffsetLinearListRequest
	if err := c.Bind(&req); err == nil {
		// 获取服务实例
		k := service.NewKlineOffsetLinear(user_service.NewUserServerInstance())
		// 从内存中获取所有偏移数据
		allOffsets, err := k.GetControlList(context.Background(), req)
		if err != nil {
			c.JSON(http.StatusOK, structs.Response{Code: serviceCommon.CodeCommonError, Message: err.Error(), Error: err.Error()})
			return
		}
		c.JSON(http.StatusOK, structs.Response{Code: serviceCommon.CodeSuccess, Message: "获取成功", Data: allOffsets})
		return
	} else {
		c.JSON(http.StatusBadRequest, serviceCommon.ParamError(err))
	}
}

/*
获取控盘历史数据
*/
/*func GetKlineOffsetImmediateHistory(c *gin.Context) {
	var req dto.GetKlineOffsetLinearHistoryRequest
	if err := c.Bind(&req); err == nil {
		// 获取服务实例
		k := service.NewKlineOffsetImmediate(user_service.NewUserServerInstance())
		// 从内存中获取所有偏移数据
		allOffsets, err := k.GetControlHistory(context.Background(), req)
		if err != nil {
			c.JSON(http.StatusOK, structs.Response{Code: serviceCommon.CodeCommonError, Message: "获取偏移量失败" + err.Error(), Error: err.Error()})
			return
		}
		c.JSON(http.StatusOK, structs.Response{Code: serviceCommon.CodeSuccess, Message: "获取成功", Data: allOffsets})
		return
	} else {
		c.JSON(http.StatusBadRequest, serviceCommon.ParamError(err))
	}
}
*/
