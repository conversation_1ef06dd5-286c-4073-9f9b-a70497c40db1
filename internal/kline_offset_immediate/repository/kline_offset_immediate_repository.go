package repository

import (
	"context"
	"errors"
	"fmt"
	"gorm.io/gorm"
	kcc_model "trading_tick_server/internal/kline_control_center/model"
	kcc_types "trading_tick_server/internal/kline_control_center/types"
	koi_model "trading_tick_server/internal/kline_offset_immediate/model"
	"trading_tick_server/lib/mysql"
)

/*
数据访问层（数据库和 Redis 操作）
*/
func SaveKlineOffsetImmediate(ctx context.Context, tx *gorm.DB, m *koi_model.KlineOffsetImmediateDB) error {
	result := tx.WithContext(ctx).Create(m)
	if result.Error != nil {
		fmt.Printf("当前用户ID: %v, 产品: %v, 偏移量控制(立即模式)设置失败, 错误信息: %v\n", m.UserID, m.Symbol, result.Error)
	}
	if result.RowsAffected == 0 {
		return errors.New("create failed: no rows affected")
	}
	return nil
}

// GetUnfinishedControlRecords 查询所有未结束的立即模式控盘记录
func GetUnfinishedControlRecords(ctx context.Context) ([]kcc_model.KlineControlRecordDB, error) {
	var records []kcc_model.KlineControlRecordDB
	err := mysql.M.WithContext(ctx).
		Where("is_end = 0 AND control_type = ?", string(kcc_types.ENUM_KLINE_CONTROL_TYPE_IMMEDIATE)).
		Find(&records).Error

	if err != nil {
		return nil, fmt.Errorf("查询未结束立即控盘记录失败: %v", err)
	}

	return records, nil
}

// GetKlineOffsetImmediateByUserAndSymbol 根据用户ID和产品查询偏移量记录
func GetKlineOffsetImmediateByUserAndSymbol(ctx context.Context, userID string, symbol string) (*koi_model.KlineOffsetImmediateDB, error) {
	var record koi_model.KlineOffsetImmediateDB
	err := mysql.M.WithContext(ctx).
		Where("user_id = ? AND symbol = ?", userID, symbol).
		Order("id DESC").
		First(&record).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // 没找到记录是正常情况
		}
		return nil, fmt.Errorf("查询偏移量记录失败: %v", err)
	}

	return &record, nil
}
