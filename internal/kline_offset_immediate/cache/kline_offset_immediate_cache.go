package cache

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	cmap "github.com/orcaman/concurrent-map/v2"
	goredis "github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
	"github.com/sknun/cf/cast"
	"time"
	"trading_tick_server/internal/kline_offset_immediate/dto"
	"trading_tick_server/internal/kline_offset_immediate/model"
	"trading_tick_server/lib/redis"
)

/*
缓存-K线偏移量控制(立即模式)
*/
// 设置偏移控制立即模式数据到缓存
func SetKlineOffsetImmediateToCache(ctx context.Context, userId string, symbol string, offsetInfo model.KlineOffsetImmediateRedis, expiration time.Duration) error {
	key := "kline_offset_immediate:" + cast.ToString(userId) + ":" + symbol + ":"
	// JSON 序列化
	val, err := json.Marshal(offsetInfo)
	if err != nil {
		return fmt.Errorf("failed to encode KlineOffsetImmediate to JSON: %w", err)
	}

	// 设置到 Redis 缓存
	_, err = redis.RDB.Set(ctx, key, val, expiration).Result()
	if err != nil {
		return fmt.Errorf("failed to set KlineOffsetImmediate to Redis: %w", err)
	}
	return nil
}

// 从缓存中获取偏移控制立即模式数据
/*
	1.	先从 Redis 缓存中获取 KlineOffsetImmediate 对象
	2.	如果不存在（返回 redis.Nil），则返回空结构和 nil 错误
	3.	如果其他错误，返回错误
	4.	如果成功，反序列化 JSON 返回结构体对象
*/
func GetKlineOffsetImmediateFromCache(ctx context.Context, userId string, symbol string) (model.KlineOffsetImmediateRedis, error) {
	var result model.KlineOffsetImmediateRedis
	key := "kline_offset_immediate:" + cast.ToString(userId) + ":" + symbol + ":"
	val, err := redis.RDB.Get(ctx, key).Result()
	if errors.Is(err, goredis.Nil) {
		// 缓存不存在，不算错误，返回空结构体
		return result, nil
	} else if err != nil {
		// Redis 查询出错
		return result, fmt.Errorf("failed to get KlineOffsetImmediate from Redis: %w", err)
	}

	// JSON 反序列化
	err = json.Unmarshal([]byte(val), &result)
	if err != nil {
		return result, fmt.Errorf("failed to decode KlineOffsetImmediate from Redis: %w", err)
	}

	return result, nil
}

// 删除缓存中的偏移控制立即模式数据
func DeleteKlineOffsetImmediateFromCache(ctx context.Context, userId string, symbol string) error {
	key := "kline_offset_immediate:" + cast.ToString(userId) + ":" + symbol + ":"

	// 调用 Redis 的 DEL 命令
	_, err := redis.RDB.Del(ctx, key).Result()
	if err != nil {
		return fmt.Errorf("failed to delete KlineOffsetImmediate from Redis: %w", err)
	}

	return nil
}

// 获取偏移控制(立即模式)的所有数据
func GetAllKlineOffsetImmediateFromCache(ctx context.Context, offsetMap cmap.ConcurrentMap[string, cmap.ConcurrentMap[string, decimal.Decimal]]) error {
	var cursor uint64
	pattern := "kline_offset_immediate:*:*:"

	for {
		keys, nextCursor, err := redis.RDB.Scan(ctx, cursor, pattern, 100).Result()
		if err != nil {
			return fmt.Errorf("failed to scan Redis keys: %w", err)
		}

		for _, key := range keys {
			val, err := redis.RDB.Get(ctx, key).Result()
			if err != nil {
				if errors.Is(err, goredis.Nil) {
					continue
				}
				return fmt.Errorf("failed to get key %s: %w", key, err)
			}

			var offset model.KlineOffsetImmediateRedis
			if err := json.Unmarshal([]byte(val), &offset); err != nil {
				return fmt.Errorf("failed to decode key %s: %w", key, err)
			}

			symbol := offset.Symbol
			userId := offset.UserID
			offsetVal := offset.TotalOffset

			// 获取或初始化内层 map
			inner, ok := offsetMap.Get(offset.UserID)
			if !ok {
				inner = cmap.New[decimal.Decimal]()
			}
			inner.Set(symbol, offsetVal)
			offsetMap.Set(userId, inner) // ✅ 确保写入
		}

		cursor = nextCursor
		if cursor == 0 {
			break
		}
	}

	return nil
}

// 获取所有偏移控制(立即模式)缓存数据
func GetAllKlineOffsetImmediate(ctx context.Context) ([]model.KlineOffsetImmediateRedis, error) {
	var cursor uint64
	var result []model.KlineOffsetImmediateRedis

	pattern := "kline_offset_immediate:*"

	for {
		keys, newCursor, err := redis.RDB.Scan(ctx, cursor, pattern, 100).Result()
		if err != nil {
			return nil, fmt.Errorf("scan error: %w", err)
		}

		for _, key := range keys {
			val, err := redis.RDB.Get(ctx, key).Result()
			if err != nil {
				if errors.Is(err, goredis.Nil) {
					continue // key 不存在，跳过
				}
				return nil, fmt.Errorf("get error for key %s: %w", key, err)
			}

			var data model.KlineOffsetImmediateRedis
			if err := json.Unmarshal([]byte(val), &data); err != nil {
				return nil, fmt.Errorf("unmarshal error for key %s: %w", key, err)
			}
			result = append(result, data)
		}

		if newCursor == 0 {
			break
		}
		cursor = newCursor
	}

	return result, nil
}

/*
更新或设置偏移量控制(立即模式)的缓存
*/
func KlineOffsetCacheSet(ctx context.Context, req dto.PostKlineOffsetImmediateRequest) (*model.KlineOffsetImmediateRedis, error) {
	// 判断当前用户当前产品是否已经存在偏移量控制
	offsetImmediateCache, err := GetKlineOffsetImmediateFromCache(ctx, req.UserID, req.Symbol)
	if err != nil {
		return nil, err
	}
	// 总偏移量
	totalOffset := decimal.NewFromFloat(0)
	// 是否偏移期间(是否存在控盘)
	if offsetImmediateCache.UserID == req.UserID && offsetImmediateCache.Symbol == req.Symbol {
		totalOffset = offsetImmediateCache.TotalOffset
		fmt.Printf("当前用户ID: %v, 产品: %v, ❌已存在偏移量控制(立即模式)\n", req.UserID, req.Symbol)
	} else {
		fmt.Printf("当前用户ID: %v, 产品: %v, 不存在偏移量控制(立即模式)\n", req.UserID, req.Symbol)
	}
	// 2.2 设置偏移量到缓存
	offset := decimal.Zero
	if req.Direction == "up" {
		offset = offset.Add(req.Offset)
	} else if req.Direction == "down" {
		offset = offset.Sub(req.Offset)
	} else {
		return nil, errors.New("偏移方向错误,只能是 up 或 down")
	}
	offsetImmediate := model.KlineOffsetImmediateRedis{
		UserID:      req.UserID,
		Symbol:      req.Symbol,
		ControlTime: time.Now().Unix(),
		TotalOffset: totalOffset.Add(offset),
	}
	err = SetKlineOffsetImmediateToCache(ctx, req.UserID, req.Symbol, offsetImmediate, 0)
	if err != nil {
		return nil, err
	}
	fmt.Printf("😋当前用户ID: %v, 产品: %v, 偏移量控制(立即模式)缓存设置成功, 当前总偏移量: %v\n", req.UserID, req.Symbol, offsetImmediate.TotalOffset)
	return &offsetImmediate, nil
}
