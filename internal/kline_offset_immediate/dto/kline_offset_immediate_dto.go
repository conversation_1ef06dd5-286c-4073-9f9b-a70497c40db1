package dto

import "github.com/shopspring/decimal"

/*
请求与响应结构体
*/

// KlineOffsetRequest 请求结构体
// 立即模式
type PostKlineOffsetImmediateRequest struct {
	// 用户ID
	UserID string `json:"user_id" binding:"required"`
	// token
	Token string `json:"token" binding:"required"`
	// 名称
	Symbol string `json:"symbol" binding:"required"`
	// 偏移值
	Offset decimal.Decimal `json:"offset" binding:"required,decimal_gt0"`
	// 偏移方向 1: up(涨) 2: down(跌), 3: over (结束)
	Direction string `json:"direction" binding:"required,oneof=up down"`
	// 买跌浮动最小值
	AsksPriceMin decimal.Decimal `json:"asks_price_min,omitempty"`
	// 买跌浮动最大值
	AsksPriceMax decimal.Decimal `json:"asks_price_max,omitempty"`
	// 盘口价差最小值
	OrderBookDiffMin decimal.Decimal `json:"order_book_diff_min,omitempty"`
	// 盘口价差最大值
	OrderBookDiffMax decimal.Decimal `json:"order_book_diff_max,omitempty"`
}

type DelKlineOffsetImmediateRequest struct {
	// 用户ID
	UserID string `json:"user_id" binding:"required"`
	// token
	Token string `json:"token" binding:"required"`
	// 名称
	Symbol string `json:"symbol" binding:"required"`
}

type GetKlineOffsetImmediateListRequest struct {
	// 用户ID
	UserID string `form:"user_id" binding:"required"`
	// token
	Token string `form:"token" binding:"required"`
}

type GetKlineOffsetImmediateHistoryRequest struct {
	// 用户ID
	UserID string `json:"user_id" binding:"required"`
	// token
	Token string `json:"token" binding:"required"`
	// 名称
	Symbol string `json:"symbol" binding:"required"`
}
