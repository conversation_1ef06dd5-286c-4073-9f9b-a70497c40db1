package service

import (
	"encoding/json"
	"fmt"
	"trading_tick_server/common/utils"
	"trading_tick_server/internal/data_transfer_station/service"
	"trading_tick_server/internal/data_transfer_station/types"
	kcc_service "trading_tick_server/internal/kline_control_center/service"
	"trading_tick_server/internal/kline_offset_immediate/model"
	"trading_tick_server/lib/function"
	"trading_tick_server/lib/mysql"
	"trading_tick_server/lib/redis"
	"trading_tick_server/lib/structs"

	cmap "github.com/orcaman/concurrent-map/v2"
	"github.com/shopspring/decimal"
	"github.com/sknun/cf/cast"
)

/*
数据控制(进行)
*/
func (k *klineOffsetImmediate) DataControl(userID string, symbol string) error {
	/*
		获取用户房间
	*/
	productRoom, ok := service.GetStationProductRoom[types.ExtraDataPkg](userID, symbol)
	if !ok {
		return fmt.Errorf("数据传输中心,用户房间不存在, userID: %s", userID)
	}
	/*
		激活房间的修改功能(数据控制)
	*/
	err := productRoom.EnableModification(k.EnableModifier, k.Selector)
	if err != nil {
		return fmt.Errorf("DataControl 数据控制功能失败, err: %v", err)
	}
	return nil
}

/*
数据控制(停止)
*/
func (k *klineOffsetImmediate) OverControl(userID string, symbol string) error {
	/*
		获取用户房间
	*/
	productRoom, ok := service.GetStationProductRoom[types.ExtraDataPkg](userID, symbol)
	if !ok {
		return fmt.Errorf("数据传输中心,用户房间不存在, userID: %s", userID)
	}
	/*
		⚠️注意: 这里是重设房间的修改功能(数据控制)
	*/
	err := productRoom.EnableModification(k.DisableModifier, k.Selector)
	if err != nil {
		return fmt.Errorf("OverControl 数据控制功能失败, err: %v", err)
	}
	return nil
}

/*
数据选择器
选择需要控制的数据
当前只需要选择该用户需要控制的对应币种
*/
func (k *klineOffsetImmediate) Selector(userID string, data types.ExtraDataPkg) bool {
	originalData := data.OriginalData
	if len(*originalData) == 0 {
		return false
	}

	if userControlMap, ok := k.OffsetInfo.Get(userID); ok {
		if _, exists := userControlMap.Get((*originalData)[0].Code); exists {
			return true
		}
	}

	return false
}

/*
数据修改器(激活时期)
*/
func (k *klineOffsetImmediate) EnableModifier(userID string, data *types.ExtraDataPkg) *types.ExtraDataPkg {
	/*
		⚠️注意:拷贝一份数据,避免修改原数据
	*/
	originalData := data.OriginalData
	dataCopy := append([]structs.KlineRetData(nil), *data.OriginalData...)
	symbol := dataCopy[0].Code
	// 得到用户ID对应的偏移量控制数据
	symbolMap, ok := k.OffsetInfo.Get(userID)
	if ok {
		// 确认当前数据的产品是否在偏移量控制列表中
		offset, exist := symbolMap.Get(symbol)
		if exist { // 开始控制
			// 获取控制信息,默认这里肯定有控制信息
			singleSymbolControlInfos, _ := k.GetSingleSymbolControlInfo(userID, symbol)
			// 计算控制开始时间这根K线的整秒数
			controlInfoStartMinuteAlignedTimestamp := utils.GetMinuteStartTimestamp(cast.ToInt64(singleSymbolControlInfos.StartTime))
			/*
				K线数据 - 时间颗粒组
			*/
			klineTimeframe := cmap.New[*model.SingleSymbolTimeframeControlKline]()
			for i := 0; i < len(dataCopy); i++ { // 循环每一个时间颗粒
				if (dataCopy)[i].Time-controlInfoStartMinuteAlignedTimestamp >= 60 { // 大于等于60秒,说明当前data数据的时间是控盘开始后的第N根K线(不是第一根),所以开盘价要取上一根的收盘价
					prevClosePrice, err := k.GetPreviousMinuteControlKlineClosePrice(userID, symbol, cast.ToString((dataCopy)[i].Time))
					if err != nil {
						// 判断控盘已经进行了多久
						controlDuration := (dataCopy)[i].Time - controlInfoStartMinuteAlignedTimestamp
						if controlDuration < 120 { // 控盘开始后的第二根K线（60-119秒）
							// 由于边界问题，使用当前K线的开盘价
							fmt.Printf("⚠️ 用户ID: %s, 产品: %s, 控盘初期获取上一分钟K线失败，使用当前开盘价, 控盘时长: %d秒\n", userID, symbol, controlDuration)
							prevClosePrice = decimal.NewFromFloat((dataCopy)[i].Open)
						} else { // 控盘已经超过2分钟
							// 尝试从ControlKlineHistory中获取最后存储的数据
							fmt.Printf("⚠️ 用户ID: %s, 产品: %s, 控盘中期获取上一分钟K线失败，查找历史存储数据, 控盘时长: %d秒\n", userID, symbol, controlDuration)

							lastStoredPrice, err := k.GetLastStoredControlKlineClosePrice(userID, symbol, (dataCopy)[i].Time)
							if err == nil {
								prevClosePrice = lastStoredPrice
								fmt.Printf("✅ 找到历史存储的K线数据，使用最后存储的收盘价\n")
							} else {
								// 没有历史存储数据，说明从控盘开始到现在都没有K线数据
								fmt.Printf("❌ 没有历史存储数据（从控盘开始到现在都没有K线），使用当前开盘价\n")
								prevClosePrice = decimal.NewFromFloat((dataCopy)[i].Open)
							}
						}
					}
					// 这里要将控盘开始的那根K线的收盘价设置为上一根K线的收盘价
					(dataCopy)[i].Open = prevClosePrice.Truncate(data.ExtraData.OriginalDecimalPlaces).InexactFloat64() // 改变开盘价
				}
				/*
					改变K线数据,收盘价,最高价,最低价,
					如果是同一根k线要判断新的最高价,最低价是否需要采用上一次的
				*/
				dataCopy[i].High = offset.Add(decimal.NewFromFloat(dataCopy[i].High)).Truncate(data.ExtraData.OriginalDecimalPlaces).InexactFloat64()   // 改变最高价
				dataCopy[i].Low = offset.Add(decimal.NewFromFloat(dataCopy[i].Low)).Truncate(data.ExtraData.OriginalDecimalPlaces).InexactFloat64()     // 改变最低价
				dataCopy[i].Close = offset.Add(decimal.NewFromFloat(dataCopy[i].Close)).Truncate(data.ExtraData.OriginalDecimalPlaces).InexactFloat64() // 改变收盘价
				currentKineHistory, err := singleSymbolControlInfos.GetTimeframeKline(cast.ToString(dataCopy[0].Time), "1m")
				if err == nil {
					// 说明当前K线在控盘期间,所以要判断新的最高价,最低价是否需要采用上一次的
					if dataCopy[i].High < currentKineHistory.High.Truncate(data.ExtraData.OriginalDecimalPlaces).InexactFloat64() {
						dataCopy[i].High = currentKineHistory.High.Truncate(data.ExtraData.OriginalDecimalPlaces).InexactFloat64()
					}
					if dataCopy[i].Low > currentKineHistory.Low.Truncate(data.ExtraData.OriginalDecimalPlaces).InexactFloat64() {
						dataCopy[i].Low = currentKineHistory.Low.Truncate(data.ExtraData.OriginalDecimalPlaces).InexactFloat64()
					}
				}
				if dataCopy[i].High < 0 {
					dataCopy[i].High = 0 // 如果最高价小于0,则设置为0
				}
				if dataCopy[i].Low < 0 {
					dataCopy[i].Low = 0 // 如果最低价小于0,则设置为0
				}
				if dataCopy[i].Close < 0 {
					dataCopy[i].Close = 0 // 如果收盘价小于0,则设置为0
				}
				if dataCopy[i].Open < 0 {
					dataCopy[i].Open = 0 // 如果开盘价小于0,则设置为0
				}
				/*
					将改变的收盘价存入控盘期间的数据中
				*/
				klineTimeframe.Set(dataCopy[i].Interval, &model.SingleSymbolTimeframeControlKline{
					Open:     decimal.NewFromFloat(dataCopy[i].Open),     // 开盘价
					Close:    decimal.NewFromFloat(dataCopy[i].Close),    // 收盘价
					High:     decimal.NewFromFloat(dataCopy[i].High),     // 最高价
					Low:      decimal.NewFromFloat(dataCopy[i].Low),      // 最低价
					Volume:   decimal.NewFromFloat(dataCopy[i].Volume),   // 成交量
					Turnover: decimal.NewFromFloat(dataCopy[i].Turnover), // 成交额
				})
				/*
					🔫临时处理,这里存放修改后的K线缓存给历史接口使用
				*/
				var UseOpen bool
				if controlInfoStartMinuteAlignedTimestamp < dataCopy[i].Time {
					// 使用开盘价格
					UseOpen = true
				}
				direction := "up" // 偏移方向,默认向上偏移
				if offset.IsNegative() {
					direction = "down" // 如果偏移量是负数,说明是向下偏移
				}
				b, _ := json.Marshal(structs.KlineDataShadow{
					UseOpen:   UseOpen,              // 是否使用开盘价格
					UseClose:  true,                 // 是否使用收盘价格
					Time:      dataCopy[i].Time,     // 这根k线的时间戳
					Open:      dataCopy[i].Open,     // 开盘价
					Close:     dataCopy[i].Close,    // 收盘价
					High:      dataCopy[i].High,     // 最高价
					Low:       dataCopy[i].Low,      // 最低价
					Volume:    dataCopy[i].Volume,   // 成交量
					Turnover:  dataCopy[i].Turnover, // 成交额
					Direction: direction,            // 偏移方向
				})

				go func() {
					redis.KlineShadowStoreData(userID, "alltick", symbol, "utc", dataCopy[i].Interval, (dataCopy)[0].Time, string(b))
				}()

			}
			/*
				将控盘后的K线数据,存入ControlInfo中
				ControlKlineHistory: map[时间]map[k线颗粒]控盘k线信息
			*/
			singleSymbolControlInfos.ControlKlineHistory.Set(cast.ToString(dataCopy[0].Time), klineTimeframe)
			/*
				临时处理,给予外界信息
			*/
			data.ExtraData = &types.ExtraData{
				Symbol:           symbol,
				TotalOffset:      singleSymbolControlInfos.TotalOffset,
				CurrentOffset:    singleSymbolControlInfos.TotalOffset,
				RealPrice:        decimal.NewFromFloat((*originalData)[0].Close),
				ControlPrice:     decimal.NewFromFloat(dataCopy[0].Close),
				AsksPriceMin:     singleSymbolControlInfos.AsksPriceMin,
				AsksPriceMax:     singleSymbolControlInfos.AsksPriceMax,
				OrderBookDiffMin: singleSymbolControlInfos.OrderBookDiffMin,
				OrderBookDiffMax: singleSymbolControlInfos.OrderBookDiffMax,
			}
		} else {
			fmt.Printf("用户ID: %s, 偏移量控制数据不存在\n", userID)
			data.OriginalData = &dataCopy
			return data
		}

	} else {
		fmt.Printf("用户ID: %s, 偏移量控制数据不存在\n", userID)
		data.OriginalData = &dataCopy
		return data
	}
	//fmt.Printf("🤡用户ID[%s]启用了控制,修改数据成功, 数据{ID: %v, 开盘价: %v, 收盘价: %v}\n", userID, (*data)[0].ID, (*data)[0].Open, (*data)[0].Close)
	data.OriginalData = &dataCopy
	return data
}

/*
数据修改器(关闭时期)
*/
func (k *klineOffsetImmediate) DisableModifier(userID string, data *types.ExtraDataPkg) *types.ExtraDataPkg {
	originalData := data.OriginalData
	dataCopy := append([]structs.KlineRetData(nil), *data.OriginalData...)
	symbol := dataCopy[0].Code
	singleSymbolControlInfo, err := k.GetSingleSymbolControlInfo(userID, symbol)
	if err != nil {
		fmt.Printf("DisableModifier 用户ID: %s, 产品: %s, 偏移量控制数据不存在\n", userID, symbol)
		return data
	}
	// 获取用户房间,图快暂时不 OOP 了, 忙完记得来改
	productRoom, ok := service.GetStationProductRoom[types.ExtraDataPkg](userID, symbol)
	if !ok {
		fmt.Printf("用户ID: %s, 产品: %s, 房间不存在\n", userID, symbol)
		return data
	}
	// 获取精确的控盘结束时间戳
	endTimestamp := cast.ToInt64(singleSymbolControlInfo.EndTime)
	// 获取控盘结束分钟对齐时间戳(秒级)
	endMinuteAlignedTime := utils.GetMinuteStartTimestamp(endTimestamp)

	// 如果当前K线时间超过控盘结束时间的分钟边界，则真正结束控盘
	if dataCopy[0].Time >= endMinuteAlignedTime {

		// 说明当前数据已经超过了控盘结束时间,所以不需要进行控盘,真正的结束
		productRoom.DisableModification()
		/*
			清空偏移量内存
		*/
		symbolMap, ok := k.OffsetInfo.Get(userID)
		if ok {
			symbolMap.Remove(symbol)
		} else {
			fmt.Printf("❌ 偏移量控制(立即模式)内存记录不存在, 用户ID: %v, 产品: %v", userID, symbol)
		}
		/*
			设置是否可以重制这次控盘周期内的历史标志
		*/
		singleSymbolControlInfo.IsCanResetHistory = true
		/*
			修改数据库K线控制中心信息
		*/
		err = kcc_service.SymbolControlEnd(userID, symbol, mysql.M)
		if err != nil {
			fmt.Printf("❌ 偏移量控制(立即模式)K线控制中心,控制结束修改错误, 用户ID: %v, 产品: %v", userID, symbol)
		}
		// 临时给予外界信息
		data.ExtraData = &types.ExtraData{
			Symbol:           symbol,
			TotalOffset:      decimal.Zero,
			CurrentOffset:    decimal.Zero,
			RealPrice:        decimal.NewFromFloat((*originalData)[0].Close),
			ControlPrice:     decimal.NewFromFloat(dataCopy[0].Close),
			OrderBookDiffMin: decimal.Zero,
			OrderBookDiffMax: decimal.Zero,
		}
		return data
	} else {
		/*
			当前结束信号在已控盘的数据中,所以即使结束信号来了并不是真正的结束控盘,
			这时依然需要改变数据,但是只改变开盘 open 数据,收盘 close 数据就开始使用真实值,
			⚠️注意: 特殊情况,结束信号来的时候, 和开始新信号是在同一根k线内,这时只需要改变最高价即可
		*/
		// 得到用户ID对应的偏移量控制数据
		symbolMap, ok := k.OffsetInfo.Get(userID)
		if ok {
			// 确认当前数据的产品是否在偏移量控制列表中
			offset, exist := symbolMap.Get(symbol)
			if exist { // 开始控制
				// 获取控制信息,默认这里肯定有控制信息
				controlInfos, _ := k.GetSingleSymbolControlInfo(userID, symbol)
				// 计算控制开始时间这根K线的整秒数
				controlInfoStartMinuteAlignedTimestamp := utils.GetMinuteStartTimestamp(cast.ToInt64(controlInfos.StartTime))
				// 控盘结束时间，不再额外加60秒，使用精确的结束时间
				controlInfoEndTimestamp := cast.ToInt64(controlInfos.EndTime)
				//controlInfoEndMinuteAlignedTimestamp := utils.GetMinuteStartTimestamp(controlInfoEndTimestamp)
				//currentMinuteAlignedTimestamp := cast.ToString(utils.GetStartOfCurrentMinuteTimestampUnix()) // 当前分钟开始秒数时间戳
				/*
					K线数据 - 时间颗粒组
				*/
				klineInterval := cmap.New[*model.SingleSymbolTimeframeControlKline]()
				for i := 0; i < len(dataCopy); i++ { // 循环每一个时间颗粒
					if dataCopy[i].Time-controlInfoStartMinuteAlignedTimestamp > 59 { // 大于60秒,说明当前data数据的时间是控盘开始后的第N根K线(不是第一根),所以开盘价要取上一根的收盘价
						prevClosePrice, err := k.GetPreviousMinuteControlKlineClosePrice(userID, symbol, cast.ToString(dataCopy[i].Time))
						if err != nil {
							// 判断控盘已经进行了多久
							controlDuration := dataCopy[i].Time - controlInfoStartMinuteAlignedTimestamp
							if controlDuration < 120 { // 控盘开始后的第二根K线（60-119秒）
								// 由于边界问题，使用当前K线的开盘价
								fmt.Printf("⚠️ 用户ID: %s, 产品: %s, 回落期-控盘初期获取上一分钟K线失败，使用当前开盘价, 控盘时长: %d秒\n", userID, symbol, controlDuration)
								prevClosePrice = decimal.NewFromFloat(dataCopy[i].Open)
							} else { // 控盘已经超过2分钟
								fmt.Printf("⚠️ 用户ID: %s, 产品: %s, 回落期-控盘中期获取上一分钟K线失败，查找历史存储数据, 控盘时长: %d秒\n", userID, symbol, controlDuration)

								lastStoredPrice, err := k.GetLastStoredControlKlineClosePrice(userID, symbol, dataCopy[i].Time)
								if err == nil {
									prevClosePrice = lastStoredPrice
									fmt.Printf("✅ 回落期-找到历史存储的K线数据，使用最后存储的收盘价\n")
								} else {
									// 没有历史存储数据，说明从控盘开始到现在都没有K线数据
									fmt.Printf("❌ 回落期-没有历史存储数据（从控盘开始到现在都没有K线），使用当前开盘价\n")
									prevClosePrice = decimal.NewFromFloat(dataCopy[i].Open)
								}
							}
						}
						// 这里要将控盘开始的那根K线的收盘价设置为上一根K线的收盘价
						dataCopy[i].Open = prevClosePrice.InexactFloat64() // 改变开盘价
					}
					/*
						改变K线数据,最高价,最低价
					*/
					if offset.IsNegative() {
						dataCopy[i].Low = offset.Add(decimal.NewFromFloat(dataCopy[i].Low)).InexactFloat64() // 改变最低价
					} else {
						dataCopy[i].High = offset.Add(decimal.NewFromFloat(dataCopy[i].High)).InexactFloat64() // 改变最高价
					}

					if dataCopy[i].High < 0 {
						dataCopy[i].High = 0 // 如果最高价小于0,则设置为0
					}
					if dataCopy[i].Low < 0 {
						dataCopy[i].Low = 0 // 如果最低价小于0,则设置为0
					}
					if dataCopy[i].Close < 0 {
						dataCopy[i].Close = 0 // 如果收盘价小于0,则设置为0
					}
					if dataCopy[i].Open < 0 {
						dataCopy[i].Open = 0 // 如果开盘价小于0,则设置为0
					}
					/*
						将改变的收盘价存入控盘期间的数据中

					*/
					klineInterval.Set(dataCopy[i].Interval, &model.SingleSymbolTimeframeControlKline{
						Open:     decimal.NewFromFloat(dataCopy[i].Open),     // 开盘价
						Close:    decimal.NewFromFloat(dataCopy[i].Close),    // 收盘价
						High:     decimal.NewFromFloat(dataCopy[i].High),     // 最高价
						Low:      decimal.NewFromFloat(dataCopy[i].Low),      // 最低价
						Volume:   decimal.NewFromFloat(dataCopy[i].Volume),   // 成交量
						Turnover: decimal.NewFromFloat(dataCopy[i].Turnover), // 成交额
					})
					if offset.IsZero() {
						// 如果偏移量为0,说明是回落期,不需要存储
						continue
					}

					/*
						🔫临时处理,这里存放修改后的K线缓存给历史接口使用
					*/
					var UseOpen, UseClose bool
					nextTime := function.NextKlineTimestamp(dataCopy[i].Time, data.ExtraData.TimeZone, dataCopy[i].Interval)

					// 判断控盘是否在当前K线时间范围内
					currentKlineStart := dataCopy[i].Time
					currentKlineEnd := nextTime

					// 控盘开始时间是否在当前K线之前
					controlStartsBeforeCurrentKline := controlInfoStartMinuteAlignedTimestamp < currentKlineStart
					// 控盘结束时间是否在当前K线之后（考虑精确的结束时间）
					controlEndsAfterCurrentKline := controlInfoEndTimestamp >= currentKlineEnd

					if controlInfoStartMinuteAlignedTimestamp >= currentKlineStart && controlInfoEndTimestamp <= currentKlineEnd {
						// 控盘完全在当前K线内，开盘价和收盘价都不使用
						UseClose = false
						UseOpen = false
					} else {
						if controlEndsAfterCurrentKline {
							// 控盘在当前K线结束后才结束，使用收盘价格
							UseClose = true
						}
						if controlStartsBeforeCurrentKline {
							// 控盘在当前K线开始前就开始了，使用开盘价格
							UseOpen = true
						}
					}
					direction := "up" // 偏移方向,默认向上偏移
					if offset.IsNegative() {
						direction = "down" // 如果偏移量是负数,说明是向下偏移
					}
					b, _ := json.Marshal(structs.KlineDataShadow{
						UseOpen:   UseOpen,
						UseClose:  UseClose,
						Time:      dataCopy[i].Time,     // 这根k线的时间戳
						Open:      dataCopy[i].Open,     // 开盘价
						Close:     dataCopy[i].Close,    // 收盘价
						High:      dataCopy[i].High,     // 最高价
						Low:       dataCopy[i].Low,      // 最低价
						Volume:    dataCopy[i].Volume,   // 成交量
						Turnover:  dataCopy[i].Turnover, // 成交额
						Direction: direction,            // 偏移方向
					})

					go func() {
						redis.KlineShadowStoreData(userID, "alltick", symbol, "utc", dataCopy[i].Interval, (dataCopy)[0].Time, string(b))
					}()
				}
				/*
					将控盘后的K线数据,存入ControlInfo中
					ControlKlineHistory: map[时间]map[k线颗粒]控盘k线信息
				*/
				controlInfos.ControlKlineHistory.Set(cast.ToString(dataCopy[0].Time), klineInterval)

				// 临时给予外界信息
				data.ExtraData = &types.ExtraData{
					Symbol:           symbol,
					TotalOffset:      decimal.Zero,
					CurrentOffset:    decimal.Zero,
					RealPrice:        decimal.NewFromFloat((*originalData)[0].Close),
					ControlPrice:     decimal.NewFromFloat(dataCopy[0].Close),
					AsksPriceMin:     controlInfos.AsksPriceMin,
					AsksPriceMax:     controlInfos.AsksPriceMax,
					OrderBookDiffMin: controlInfos.OrderBookDiffMin,
					OrderBookDiffMax: controlInfos.OrderBookDiffMax,
				}
			} else {
				fmt.Printf("用户ID: %s, 偏移量控制数据不存在(offset map 不存在)\n", userID)
				data.OriginalData = &dataCopy
				return data
			}
		} else {
			fmt.Printf("用户ID: %s, 偏移量控制数据不存在(symbol map 不存在)\n", userID)
			data.OriginalData = &dataCopy
			return data
		}
		data.OriginalData = &dataCopy
		return data
	}
}

/*
获取最后存储的控盘K线收盘价格
*/
func (k *klineOffsetImmediate) GetLastStoredControlKlineClosePrice(userID string, symbol string, currentTime int64) (decimal.Decimal, error) {
	// 获取用户ID对应的偏移量控制数据
	symbolMap, ok := k.ControlInfos.Get(userID)
	if ok {
		// 确认当前数据的产品是否在偏移量控制列表中
		allTimeControlInfo, exist := symbolMap.Get(symbol)
		if exist {
			// 获取所有存储的时间戳
			var timestamps []int64
			allTimeControlInfo.ControlKlineHistory.IterCb(func(key string, v cmap.ConcurrentMap[string, *model.SingleSymbolTimeframeControlKline]) {
				ts := cast.ToInt64(key)
				if ts < currentTime { // 只获取早于当前时间的数据
					timestamps = append(timestamps, ts)
				}
			})

			// 如果没有历史数据
			if len(timestamps) == 0 {
				return decimal.Decimal{}, fmt.Errorf("没有历史控盘K线数据")
			}

			// 找到最大的时间戳（最近的数据）
			var maxTimestamp int64
			for _, ts := range timestamps {
				if ts > maxTimestamp {
					maxTimestamp = ts
				}
			}

			// 获取该时间的K线数据
			klineData, err := allTimeControlInfo.GetTimeframeKline(cast.ToString(maxTimestamp), "1m")
			if err != nil {
				return decimal.Decimal{}, fmt.Errorf("获取最后存储的K线数据失败")
			}

			return klineData.Close, nil
		}
	}
	return decimal.Decimal{}, fmt.Errorf("控盘数据不存在")
}

/*
获取一个用户对应产品的控盘信息
*/
func (k *klineOffsetImmediate) GetSingleSymbolControlInfo(userID string, symbol string) (*model.SingleSymbolControlInfo, error) {
	symbolMap, ok := k.ControlInfos.Get(userID)
	if ok {
		controlInfo, have := symbolMap.Get(symbol)
		if have {
			return controlInfo, nil
		}
		return nil, fmt.Errorf("用户ID: %s, 产品: %s, 控盘信息不存在", userID, symbol)
	}
	return nil, fmt.Errorf("用户ID: %s, 控盘信息不存在", userID)
}

/*
获取上一分钟控盘K线收盘价格
*/
func (k *klineOffsetImmediate) GetPreviousMinuteControlKlineClosePrice(userID string, symbol string, currentTime string) (decimal.Decimal, error) {
	// 获取用户ID对应的偏移量控制数据
	symbolMap, ok := k.ControlInfos.Get(userID)
	if ok {
		// 确认当前数据的产品是否在偏移量控制列表中
		allTimeControlInfo, exist := symbolMap.Get(symbol)
		if exist {
			// 获取上一分钟开始时间戳
			prevMinuteStartTimestamp := cast.ToString(utils.GetPreviousMinuteStartTimestamp(cast.ToInt64(currentTime)))
			singleSymbolTimeIntervalControlInfo, err := allTimeControlInfo.GetTimeframeKline(prevMinuteStartTimestamp, "1m")
			if err != nil {
				return decimal.Decimal{}, fmt.Errorf("r3 用户ID: %s, 产品: %s, 偏移量控制数据不存在", userID, symbol)
			}
			return singleSymbolTimeIntervalControlInfo.Close, nil
		} else {
			return decimal.Decimal{}, fmt.Errorf("r2 用户ID: %s, 产品: %s, 偏移量控制数据不存在", userID, symbol)
		}
	} else {
		return decimal.Decimal{}, fmt.Errorf("r1 用户ID: %s, 偏移量控制数据不存在", userID)
	}
}

/*
获取这一分钟控盘K线的信息
*/
func (k *klineOffsetImmediate) GetCurrentMinuteControlKlineInfo(userID string, symbol string, currentTime string) (*model.SingleSymbolTimeframeControlKline, error) {
	// 获取用户ID对应的偏移量控制数据
	symbolMap, ok := k.ControlInfos.Get(userID)
	if ok {
		// 确认当前数据的产品是否在偏移量控制列表中
		allTimeControlInfo, exist := symbolMap.Get(symbol)
		if exist {
			singleSymbolTimeIntervalControlInfo, err := allTimeControlInfo.GetTimeframeKline(currentTime, "1m")
			if err != nil {
				return nil, fmt.Errorf("立即模式 GetCurrentMinuteControlKlineInfor 3 用户ID: %s, 产品: %s, 偏移量控制数据不存在", userID, symbol)
			}
			return singleSymbolTimeIntervalControlInfo, nil
		} else {
			return nil, fmt.Errorf("立即模式 GetCurrentMinuteControlKlineInfor r2 用户ID: %s, 产品: %s, 偏移量控制数据不存在", userID, symbol)
		}
	} else {
		return nil, fmt.Errorf("立即模式 GetCurrentMinuteControlKlineInfor r1 用户ID: %s, 偏移量(立即模式)制数据不存在", userID)
	}
}
