package service

import (
	"context"
	"errors"
	"fmt"
	cmap "github.com/orcaman/concurrent-map/v2"
	"github.com/shopspring/decimal"
	"github.com/sknun/cf/cast"
	"gorm.io/gorm"
	"sync"
	"time"
	koi_cache "trading_tick_server/internal/kline_offset_immediate/cache"
	"trading_tick_server/internal/kline_offset_immediate/dto"
	"trading_tick_server/internal/kline_offset_immediate/model"
	"trading_tick_server/internal/kline_offset_immediate/repository"
	"trading_tick_server/internal/user/use_iface"
	"trading_tick_server/lib/cache"
	"trading_tick_server/lib/mysql"
	"trading_tick_server/lib/symbol_price"
)

/*
1. 服务器启动时创建一个 KlineOffsetImmediate 实例
2. 去缓存查询是否有未完成的偏移量控制
*/

type klineOffsetImmediate struct {
	UserService  use_iface.UserService                                                                  // 注意 user 服务 和 User 模块不是一个概念
	OffsetInfo   cmap.ConcurrentMap[string, cmap.ConcurrentMap[string, decimal.Decimal]]                //[用户ID]map[产品]总偏移量(所有用户,所有产品),这个是控制信
	ControlInfos cmap.ConcurrentMap[string, cmap.ConcurrentMap[string, *model.SingleSymbolControlInfo]] //[用户ID]map[产品]控盘信息,这个是控制之后的数据
}

var (
	klineOffsetImmediateInstance *klineOffsetImmediate
	klineOffsetImmediateOnce     sync.Once
)

// 单例构造函数
func NewKlineOffsetImmediate(user use_iface.UserService) *klineOffsetImmediate {
	klineOffsetImmediateOnce.Do(func() {
		klineOffsetImmediateInstance = &klineOffsetImmediate{
			UserService:  user,
			OffsetInfo:   cmap.New[cmap.ConcurrentMap[string, decimal.Decimal]](),
			ControlInfos: cmap.New[cmap.ConcurrentMap[string, *model.SingleSymbolControlInfo]](),
		}

		ctx := context.Background()

		// 1. 从缓存中获取所有的偏移量控制(立即模式)数据
		err := koi_cache.GetAllKlineOffsetImmediateFromCache(ctx, klineOffsetImmediateInstance.OffsetInfo)
		if err != nil {
			fmt.Printf("❌从缓存初始化偏移量控制(立即模式)失败, err: %v\n", err)
		}

		// 2. 从数据库恢复未结束的控盘记录，确保数据一致性
		err = klineOffsetImmediateInstance.recoverUnfinishedControlFromDB(ctx)
		if err != nil {
			fmt.Printf("❌从数据库恢复未结束控盘记录失败, err: %v\n", err)
		}

		// 3. 打印所有的偏移量控制(立即模式)数据
		if klineOffsetImmediateInstance.OffsetInfo.Count() > 0 {
			fmt.Printf("🔧初始化偏移量控制(立即模式)模块\n")
			for userID, symbolMap := range klineOffsetImmediateInstance.OffsetInfo.Items() {
				fmt.Printf("用户ID : %s\n", userID)
				for symbol, offset := range symbolMap.Items() {
					fmt.Printf("  产品: %s, OffsetInfo: %s\n", symbol, offset.String())
				}
			}
			fmt.Printf("\n")
		}
	})

	return klineOffsetImmediateInstance
}

// validateOffsetAgainstCoinValue 检查偏移量是否超出币种价值
func (k *klineOffsetImmediate) validateOffsetAgainstCoinValue(req dto.PostKlineOffsetImmediateRequest) error {
	// 检查向下偏移量是否超出币种价值
	if req.Direction == "down" {
		offsetValue := req.Offset.InexactFloat64()
		// 获取当前币种价格
		currentPrice, err := k.getCurrentCoinPrice(req.UserID, req.Symbol)
		if err != nil {
			return fmt.Errorf("获取当前币种价格失败: %v", err)
		}
		if offsetValue >= currentPrice {
			return fmt.Errorf("偏移量过大，超出本币价值，请重新设置")
		}
	}

	return nil
}

// getCurrentCoinPrice 获取当前币种价格
func (k *klineOffsetImmediate) getCurrentCoinPrice(userID, symbol string) (float64, error) {
	// 1. 检查是否已有控盘，如果有则使用控盘后的当前价格
	if inner, exists := k.OffsetInfo.Get(userID); exists {
		if _, symbolExists := inner.Get(symbol); symbolExists {
			// 已有控盘，获取控盘后的当前价格
			controlPrice, err := k.getControlledCurrentPrice(userID, symbol)
			if err == nil {
				return controlPrice, nil
			}
			// 如果获取控盘价格失败，继续使用市场价格
		}
	}

	// 2. 无控盘或获取控盘价格失败，使用当前市场行情价
	marketPrice, err := k.getMarketCurrentPrice(symbol)
	if err != nil {
		return 0, fmt.Errorf("获取市场价格失败: %v", err)
	}

	return marketPrice, nil
}

// getControlledCurrentPrice 获取控盘后的当前价格
func (k *klineOffsetImmediate) getControlledCurrentPrice(userID, symbol string) (float64, error) {
	// 获取市场价格
	marketPrice := symbol_price.Get("alltick", symbol)
	if marketPrice <= 0 {
		return 0, fmt.Errorf("无法获取 %s 的市场价格", symbol)
	}

	// 获取当前的总偏移量
	userOffsetInfo, exists := k.OffsetInfo.Get(userID)
	if !exists {
		return marketPrice, nil // 没有偏移量，返回市场价格
	}

	totalOffset, exists := userOffsetInfo.Get(symbol)
	if !exists {
		return marketPrice, nil // 没有偏移量，返回市场价格
	}

	// 返回市场价格 + 偏移量
	controlledPrice := marketPrice + totalOffset.InexactFloat64()
	return controlledPrice, nil
}

// getMarketCurrentPrice 获取当前市场行情价
func (k *klineOffsetImmediate) getMarketCurrentPrice(symbol string) (float64, error) {
	// 使用 symbol_price 库获取最新市场价格
	currentPrice := symbol_price.Get("alltick", symbol)
	if currentPrice <= 0 {
		return 0, fmt.Errorf("无法获取 %s 的市场价格", symbol)
	}

	return currentPrice, nil
}

// recoverUnfinishedControlFromDB 从数据库恢复未结束的控盘记录
func (k *klineOffsetImmediate) recoverUnfinishedControlFromDB(ctx context.Context) error {
	// 查询数据库中未结束的控盘记录
	unfinishedRecords, err := repository.GetUnfinishedControlRecords(ctx)
	if err != nil {
		return fmt.Errorf("查询未结束控盘记录失败: %v", err)
	}

	if len(unfinishedRecords) == 0 {
		return nil
	}

	fmt.Printf("🔄 发现 %d 个未结束的控盘记录，开始恢复...\n", len(unfinishedRecords))

	for _, record := range unfinishedRecords {
		userID := cast.ToString(record.UserID)
		symbol := record.Symbol

		// 检查内存中是否已有该控盘信息
		if inner, exists := k.OffsetInfo.Get(userID); exists {
			if _, symbolExists := inner.Get(symbol); symbolExists {
				fmt.Printf("✅ 控盘记录已存在于内存中: UserID=%s, Symbol=%s\n", userID, symbol)
				continue
			}
		}

		// 从数据库恢复偏移量信息
		offsetRecord, err := repository.GetKlineOffsetImmediateByUserAndSymbol(ctx, record.UserID, symbol)
		if err != nil {
			fmt.Printf("❌ 获取偏移量记录失败: UserID=%s, Symbol=%s, err=%v\n", userID, symbol, err)
			continue
		}

		if offsetRecord == nil {
			fmt.Printf("⚠️  未找到对应的偏移量记录: UserID=%s, Symbol=%s\n", userID, symbol)
			continue
		}

		// 恢复到 Redis 缓存
		offsetImmediate := &model.KlineOffsetImmediateRedis{
			UserID:      userID,
			Symbol:      symbol,
			ControlTime: offsetRecord.ControlTime, // 使用数据库中的控制时间
			TotalOffset: offsetRecord.TotalOffset,
		}

		err = koi_cache.SetKlineOffsetImmediateToCache(ctx, record.UserID, symbol, *offsetImmediate, 0)
		if err != nil {
			fmt.Printf("❌ 恢复Redis缓存失败: UserID=%s, Symbol=%s, err=%v\n", userID, symbol, err)
			continue
		}

		// 恢复到内存
		inner, _ := k.OffsetInfo.Get(userID)
		inner.Set(symbol, offsetRecord.TotalOffset)
		k.OffsetInfo.Set(userID, inner)

		fmt.Printf("✅ 成功恢复控盘记录: UserID=%s, Symbol=%s, Offset=%s\n",
			userID, symbol, offsetRecord.TotalOffset.String())
	}
	return nil
}

/*
 StartControl 启动立即模式控盘流程，
参数校验
事务控制
缓存设置
数据库写入
内存信息更新
控盘执行
*/

func (k *klineOffsetImmediate) StartControl(ctx context.Context, req dto.PostKlineOffsetImmediateRequest) error {
	// 1. 参数校验
	if err := k.PostValidate(ctx, req); err != nil {
		return err
	}

	// 检查偏移量是否超出币种价值
	if err := k.validateOffsetAgainstCoinValue(req); err != nil {
		return err
	}

	// 2. 开启事务
	tx := mysql.M.Begin()
	committed := false
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
		if !committed {
			tx.Rollback()
		}
	}()

	/*
		3. 控盘状态检查（含事务锁）
		⚠️注意: 注意参数 isCanControl 和 isInControl 的区别
		isCanControl: 是否可以控盘,是否可以进行这次操作
		isInControl: 是否在控盘中
	*/
	isCanControl, isInControl, err := k.IsCanIsInControl(req.UserID, req.Symbol, tx)
	if err != nil || !isCanControl {
		return err
	}

	// 4. 设置缓存并计算偏移量
	offsetImmediate, err := k.setupOffsetImmediateCache(ctx, req)
	if err != nil {
		return err
	}

	// 5. 持久化数据库记录（控盘记录和偏移记录）
	err = k.saveControlData(ctx, req, offsetImmediate, isInControl, tx)
	if err != nil {
		return err
	}

	// 6. 内存中的控制信息更新或初始化,注意参数为是否控盘中
	err = k.updateControlInfoMemory(req, offsetImmediate.TotalOffset, isInControl)
	if err != nil {
		return err
	}

	// 7. 启动控盘逻辑
	err = k.DataControl(req.UserID, req.Symbol)
	if err != nil {
		return fmt.Errorf("❌偏移量(立即模式) Start Control 失败, err: %v", err)
	}

	// 8. 提交事务
	committed = true
	return tx.Commit().Error
}

// 停止执行K线偏移量控制
func (k *klineOffsetImmediate) StopControl(ctx context.Context, req dto.DelKlineOffsetImmediateRequest) error {
	// 1. 参数校验
	if err := k.StopValidate(ctx, req); err != nil {
		return err
	}
	/*
		尝试获取缓存中的控制信息
	*/
	offsetCache, err := koi_cache.GetKlineOffsetImmediateFromCache(ctx, req.UserID, req.Symbol)
	if err != nil {
		return fmt.Errorf("❌获取偏移量控制(立即模式)缓存记录失败,err : %v", err)
	} else if offsetCache.UserID == "" {
		return fmt.Errorf("❌控盘正在停止中 或 没有偏移量控制")
	}

	/*
		删除缓存记录
	*/
	err = koi_cache.DeleteKlineOffsetImmediateFromCache(context.Background(), req.UserID, req.Symbol)
	if err != nil {
		fmt.Printf("❌删除偏移量控制(立即模式)缓存记录失败, err : %v", err)
	}

	m := &model.KlineOffsetImmediateDB{
		UserID:       req.UserID,
		Symbol:       req.Symbol,
		Offset:       decimal.Zero,
		Direction:    "over", // 停止
		TotalOffset:  decimal.Zero,
		ControlTime:  time.Now().Unix(),
		ControlPrice: decimal.Zero,
		IsOffset:     true,
	}
	err = repository.SaveKlineOffsetImmediate(ctx, mysql.M, m)
	if err != nil {
		return fmt.Errorf("❌停止偏移, 保存偏移量控制(立即模式)记录失败, err : %v", err)
	}

	// ================================== 停止逻辑 ================================
	/*
		1. 记录数据,产品停止控盘时间
	*/
	controlInfo, err := k.GetSingleSymbolControlInfo(req.UserID, req.Symbol)
	if err != nil {
		return fmt.Errorf("❌获取偏移量控制(立即模式)控盘信息失败, err : %v", err)
	}
	controlInfo.EndTime = cast.ToString(time.Now().Unix() + 60) // 立即模式加60 是为了防止结束时没有走获取上一根k线收盘价作为开盘价的流程,因为点击结束时,有可能这一分钟之后都没有数据来,
	err = k.OverControl(req.UserID, req.Symbol)
	if err != nil {
		return fmt.Errorf("❌偏移量(立即模式) Over Control 失败, err: %v", err)
	}
	fmt.Println("🐶停止偏移量(立即模式)控盘,用户ID: ", req.UserID, "产品: ", req.Symbol)
	//service.GetStationRoom[[]structs.KlineRetData](req.UserID).DisableModification()

	return nil
}

// cleanupMemoryInfo 清理内存中的控盘信息
func (k *klineOffsetImmediate) cleanupMemoryInfo(userID, symbol string) {
	// 清理 OffsetInfo
	if inner, exists := k.OffsetInfo.Get(userID); exists {
		inner.Remove(symbol)
		if inner.Count() == 0 {
			k.OffsetInfo.Remove(userID)
		}
	}

	// 清理 ControlInfos
	if inner, exists := k.ControlInfos.Get(userID); exists {
		inner.Remove(symbol)
		if inner.Count() == 0 {
			k.ControlInfos.Remove(userID)
		}
	}

	fmt.Printf("✅ 已清理内存控盘信息: UserID=%s, Symbol=%s\n", userID, symbol)
}

// 获取偏移量控制(立即模式)列表
func (k *klineOffsetImmediate) GetControlList(ctx context.Context, req dto.GetKlineOffsetImmediateListRequest) ([]model.KlineOffsetImmediateRedis, error) {
	// 1. 参数校验
	if err := k.GetValidate(ctx, req); err != nil {
		return nil, err
	}
	offsetList := make([]model.KlineOffsetImmediateRedis, 0)

	// 填充 k.OffsetInfo（线程安全 map）从 Redis 中
	err := koi_cache.GetAllKlineOffsetImmediateFromCache(ctx, k.OffsetInfo)
	if err != nil {
		return nil, fmt.Errorf("❌获取偏移量控制(立即模式)列表失败, err : %v", err)
	}

	// 遍历外层：userID -> innerMap[symbol]decimal
	for userID, innerMap := range k.OffsetInfo.Items() {
		if userID == req.UserID {
			for symbol, offset := range innerMap.Items() {
				offsetList = append(offsetList, model.KlineOffsetImmediateRedis{
					UserID:      userID,
					Symbol:      symbol,
					TotalOffset: offset,
				})
			}
			break
		}
	}

	// 打印一下所有信息
	for userID, symbolMap := range klineOffsetImmediateInstance.OffsetInfo.Items() {
		fmt.Printf("UserID: %s\n", userID)
		for symbol, offset := range symbolMap.Items() {
			fmt.Printf("ymbol: %s, OffsetInfo: %s\n", symbol, offset.String())
		}
	}

	return offsetList, nil
}

/*
获取偏移量控制(立即模式)历史记录
*/
func (k *klineOffsetImmediate) GetControlHistory(ctx context.Context, req dto.GetKlineOffsetImmediateHistoryRequest) ([]model.KlineOffsetImmediateDB, error) {
	/*
		从数据库获取历史
	*/
	var record []model.KlineOffsetImmediateDB
	err := mysql.M.WithContext(ctx).
		Where("user_id = ? AND symbol = ?", req.UserID, req.Symbol).
		Order("id DESC").
		Limit(20).
		Take(&record).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // 没找到记录是正常情况
		}
		return nil, fmt.Errorf("查询用户[%s]产品[%s]的线性控盘信息失败: %w", req.UserID, req.Symbol, err)
	}
	return record, nil
}

/*
	恢复(服务器启动时调用,现在暂时先在启动时删除没有完成的控制,等空了再来做真正的启动恢复)

出错直接 panic
*/
func OffsetImmediateRecover() {
	ctx := context.Background()
	// 1. 尝试获取缓存中的控制信息
	offsetImmediateCache, err := koi_cache.GetAllKlineOffsetImmediate(ctx)
	if err != nil {
		panic(fmt.Errorf("❌获取偏移量控制(立即模式)缓存记录失败,err : %v\n", err))
	}
	// 2. 遍历所有的偏移量控制(立即模式)数据,依次删除
	for _, offset := range offsetImmediateCache {
		// 删除缓存
		err = koi_cache.DeleteKlineOffsetImmediateFromCache(ctx, offset.UserID, offset.Symbol)
		if err != nil {
			panic(fmt.Errorf("❌删除偏移量控制(立即模式)缓存记录失败, err : %v", err))
		}
		m := &model.KlineOffsetImmediateDB{
			UserID:       offset.UserID,
			Symbol:       offset.Symbol,
			Offset:       decimal.Zero,
			Direction:    "over", // 停止
			TotalOffset:  decimal.Zero,
			ControlTime:  time.Now().Unix(),
			ControlPrice: decimal.Zero,
			IsOffset:     true,
		}

		err = repository.SaveKlineOffsetImmediate(ctx, mysql.M, m)
		if err != nil {
			panic(fmt.Errorf("❌恢复偏移量控制(立即模式)数据失败, err : %v", err))
		}
	}
	return
}

/*
===========================================================================================
*/
// Post参数校验
func (k *klineOffsetImmediate) PostValidate(ctx context.Context, req dto.PostKlineOffsetImmediateRequest) error {
	// 1. 用户以及token
	token, err := k.UserService.GetToken(ctx, cast.ToInt64(req.UserID))
	if err != nil {
		return err
	}

	if token != req.Token {
		return errors.New("token 错误")
	}
	/*
		方向是否正确
	*/

	// 3. 产品是否存在
	var existSymbol bool
	// 查询哪个平台有当前请求的这个产品的数据
	/*	for _, v := range cache.CacheTickSliceGet() {
		fmt.Printf("🤔这个ChacheTickSliceGet 里面是啥?%v\n", v)
	}*/

	for _, v := range cache.CacheTickSliceGet() {
		if v.Symbol == req.Symbol {
			existSymbol = true
			break
		}
	}
	if !existSymbol {
		return errors.New("产品不存在")
	}

	return nil
}

// Stop参数校验
func (k *klineOffsetImmediate) StopValidate(ctx context.Context, req dto.DelKlineOffsetImmediateRequest) error {
	// 1. 用户以及token
	token, err := k.UserService.GetToken(ctx, cast.ToInt64(req.UserID))
	if err != nil {
		return err
	}

	if token != req.Token {
		return errors.New("token 错误")
	}

	// 2. 产品是否存在
	var existSymbol bool
	// 查询哪个平台有当前请求的这个产品的数据
	/*	for _, v := range cache.CacheTickSliceGet() {
		fmt.Printf("🤔这个ChacheTickSliceGet 里面是啥?%v\n", v)
	}*/

	for _, v := range cache.CacheTickSliceGet() {
		if v.Symbol == req.Symbol {
			existSymbol = true
			break
		}
	}
	if !existSymbol {
		return errors.New("产品不存在")
	}

	return nil
}

// Get 参数校验
func (k *klineOffsetImmediate) GetValidate(ctx context.Context, req dto.GetKlineOffsetImmediateListRequest) error {
	// 1. 用户以及token
	token, err := k.UserService.GetToken(ctx, cast.ToInt64(req.UserID))
	if err != nil {
		return err
	}

	if token != req.Token {
		return errors.New("token 错误")
	}
	return nil
}
