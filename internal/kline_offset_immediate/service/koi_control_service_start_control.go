package service

import (
	"context"
	"fmt"
	"time"
	kcc_model "trading_tick_server/internal/kline_control_center/model"
	kcc_service "trading_tick_server/internal/kline_control_center/service"
	kcc_types "trading_tick_server/internal/kline_control_center/types"
	koi_cache "trading_tick_server/internal/kline_offset_immediate/cache"
	"trading_tick_server/internal/kline_offset_immediate/dto"
	"trading_tick_server/internal/kline_offset_immediate/model"
	"trading_tick_server/internal/kline_offset_immediate/repository"

	cmap "github.com/orcaman/concurrent-map/v2"
	"github.com/shopspring/decimal"
	"github.com/sknun/cf/cast"
	"gorm.io/gorm"
)

/*
IsCanIsInControl 检查这次操作是否可以进行控制
true 代表可以进行控制或调整
*/
func (k *klineOffsetImmediate) IsCanIsInControl(userID string, symbol string, tx *gorm.DB) (bool, bool, error) {
	record, err := kcc_service.GetControlRecordWithLock(userID, symbol, tx)
	if err != nil {
		return false, false, fmt.Errorf("❌获取控盘记录失败: %v", err)
	}
	if record == nil {
		/*
			没有控盘记录, 说明没有控盘,返回可以进行控盘
		*/
		return true, false, nil
	}
	if record.ControlMode != string(kcc_types.ENUM_KLINE_CONTROL_TYPE_IMMEDIATE) {
		if record.ControlMode == string(kcc_types.ENUM_KLINE_CONTROL_TYPE_GRADIENT) {
			return false, false, fmt.Errorf("❌%s 正在渐变控盘，请在控盘结束后重试。", symbol)
		} else if record.ControlMode == string(kcc_types.ENUM_KLINE_CONTROL_TYPE_LINEAR) {
			return false, false, fmt.Errorf("❌%s 正在线形控盘，请在控盘结束后重试。", symbol)
		} else {
			return false, false, fmt.Errorf("❌%s 正在其他类型控盘，请在控盘结束后重试。", symbol)
		}
	}
	/*	if record.IsInFall == true {
		return false, false, fmt.Errorf("❌产品 %s 在回落期,请等待回落期结束", symbol)
	}*/
	return true, true, nil
}

// setupOffsetImmediateCache 设置并写入偏移量缓存，返回缓存结构体（含总偏移量）
func (k *klineOffsetImmediate) setupOffsetImmediateCache(ctx context.Context, req dto.PostKlineOffsetImmediateRequest) (*model.KlineOffsetImmediateRedis, error) {
	offset, err := koi_cache.KlineOffsetCacheSet(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("❌设置缓存失败: %v", err)
	}
	return offset, nil
}

// saveControlData 写入偏移控制记录和控盘记录到数据库（事务内操作）
func (k *klineOffsetImmediate) saveControlData(ctx context.Context, req dto.PostKlineOffsetImmediateRequest, offset *model.KlineOffsetImmediateRedis, isInControl bool, tx *gorm.DB) error {
	// 插入偏移控制记录
	dbRecord := &model.KlineOffsetImmediateDB{
		UserID:      req.UserID,
		Symbol:      req.Symbol,
		Offset:      req.Offset,
		Direction:   req.Direction,
		TotalOffset: offset.TotalOffset,
		ControlTime: time.Now().Unix(),
		IsOffset:    isInControl,
	}
	if err := repository.SaveKlineOffsetImmediate(ctx, tx, dbRecord); err != nil {
		_ = koi_cache.DeleteKlineOffsetImmediateFromCache(ctx, req.UserID, req.Symbol)
		return fmt.Errorf("❌保存控盘记录失败: %v", err)
	}

	// 如果不是已控盘状态，需要插入新的控盘记录
	if !isInControl {
		dbControl := &kcc_model.KlineControlRecordDB{
			UserID:      req.UserID,
			Symbol:      req.Symbol,
			ControlMode: string(kcc_types.ENUM_KLINE_CONTROL_TYPE_IMMEDIATE),
			StartTime:   time.Now().Unix(),
			EndTime:     0,
			IsEnd:       false,
		}
		if err := kcc_service.CreateControlSafelyFromStruct(dbControl, tx); err != nil {
			return fmt.Errorf("❌创建控盘记录失败: %v", err)
		}
	}

	return nil
}

// updateControlInfoMemory 更新或初始化内存中的控盘控制结构（用户 -> 产品 -> 控盘信息）
func (k *klineOffsetImmediate) updateControlInfoMemory(req dto.PostKlineOffsetImmediateRequest, totalOffset decimal.Decimal, isInControl bool) error {
	if !isInControl {
		// 初始化或重置控盘信息
		symbolMap, ok := k.ControlInfos.Get(req.UserID)
		if !ok {
			symbolMap = cmap.New[*model.SingleSymbolControlInfo]()
			k.ControlInfos.Set(req.UserID, symbolMap)
		}

		if info, exists := symbolMap.Get(req.Symbol); exists {
			// 更新字段（共通逻辑）
			info.StartTime = cast.ToString(time.Now().Unix())
			info.TotalOffset = totalOffset
			info.AsksPriceMin = totalOffset

			// 若允许清空历史，则清空
			if info.IsCanResetHistory {
				info.ControlKlineHistory = cmap.New[cmap.ConcurrentMap[string, *model.SingleSymbolTimeframeControlKline]]()
				info.IsCanResetHistory = false
			}
		} else {
			// 初始化控盘信息
			symbolMap.Set(req.Symbol, &model.SingleSymbolControlInfo{
				StartTime:           cast.ToString(time.Now().Unix()),
				TotalOffset:         totalOffset,
				AsksPriceMin:        req.AsksPriceMin,     // 买跌浮动最小值
				AsksPriceMax:        req.AsksPriceMax,     // 买涨浮动最大值
				OrderBookDiffMin:    req.OrderBookDiffMin, // 盘口价差最小值
				OrderBookDiffMax:    req.OrderBookDiffMax, // 盘口价差最大值
				IsCanResetHistory:   false,
				ControlKlineHistory: cmap.New[cmap.ConcurrentMap[string, *model.SingleSymbolTimeframeControlKline]](),
				Direction:           req.Direction,
			})
		}

		// 初始化偏移量信息
		userOffsetInfo, ok := k.OffsetInfo.Get(req.UserID)
		if !ok {
			userOffsetInfo = cmap.New[decimal.Decimal]()
			userOffsetInfo.Set(req.Symbol, totalOffset)
			k.OffsetInfo.Set(req.UserID, userOffsetInfo)
		} else {
			userOffsetInfo.Set(req.Symbol, totalOffset)
		}
	} else {
		err := koi_cache.GetAllKlineOffsetImmediateFromCache(context.Background(), k.OffsetInfo)
		if err != nil {
			return fmt.Errorf("❌ 偏移量(立即模式),Start Control 刷新缓存失败, 没有在缓存中,找到控盘信息, err : %v", err)
		}
		// 控盘中：更新控盘信息结构体
		symbolMap, ok := k.ControlInfos.Get(req.UserID)
		if !ok {
			// 内存中没有控盘信息，重新初始化（容错处理）
			fmt.Printf("⚠️  内存中缺少控盘信息，重新初始化: UserID=%s, Symbol=%s\n", req.UserID, req.Symbol)
			symbolMap = cmap.New[*model.SingleSymbolControlInfo]()
			k.ControlInfos.Set(req.UserID, symbolMap)
		}

		controlInfo, have := symbolMap.Get(req.Symbol)
		if !have {
			// 产品控盘信息不存在，重新创建（容错处理）
			fmt.Printf("⚠️  产品控盘信息不存在，重新创建: UserID=%s, Symbol=%s\n", req.UserID, req.Symbol)
			controlInfo = &model.SingleSymbolControlInfo{
				StartTime:           cast.ToString(time.Now().Unix()),
				EndTime:             "", // 立即模式手动结束，初始为空
				TotalOffset:         totalOffset,
				AsksPriceMin:        req.AsksPriceMin,
				AsksPriceMax:        req.AsksPriceMax,
				OrderBookDiffMin:    req.OrderBookDiffMin,
				OrderBookDiffMax:    req.OrderBookDiffMax,
				IsCanResetHistory:   false,
				ControlKlineHistory: cmap.New[cmap.ConcurrentMap[string, *model.SingleSymbolTimeframeControlKline]](),
				Direction:           req.Direction,
			}
			symbolMap.Set(req.Symbol, controlInfo)
		} else {
			// 更新现有控盘信息
			controlInfo.TotalOffset = totalOffset
			controlInfo.AsksPriceMin = req.AsksPriceMin // 买跌浮动最小值
			controlInfo.AsksPriceMax = req.AsksPriceMax // 买涨浮动最大值
			controlInfo.OrderBookDiffMin = req.OrderBookDiffMin
			controlInfo.OrderBookDiffMax = req.OrderBookDiffMax
		}
	}
	return nil
}
