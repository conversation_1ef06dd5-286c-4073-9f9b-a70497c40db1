package model

import (
	"fmt"
	cmap "github.com/orcaman/concurrent-map/v2"
	"github.com/shopspring/decimal"
)

/*
单个产品的控制信息
*/
type SingleSymbolControlInfo struct {
	StartTime           string                                                                                     `json:"start_time"`            // 控制开始时间(秒)
	EndTime             string                                                                                     `json:"end_time"`              // 控制结束时间(秒)
	TotalOffset         decimal.Decimal                                                                            `json:"total_offset"`          // 总偏移量
	AsksPriceMin        decimal.Decimal                                                                            `json:"asks_price_min"`        // 买跌浮动最小值
	AsksPriceMax        decimal.Decimal                                                                            `json:"asks_price_max"`        // 买涨浮动最大值
	OrderBookDiffMin    decimal.Decimal                                                                            `json:"order_book_diff_min"`   // 盘口价差最小值
	OrderBookDiffMax    decimal.Decimal                                                                            `json:"order_book_diff_max"`   // 盘口价差最大值
	IsCanResetHistory   bool                                                                                       `json:"is_can_reset_history"`  // 是否可以重置,由于结束控盘后,在当前的这个根K线内还需要历史数据,所以需要这个标志来判断是否是真的全部控盘结束后,可以重置
	ControlKlineHistory cmap.ConcurrentMap[string, cmap.ConcurrentMap[string, *SingleSymbolTimeframeControlKline]] `json:"control_kline_history"` // 这一次控盘期间所有的控盘K线信息(控制后的数据) map[时间]map[k线颗粒]控盘k线信息
	Direction           string                                                                                     `json:"direction"`             // 偏移方向
}

/*
获取一个用户对应产品的控盘信息中的控盘K线信息(对齐时间间隔)2
*/
func (k *SingleSymbolControlInfo) GetTimeframeKline(klineTime string, timeframe string) (*SingleSymbolTimeframeControlKline, error) {
	intervalKline, ok := k.ControlKlineHistory.Get(klineTime)
	if ok {
		kline, ok := intervalKline.Get(timeframe)
		if ok {
			return kline, nil
		} else {
			return nil, fmt.Errorf("❌获取控盘K线信息失败,没有这个时间颗粒的控盘K线信息, klineTime: %s, timeframe: %s", klineTime, timeframe)
		}
	} else {
		return nil, fmt.Errorf("❌获取控盘K线信息失败,没有这个时间的控盘K线信息, klineTime: %s", klineTime)
	}
}
