package model

import (
	"github.com/shopspring/decimal"
	"trading_tick_server/common/gmysql"
)

/*
数据库模型
*/

/*
偏移量控制(立即模式)数据库结构
*/
type KlineOffsetImmediateDB struct {
	gmysql.GormModel
	// 用户ID
	UserID string `gorm:"column:user_id;type:int(11);not null;default:0;comment:用户ID" json:"user_id"`
	// 产品
	Symbol string `gorm:"column:symbol;type:varchar(32);not null;default:'';comment:产品" json:"symbol"`
	// 偏移量(当前这次操作的偏移量)
	Offset decimal.Decimal `gorm:"column:offset;type:decimal(20,8);not null;default:0;comment:偏移量(当前这次操作的偏移量)" json:"offset"`
	// 偏移方向 1: up(涨) 2: down(跌), 3: over (结束)
	Direction string `gorm:"column:direction;type:varchar(10);not null;default:'';comment:偏移方向 1: up(涨) 2: down(跌), 3: over (结束)" json:"direction"`
	// 总偏移量(累计值)
	TotalOffset decimal.Decimal `gorm:"column:total_offset;type:decimal(20,8);not null;default:0;comment:总偏移量" json:"total_offset"`
	// 控制时间(这次偏移操作的时间)秒级
	ControlTime int64 `gorm:"column:control_time;type:int(11);not null;default:0;comment:控制时间" json:"control_time"`
	// 控盘价格(这次偏移操作时的价格, 这个价格可能是真实价格, 也可能是偏移后的价格,(还在偏移期间))
	ControlPrice decimal.Decimal `gorm:"column:control_price;type:decimal(20,8);not null;default:0;comment:控制价格" json:"control_price"`
	// 是否偏移期间,这一次偏移操作是否在偏移期间内(如果不是立即模式,在结束后的会落期一样也算偏移期间, 线性模式需要加一个是否回落期的字段)
	IsOffset bool `gorm:"column:is_offset;type:tinyint(1);not null;default:0;comment:是否偏移期间" json:"is_offset"`
}

// 表名
func (KlineOffsetImmediateDB) TableName() string {
	return "kline_offset_immediate"
}

// KlineOffsetImmediateRedis Redis模型
type KlineOffsetImmediateRedis struct {
	// 用户ID
	UserID string `gorm:"column:user_id;type:int(11);not null;default:0;comment:用户ID" json:"user_id"`
	// 产品
	Symbol string `gorm:"column:symbol;type:varchar(32);not null;default:'';comment:产品" json:"symbol"`
	// 控制时间(这次偏移操作的时间)秒级
	ControlTime int64 `gorm:"column:control_time;type:int(11);not null;default:0;comment:控制时间" json:"control_time"`
	// 总偏移量(累计值)
	TotalOffset decimal.Decimal `gorm:"column:total_offset;type:decimal(20,8);not null;default:0;comment:总偏移量" json:"total_offset"`
}
