package config

import (
	"fmt"
	"os"
	"trading_tick_server/lib/global"

	"gopkg.in/yaml.v3"
)

func Init() {
	// 读取 YAML 文件
	data, err := os.ReadFile(global.Path + "/storage/conf/conf.yaml")
	if err != nil {
		panic(fmt.Errorf("reading yaml config file failed: %w", err))
	}

	// 将 YAML 数据解析到结构体中
	err = yaml.Unmarshal(data, &Data)
	if err != nil {
		panic(fmt.Errorf("yaml.Unmarshal failed: %w", err))
	}
}
