package rx_observer

import (
	"fmt"
	"trading_tick_server/common/rx_observer_pattern/rx_observable"
)

// 初始化时注册
func Init() {
	/*
		偏移量(立即模式)数据变化事件的Observable
	*/
	rx_observable.KlineOffsetImmediateObservable.ForEach(
		func(item interface{}) { // NextFunc: 处理每个事件
			fmt.Println("🫡偏移量(立即模式)数据变化事件")
		},
		func(err error) { // ErrFunc: 处理错误
			fmt.Printf("❌偏移量(立即模式)数据变化事件, err : %v\n", err)
		},
		func() { // CompletedFunc: 当 Observable 完成时调用
			fmt.Println("🎆偏移量(立即模式)数据变化事件完成")
		},
	)
}
