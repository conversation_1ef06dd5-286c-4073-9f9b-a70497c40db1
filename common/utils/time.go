package utils

import (
	"fmt"
	"time"

	"github.com/sknun/cf/cast"
)

// GetStartOfCurrentMinuteTimestamp 返回当前这一分钟开始的秒级时间戳
func GetStartOfCurrentMinuteTimestampUnix() int64 {
	now := time.Now()
	startOfMinute := time.Date(
		now.Year(),
		now.Month(),
		now.Day(),
		now.Hour(),
		now.Minute(),
		0, // 秒设为0
		0, // 纳秒设为0
		now.Location(),
	)
	return startOfMinute.Unix()
}

func GetStartOfCurrentMinuteTimestampUnixToString() string {
	now := time.Now()
	startOfMinute := time.Date(
		now.Year(),
		now.Month(),
		now.Day(),
		now.Hour(),
		now.Minute(),
		0, // 秒设为0
		0, // 纳秒设为0
		now.Location(),
	)
	return cast.ToString(startOfMinute.Unix())
}

// 获取上一分钟开始的秒级时间戳
func GetStartOfLastMinuteTimestampUnix() int64 {
	now := time.Now()
	startOfMinute := time.Date(
		now.Year(),
		now.Month(),
		now.Day(),
		now.Hour(),
		now.Minute()-1,
		0, // 秒设为0
		0, // 纳秒设为0
		now.Location(),
	)
	return startOfMinute.Unix()
}

// 获取上一分钟开始的秒级时间戳
func GetStartOfLastMinuteTimestampUnixToString() string {
	now := time.Now()
	startOfMinute := time.Date(
		now.Year(),
		now.Month(),
		now.Day(),
		now.Hour(),
		now.Minute()-1,
		0, // 秒设为0
		0, // 纳秒设为0
		now.Location(),
	)
	return cast.ToString(startOfMinute.Unix())
}

// GetMinuteStartTimestamp 返回该时间所在分钟的起始时间戳（单位：秒）
func GetMinuteStartTimestamp(ts int64) int64 {
	t := time.Unix(ts, 0)                    // 秒级时间戳转为 time.Time
	startOfMinute := t.Truncate(time.Minute) // 截断到整分钟
	return startOfMinute.Unix()              // 转回秒级时间戳
}

// GetPreviousMinuteStartTimestamp 返回上一分钟的起始时间戳（单位：秒）
func GetPreviousMinuteStartTimestamp(timestamp int64) int64 {
	// 转为 time.Time
	t := time.Unix(timestamp, 0)

	// 减去 1 分钟，然后设置秒和纳秒为 0，得到上一分钟的起始点
	prevMinute := t.Add(-1 * time.Minute).Truncate(time.Minute)

	// 返回秒级时间戳
	return prevMinute.Unix()
}

// GetControlEndTimeUnix 计算控盘结束时间
func GetControlEndTimeUnix(stopTimeUnix, endDuration int64) int64 {
	// 正常的回落期结束时间
	normalEndTime := stopTimeUnix + endDuration

	// 获取回落期结束时的分钟开始时间
	endMinuteStart := GetMinuteStartTimestamp(normalEndTime)

	fmt.Printf("🕐 GetControlEndTimeUnix计算: stopTimeUnix=%d, endDuration=%d, normalEndTime=%d, endMinuteStart=%d\n",
		stopTimeUnix, endDuration, normalEndTime, endMinuteStart)

	// 如果回落期在分钟中间结束，延长到下一分钟开始，确保不被K线边界截断
	if normalEndTime > endMinuteStart {
		nextMinuteStart := endMinuteStart + 60
		fmt.Printf("🕐 回落期在分钟中间结束，延长到下一分钟开始: %d -> %d (延长了%d秒)\n",
			normalEndTime, nextMinuteStart, nextMinuteStart-normalEndTime)
		return nextMinuteStart
	}

	fmt.Printf("🕐 回落期正好在分钟边界结束: %d\n", normalEndTime)
	return normalEndTime
}

// GetIntervalStartTimestamp 返回该时间所在K线周期的起始时间戳
func GetIntervalStartTimestamp(ts int64, interval string) int64 {
	t := time.Unix(ts, 0) // 秒级时间戳转为 time.Time
	var duration time.Duration
	switch interval {
	case "1m":
		duration = time.Minute
		return t.Truncate(duration).Unix()
	case "5m":
		duration = 5 * time.Minute
		return t.Truncate(duration).Unix()
	case "15m":
		duration = 15 * time.Minute
		return t.Truncate(duration).Unix()
	case "30m":
		duration = 30 * time.Minute
		return t.Truncate(duration).Unix()
	case "1h":
		duration = time.Hour
		return t.Truncate(duration).Unix()
	case "4h":
		duration = 4 * time.Hour
		return t.Truncate(duration).Unix()
	case "1d":
		duration = 24 * time.Hour
		return t.Truncate(duration).Unix()
	case "W":
		weekday := int(t.Weekday())
		if weekday == 0 {
			weekday = 7
		}
		monday := t.AddDate(0, 0, -weekday+1)
		weekStart := time.Date(monday.Year(), monday.Month(), monday.Day(), 0, 0, 0, 0, t.Location())
		return weekStart.Unix()
	case "M":
		monthStart := time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, t.Location())
		return monthStart.Unix()
	default:
		duration = time.Minute
		return t.Truncate(duration).Unix()
	}
}
