package gmysql

import (
	"context"
	"fmt"
	"gorm.io/gorm"
)

func QueryPage[T any](
	ctx context.Context,
	db *gorm.DB,   // 数据库连接
	pageNum int,   // 第几页
	pageSize int,  // 每页多少条
	condition any, // 查询条件，例如 map[string]any 或 struct
	order string,  // 排序字段，如 "id DESC"
) (*PageResult[T], error) {
	if pageNum < 1 {
		pageNum = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	offset := (pageNum - 1) * pageSize

	var list []T
	var total int64

	// 查询总数
	if err := db.WithContext(ctx).
		Model(new(T)).
		Where(condition).
		Count(&total).Error; err != nil {
		return nil, fmt.Errorf("查询总数失败: %w", err)
	}

	// 查询分页数据
	err := db.WithContext(ctx).
		Where(condition).
		Order(order).
		Limit(pageSize).
		Offset(offset).
		Find(&list).Error

	if err != nil {
		return nil, fmt.Errorf("分页查询失败: %w", err)
	}

	totalPage := int((total + int64(pageSize) - 1) / int64(pageSize)) // 向上取整

	return &PageResult[T]{
		Total:     total,
		PageNum:   pageNum,
		PageSize:  pageSize,
		TotalPage: totalPage,
		List:      list,
	}, nil
}

type PageResult[T any] struct {
	Total     int64 `json:"total"`
	PageNum   int   `json:"page_num"`
	PageSize  int   `json:"page_size"`
	TotalPage int   `json:"total_page"`
	List      []T   `json:"list"`
}
