package gmysql

import (
	"database/sql/driver"
	"fmt"
	"gorm.io/gorm"
	"time"
)

type UTCDateTime struct {
	time.Time
}

func (t *UTCDateTime) Scan(value interface{}) error {
	if value == nil {
		*t = UTCDateTime{Time: time.Time{}}
		return nil
	}
	switch v := value.(type) {
	case time.Time:
		*t = UTCDateTime{Time: v.UTC()}
	default:
		return fmt.Errorf("unsupported type: %T", v)
	}
	return nil
}

func (t UTCDateTime) Value() (driver.Value, error) {
	if t.<PERSON>() {
		return nil, nil
	}
	return t.UTC(), nil
}

func (t UTCDateTime) MarshalJSON() ([]byte, error) {
	if t.<PERSON>() {
		return []byte(`null`), nil
	}
	return []byte(`"` + t.UTC().Format(time.RFC3339) + `"`), nil
}

type GormModel struct {
	ID        uint           `gorm:"primarykey" mapstructure:"id" json:"id"`                     // 主键ID
	CreatedAt UTCDateTime    `gorm:"autoCreateTime" mapstructure:"created_at" json:"created_at"` // 自动创建时间
	UpdatedAt UTCDateTime    `gorm:"autoUpdateTime" mapstructure:"updated_at" json:"updated_at"` // 自动更新时间
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`                                             // 删除时间
}
